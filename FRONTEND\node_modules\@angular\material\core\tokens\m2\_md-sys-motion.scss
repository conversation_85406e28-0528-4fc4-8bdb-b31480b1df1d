//
// Design system display name: Material 3
// Design system version: v0.161
//

@function md-sys-motion-values() {
  @return (
    // TBD
    duration-extra-long1: null,
    duration-extra-long2: null,
    duration-extra-long3: null,
    duration-extra-long4: null,
    duration-long1: null,
    duration-long2: null,
    duration-long3: null,
    duration-long4: null,
    duration-medium1: null,
    duration-medium2: null,
    duration-medium3: null,
    duration-medium4: null,
    duration-short1: null,
    duration-short2: null,
    duration-short3: null,
    duration-short4: null,
    easing-emphasized: null,
    easing-emphasized-accelerate: null,
    easing-emphasized-decelerate: null,
    easing-legacy: null,
    easing-legacy-accelerate: null,
    easing-legacy-decelerate: null,
    easing-linear: null,
    easing-standard: null,
    easing-standard-accelerate: null,
    easing-standard-decelerate: null,
  );
}
