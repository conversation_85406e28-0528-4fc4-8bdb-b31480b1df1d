# @tufjs/canonical-json

JSON canonicalization compliant with the [OLPC Canonical JSON specification][1].

## Why

If you're looking for [RFC 8785][2] compliant JSON canonicalization there are
[any][3] [number][4] [of][5] [libraries][6] [to][7] [choose][8] [from][9].
You should only select this library if you know that you specifically need
support for the [OLPC][1]-style of canonicalization.

One reason you might chose OLPC compliance is for interoperability with
[The Update Framework][10] which specifically calls out OLPC as the
canonicalization standard for computing signatures over TUF metadata.

The canonicalized strings generated by this library are compatible with those
generated by the Python-based [securesystemslib][11] library and the Go-based
[go-securesystemslib][12] library.

## Installation

```console
npm install @tufjs/canonical-json
```

## Usage

```javascript
const json = require('@tufjs/canonical-json')

const obj = {
  bool: true,
  num: 42,
  ary: [1, 2, 3],
  str: "foo\\bar"
}

console.log(json.canonicalize(obj))
// output: {"ary":[1,2,3],"bool":true,"num":42,"str":"foo\\bar"}
```

[1]: https://wiki.laptop.org/go/Canonical_JSON
[2]: https://www.rfc-editor.org/rfc/rfc8785
[3]: https://www.npmjs.com/package/@stratumn/canonicaljson
[4]: https://www.npmjs.com/package/@truestamp/canonify
[5]: https://www.npmjs.com/package/canonical-json
[6]: https://www.npmjs.com/package/canonicalize
[7]: https://www.npmjs.com/package/canonicalize-json
[8]: https://www.npmjs.com/package/json-canonicalize
[9]: https://www.npmjs.com/package/another-json
[10]: https://theupdateframework.github.io/specification/latest/#metaformat
[11]: https://github.com/secure-systems-lab/securesystemslib
[12]: https://github.com/secure-systems-lab/go-securesystemslib
