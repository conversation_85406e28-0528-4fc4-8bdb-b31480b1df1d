{"name": "@inquirer/password", "version": "4.0.16", "description": "Inquirer password prompt", "keywords": ["answer", "answers", "ask", "base", "cli", "command", "command-line", "confirm", "enquirer", "generate", "generator", "hyper", "input", "inquire", "inquirer", "interface", "iterm", "javascript", "menu", "node", "nodejs", "prompt", "promptly", "prompts", "question", "readline", "scaffold", "scaffolder", "scaffolding", "stdin", "stdout", "terminal", "tty", "ui", "yeoman", "yo", "zsh"], "homepage": "https://github.com/SBoudrias/Inquirer.js/blob/main/packages/password/README.md", "repository": {"type": "git", "url": "https://github.com/SBoudrias/Inquirer.js.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {"./package.json": "./package.json", ".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}}, "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/commonjs/index.d.ts", "files": ["dist"], "scripts": {"attw": "attw --pack", "tsc": "tshy"}, "dependencies": {"@inquirer/core": "^10.1.14", "@inquirer/type": "^3.0.7", "ansi-escapes": "^4.3.2"}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.1", "@inquirer/testing": "^2.1.48", "tshy": "^3.0.2"}, "engines": {"node": ">=18"}, "publishConfig": {"access": "public"}, "tshy": {"exclude": ["src/**/*.test.ts"], "exports": {"./package.json": "./package.json", ".": "./src/index.ts"}}, "peerDependencies": {"@types/node": ">=18"}, "peerDependenciesMeta": {"@types/node": {"optional": true}}, "gitHead": "43b7bb94390c1e2b6473af1b790ff2fd1c8007c8"}