{"ALL_COMPILER_OPTIONS_6917": "TODAS LAS OPCIONES DEL COMPILADOR", "A_0_modifier_cannot_be_used_with_an_import_declaration_1079": "Un modificador '{0}' no se puede usar con una declaración de importación.", "A_0_parameter_must_be_the_first_parameter_2680": "El parámetro \"{0}\" debe ser el primer parámetro.", "A_JSDoc_template_tag_may_not_follow_a_typedef_callback_or_overload_tag_8039": "Una etiqueta \"@template\" de JSDoc no puede seguir a una etiqueta \"@typedef\", \"@callback\" u \"@overload\"", "A_JSDoc_typedef_comment_may_not_contain_multiple_type_tags_8033": "Un comentario \"@typedef\" de JSDoc no puede contener varias etiquetas \"@type\".", "A_bigint_literal_cannot_be_used_as_a_property_name_1539": "No se puede usar un literal 'bigint' como nombre de propiedad.", "A_bigint_literal_cannot_use_exponential_notation_1352": "Un literal bigint no puede usar la notación exponencial.", "A_bigint_literal_must_be_an_integer_1353": "Un literal bigint debe ser un entero.", "A_binding_pattern_parameter_cannot_be_optional_in_an_implementation_signature_2463": "Un parámetro de patrón de enlace no puede ser opcional en una signatura de implementación.", "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105": "Una instrucción \"break\" solo se puede usar dentro de una iteración envolvente o en una instrucción switch.", "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116": "Una instrucción \"break\" solo puede saltar a una etiqueta de una instrucción envolvente.", "A_character_class_must_not_contain_a_reserved_double_punctuator_Did_you_mean_to_escape_it_with_backs_1522": "Una clase de caracteres no debe contener un signo de puntuación doble reservado. ¿Querías escaparlo con barra diagonal inversa?", "A_character_class_range_must_not_be_bounded_by_another_character_class_1516": "Un rango de clases de caracteres no debe estar limitado por otra clase de caracteres.", "A_class_can_only_implement_an_identifier_Slashqualified_name_with_optional_type_arguments_2500": "Una clase solo puede implementar un identificador o nombre completo con argumentos de tipo opcional.", "A_class_can_only_implement_an_object_type_or_intersection_of_object_types_with_statically_known_memb_2422": "Una clase solo puede implementar un tipo de objeto o una intersección de tipos de objeto con miembros conocidos estáticamente.", "A_class_cannot_extend_a_primitive_type_like_0_Classes_can_only_extend_constructable_values_2863": "Una clase no puede extender un tipo primitivo como '{0}'. Las clases solo pueden extender valores que se puedan construir.", "A_class_cannot_implement_a_primitive_type_like_0_It_can_only_implement_other_named_object_types_2864": "Una clase no puede implementar un tipo primitivo como '{0}'. Solo puede implementar otros tipos de objeto con nombre.", "A_class_declaration_without_the_default_modifier_must_have_a_name_1211": "Una declaración de clase sin el modificador \"default\" debe tener un nombre.", "A_class_member_cannot_have_the_0_keyword_1248": "Un miembro de clase no puede tener la palabra clave '{0}'.", "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171": "No se admite una expresión de coma en un nombre de propiedad calculada.", "A_computed_property_name_cannot_reference_a_type_parameter_from_its_containing_type_2467": "Un nombre de propiedad calculada no puede hacer referencia a un parámetro de tipo desde su tipo contenedor.", "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166": "Un nombre de propiedad calculada de una declaración de propiedad de clase debe tener un tipo de literal simple o un tipo \"unique symbol\".", "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168": "Un nombre de propiedad calculada en una sobrecarga de método debe hacer referencia a una expresión que sea de tipo literal o \"unique symbol\".", "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170": "Un nombre de propiedad calculada en un literal de tipo debe hacer referencia a una expresión que sea de tipo literal o \"unique symbol\".", "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165": "Un nombre de propiedad calculada en un contexto de ambiente debe hacer referencia a una expresión que sea de tipo literal o \"unique symbol\".", "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169": "Un nombre de propiedad calculada en una interfaz debe hacer referencia a una expresión que sea de tipo literal o \"unique symbol\".", "A_computed_property_name_must_be_of_type_string_number_symbol_or_any_2464": "Un nombre de propiedad calculada debe ser de tipo \"string\", \"number\", \"symbol\" o \"any\".", "A_const_assertions_can_only_be_applied_to_references_to_enum_members_or_string_number_boolean_array__1355": "Las aserciones \"const\" solo pueden aplicarse a las referencias a miembros de enumeración o a literales de cadena, numéricos, booleanos, de matriz o de objeto.", "A_const_enum_member_can_only_be_accessed_using_a_string_literal_2476": "Solo se puede acceder a un miembro de enumeración const mediante un literal de cadena.", "A_const_initializer_in_an_ambient_context_must_be_a_string_or_numeric_literal_or_literal_enum_refere_1254": "Un inicializador \"const\" en un contexto de ambiente debe ser un literal de cadena o numérico o bien una referencia de enumeración de literal.", "A_constructor_cannot_contain_a_super_call_when_its_class_extends_null_17005": "Un constructor no puede contener una llamada a \"super\" si su clase extiende \"null\".", "A_constructor_cannot_have_a_this_parameter_2681": "Un constructor no puede tener un parámetro 'this'.", "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104": "Una instrucción \"continue\" solo se puede usar en una instrucción de iteración envolvente.", "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115": "Una instrucción \"continue\" solo puede saltar a una etiqueta de una instrucción de iteración envolvente.", "A_declaration_file_cannot_be_imported_without_import_type_Did_you_mean_to_import_an_implementation_f_2846": "No se puede importar un archivo de declaración sin 'import type'. ¿Quería importar un archivo de implementación '{0}' en su lugar?", "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038": "Un modificador \"declare\" no se puede usar en un contexto de ambiente.", "A_decorator_can_only_decorate_a_method_implementation_not_an_overload_1249": "Un decorador solo puede modificar la implementación de un método, no una sobrecarga.", "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113": "Una cláusula \"default\" no puede aparecer más de una vez en una instrucción \"switch\".", "A_default_export_can_only_be_used_in_an_ECMAScript_style_module_1319": "Solo se puede usar una exportación predeterminada en un módulo de estilo ECMAScript.", "A_default_export_must_be_at_the_top_level_of_a_file_or_module_declaration_1258": "Una exportación predeterminada debe estar en el nivel superior de una declaración de módulo o archivo.", "A_definite_assignment_assertion_is_not_permitted_in_this_context_1255": "En este contexto no se permite una aserción de asignación definitiva \"!\".", "A_destructuring_declaration_must_have_an_initializer_1182": "Una declaración de desestructuración debe tener un inicializador.", "A_dynamic_import_call_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_for_t_2712": "Una llamada de importación dinámica en ES5 requiere el constructor \"Promise\".  Asegúrese de que tiene una declaración para el constructor \"Promise\" o incluya \"ES2015\" en su opción \"--lib\".", "A_dynamic_import_call_returns_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_include_ES20_2711": "Una llamada de importación dinámica devuelve un valor \"Promise\". Asegúrese de que tiene una declaración para \"Promise\" o incluya \"ES2015\" en la opción \"--lib\".", "A_file_cannot_have_a_reference_to_itself_1006": "Un archivo no puede tener una referencia a sí mismo.", "A_function_returning_never_cannot_have_a_reachable_end_point_2534": "Una función que devuelve 'never' no puede tener un punto de conexión alcanzable.", "A_function_that_is_called_with_the_new_keyword_cannot_have_a_this_type_that_is_void_2679": "Una función a la que se llama con la palabra clave 'new' no puede tener un tipo 'this' que sea 'void'.", "A_function_whose_declared_type_is_neither_undefined_void_nor_any_must_return_a_value_2355": "Una función cuyo tipo declarado no es \"undefined\", \"void\" o \"any\" debe devolver un valor.", "A_generator_cannot_have_a_void_type_annotation_2505": "Un generador no puede tener una anotación de tipo \"void\".", "A_get_accessor_cannot_have_parameters_1054": "Un descriptor de acceso \"get\" no puede tener parámetros.", "A_get_accessor_must_be_at_least_as_accessible_as_the_setter_2808": "Un descriptor de acceso get debe ser al menos tan accesible como el establecedor", "A_get_accessor_must_return_a_value_2378": "Un descriptor de acceso \"get\" debe devolver un valor.", "A_label_is_not_allowed_here_1344": "No se permite una etiqueta aquí.", "A_labeled_tuple_element_is_declared_as_optional_with_a_question_mark_after_the_name_and_before_the_c_5086": "Un elemento de tupla etiquetado se declara como opcional con un signo de interrogación después del nombre y antes de los dos puntos, en lugar de después del tipo.", "A_labeled_tuple_element_is_declared_as_rest_with_a_before_the_name_rather_than_before_the_type_5087": "Un elemento de tupla etiquetado se declara como rest con \"...\" delante del nombre, en lugar de delante del tipo.", "A_mapped_type_may_not_declare_properties_or_methods_7061": "Un tipo asignado no puede declarar propiedades ni métodos.", "A_member_initializer_in_a_enum_declaration_cannot_reference_members_declared_after_it_including_memb_2651": "Un inicializador de miembro de una declaración de enumeración no puede hacer referencia a los miembros que se declaran después de este, incluidos aquellos definidos en otras enumeraciones.", "A_mixin_class_must_have_a_constructor_with_a_single_rest_parameter_of_type_any_2545": "Una clase mixin debe tener un constructor con un solo parámetro rest de tipo \"any[]\"", "A_mixin_class_that_extends_from_a_type_variable_containing_an_abstract_construct_signature_must_also_2797": "Una clase mixin que se extiende desde una variable de tipo que contiene una signatura de construcción abstracta debe declararse también como \"abstract\".", "A_module_cannot_have_multiple_default_exports_2528": "Un módulo no puede tener varias exportaciones predeterminadas.", "A_namespace_declaration_cannot_be_in_a_different_file_from_a_class_or_function_with_which_it_is_merg_2433": "Una declaración de espacio de nombres no puede estar en un archivo distinto de una clase o función con la que se combina.", "A_namespace_declaration_cannot_be_located_prior_to_a_class_or_function_with_which_it_is_merged_2434": "Una declaración de espacio de nombres no se puede situar antes que una clase o función con la que se combina.", "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235": "Una declaración de espacio de nombres solo se permite en el nivel superior de un espacio de nombres o módulo.", "A_namespace_declaration_should_not_be_declared_using_the_module_keyword_Please_use_the_namespace_key_1540": "Una declaración \"namespace\" no debe declararse con la palabra clave \"module\". Use la palabra clave \"namespace\" en su lugar.", "A_non_dry_build_would_build_project_0_6357": "Una compilación no -dry compilaría el proyecto \"{0}\"", "A_non_dry_build_would_delete_the_following_files_Colon_0_6356": "Una compilación no -dry eliminaría los archivos siguientes: {0}", "A_non_dry_build_would_update_timestamps_for_output_of_project_0_6374": "Una compilación no -dry actualizaría las marcas de tiempo para la salida del proyecto \"{0}\".", "A_parameter_initializer_is_only_allowed_in_a_function_or_constructor_implementation_2371": "Un inicializador de parámetros solo se permite en una implementación de función o de constructor.", "A_parameter_property_cannot_be_declared_using_a_rest_parameter_1317": "Una propiedad de parámetro no se puede declarar mediante un parámetro rest.", "A_parameter_property_is_only_allowed_in_a_constructor_implementation_2369": "Una propiedad de parámetro solo se permite en una implementación de constructor.", "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187": "Una propiedad de parámetro podría no declararse mediante un patrón de enlace.", "A_promise_must_have_a_then_method_1059": "Una promesa debe tener un método \"then\".", "A_property_of_a_class_whose_type_is_a_unique_symbol_type_must_be_both_static_and_readonly_1331": "Una propiedad de una clase cuyo tipo sea \"unique symbol\" debe ser \"static\" y \"readonly\".", "A_property_of_an_interface_or_type_literal_whose_type_is_a_unique_symbol_type_must_be_readonly_1330": "Una propiedad de una interfaz o un literal de tipo cuyo tipo sea \"unique symbol\" debe ser \"readonly\".", "A_required_element_cannot_follow_an_optional_element_1257": "Un elemento obligatorio no puede seguir a un elemento opcional.", "A_required_parameter_cannot_follow_an_optional_parameter_1016": "Un parámetro obligatorio no puede seguir a un parámetro opcional.", "A_rest_element_cannot_contain_a_binding_pattern_2501": "Un elemento rest no puede contener un patrón de enlace.", "A_rest_element_cannot_follow_another_rest_element_1265": "Un elemento rest no puede seguir a otro elemento rest.", "A_rest_element_cannot_have_a_property_name_2566": "Un elemento rest no puede tener un nombre de propiedad.", "A_rest_element_cannot_have_an_initializer_1186": "Un elemento rest no puede tener un inicializador.", "A_rest_element_must_be_last_in_a_destructuring_pattern_2462": "Un elemento rest debe ser el último en un patrón de desestructuración.", "A_rest_element_type_must_be_an_array_type_2574": "Un tipo de elemento rest debe ser un tipo de matriz.", "A_rest_parameter_cannot_be_optional_1047": "Un parámetro rest no puede ser opcional.", "A_rest_parameter_cannot_have_an_initializer_1048": "Un parámetro rest no puede tener un inicializador.", "A_rest_parameter_must_be_last_in_a_parameter_list_1014": "Un parámetro rest debe ser el último de una lista de parámetros.", "A_rest_parameter_must_be_of_an_array_type_2370": "Un parámetro rest debe ser de un tipo de matriz.", "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013": "Un parámetro rest o un patrón de enlace no pueden finalizar con una coma.", "A_return_statement_can_only_be_used_within_a_function_body_1108": "Una instrucción \"return\" solo se puede usar en el cuerpo de una función.", "A_return_statement_cannot_be_used_inside_a_class_static_block_18041": "Una instrucción 'return' no se puede usar dentro de un bloque estático de clase.", "A_series_of_entries_which_re_map_imports_to_lookup_locations_relative_to_the_baseUrl_6167": "Serie de entradas que reasigna las importaciones a ubicaciones de búsqueda relativas a \"baseUrl\".", "A_set_accessor_cannot_have_a_return_type_annotation_1095": "Un descriptor de acceso \"set\" no puede tener una anotación de tipo de valor devuelto.", "A_set_accessor_cannot_have_an_optional_parameter_1051": "Un descriptor de acceso \"set\" no puede tener un parámetro opcional.", "A_set_accessor_cannot_have_rest_parameter_1053": "Un descriptor de acceso \"set\" no puede tener un parámetro rest.", "A_set_accessor_must_have_exactly_one_parameter_1049": "Un descriptor de acceso \"set\" debe tener exactamente un parámetro.", "A_set_accessor_parameter_cannot_have_an_initializer_1052": "Un parámetro de descriptor de acceso \"set\" no puede tener un inicializador.", "A_spread_argument_must_either_have_a_tuple_type_or_be_passed_to_a_rest_parameter_2556": "Un argumento de difusión debe tener un tipo de tupla o se puede pasar a un parámetro \"rest\".", "A_super_call_must_be_a_root_level_statement_within_a_constructor_of_a_derived_class_that_contains_in_2401": "Una llamada \"super\" debe ser una instrucción de nivel raíz dentro de un constructor de una clase derivada que contiene propiedades inicializadas, propiedades de parámetros o identificadores privados.", "A_super_call_must_be_the_first_statement_in_the_constructor_to_refer_to_super_or_this_when_a_derived_2376": "Una llamada \"super\" debe ser la primera instrucción del constructor para hacer referencia a \"super\" o \"this\" cuando una clase derivada contiene propiedades inicializadas, propiedades de parámetro o identificadores privados.", "A_this_based_type_guard_is_not_compatible_with_a_parameter_based_type_guard_2518": "Una restricción de tipo basada en 'this' no es compatible con una restricción de tipo basada en un parámetro.", "A_this_type_is_available_only_in_a_non_static_member_of_a_class_or_interface_2526": "El tipo \"this\" solo está disponible en un miembro no estático de una clase o interfaz.", "A_top_level_export_modifier_cannot_be_used_on_value_declarations_in_a_CommonJS_module_when_verbatimM_1287": "No se puede usar un modificador \"export\" de nivel superior en declaraciones de valor en un módulo CommonJS cuando \"verbatimModuleSyntax\" está habilitado.", "A_tsconfig_json_file_is_already_defined_at_Colon_0_5054": "Ya hay un archivo \"tsconfig.json\" definido en: '{0}'.", "A_tuple_member_cannot_be_both_optional_and_rest_5085": "Un miembro de tupla no puede ser tanto opcional como REST.", "A_tuple_type_cannot_be_indexed_with_a_negative_value_2514": "Un tipo de tupla no se puede indizar con un valor negativo.", "A_type_assertion_expression_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_expression_Con_17007": "No se admite una expresión de aserción de tipo en el lado izquierdo de una expresión de exponenciación. Considere la posibilidad de incluir la expresión entre paréntesis.", "A_type_literal_property_cannot_have_an_initializer_1247": "Una propiedad de literal de tipo no puede tener un inicializador.", "A_type_only_import_can_specify_a_default_import_or_named_bindings_but_not_both_1363": "Una importación solo de tipo puede especificar una importación predeterminada o enlaces con nombre, pero no ambos.", "A_type_predicate_cannot_reference_a_rest_parameter_1229": "Un predicado de tipo no puede hacer referencia a un parámetro rest.", "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230": "Un predicado de tipo no puede hacer referencia al elemento '{0}' de un patrón de enlace.", "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228": "En las funciones y los métodos, un predicado de tipo solo se permite en la posición de tipo de valor devuelto.", "A_type_predicate_s_type_must_be_assignable_to_its_parameter_s_type_2677": "El tipo de un predicado de tipo debe poderse asignar al tipo de su parámetro.", "A_type_referenced_in_a_decorated_signature_must_be_imported_with_import_type_or_a_namespace_import_w_1272": "Un tipo al que se hace referencia en una firma representativo debe importarse con \"import type\" o una importación de espacio de nombres cuando están habilitados \"isolatedModules\" y \"emitDecoratorMetadata\".", "A_variable_whose_type_is_a_unique_symbol_type_must_be_const_1332": "Una variable cuyo tipo sea \"unique symbol\" debe ser \"const\".", "A_yield_expression_is_only_allowed_in_a_generator_body_1163": "Una expresión \"yield\" solo se permite en un cuerpo de generador.", "Abstract_method_0_in_class_1_cannot_be_accessed_via_super_expression_2513": "No se puede acceder al método abstracto '{0}' de la clase '{1}' mediante una expresión super.", "Abstract_methods_can_only_appear_within_an_abstract_class_1244": "Los métodos abstractos solo pueden aparecer en una clase abstracta.", "Abstract_properties_can_only_appear_within_an_abstract_class_1253": "Las propiedades abstractas solo pueden aparecer en una clase abstracta.", "Abstract_property_0_in_class_1_cannot_be_accessed_in_the_constructor_2715": "No se puede acceder a la propiedad abstracta \"{0}\" de la clase \"{1}\" en el constructor.", "Accessibility_modifier_already_seen_1028": "El modificador de accesibilidad ya se ha visto.", "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056": "Los descriptores de acceso solo están disponibles cuando el destino es ECMAScript 5 y versiones posteriores.", "Accessors_must_both_be_abstract_or_non_abstract_2676": "Los descriptores de acceso deben ser los dos abstractos o los dos no abstractos.", "Add_0_to_unresolved_variable_90008": "Agregar \"{0}.\" a una variable no resuelta", "Add_a_return_statement_95111": "Agregar una instrucción \"return\"", "Add_a_return_type_to_the_function_declaration_9031": "Agrega un tipo de valor devuelto a la declaración de función.", "Add_a_return_type_to_the_function_expression_9030": "Agrega un tipo de valor devuelto a la expresión de función.", "Add_a_return_type_to_the_get_accessor_declaration_9032": "Agregue un tipo de valor devuelto a la declaración de get accessor.", "Add_a_return_type_to_the_method_9034": "Agregar un tipo de valor devuelto al método", "Add_a_type_annotation_to_the_parameter_0_9028": "Agregue una anotación de tipo al parámetro {0}.", "Add_a_type_annotation_to_the_property_0_9029": "Agregue una anotación de tipo a la propiedad {0}.", "Add_a_type_annotation_to_the_variable_0_9027": "Agregue una anotación de tipo a la variable {0}.", "Add_a_type_to_parameter_of_the_set_accessor_declaration_9033": "Agregue un tipo al parámetro de la declaración del set accessor.", "Add_all_missing_async_modifiers_95041": "Agregar todos los modificadores \"async\" que faltan", "Add_all_missing_attributes_95168": "Agregar todos los atributos que faltan", "Add_all_missing_call_parentheses_95068": "Agregar todos los paréntesis de llamada que faltan", "Add_all_missing_function_declarations_95157": "Agregar todas las declaraciones de función que faltan", "Add_all_missing_imports_95064": "Agregar todas las importaciones que faltan", "Add_all_missing_members_95022": "Agregar todos los miembros que faltan", "Add_all_missing_override_modifiers_95162": "Agregar todos los modificadores \"override\" que faltan", "Add_all_missing_parameters_95190": "Agregar todos los parámetros que faltan", "Add_all_missing_properties_95166": "Agregar todas las propiedades que faltan", "Add_all_missing_return_statement_95114": "Agregar todas las instrucciones \"return\" que faltan", "Add_all_missing_super_calls_95039": "Agregar todas las llamadas a super que faltan", "Add_all_missing_type_annotations_90067": "Agregar todas las anotaciones de tipo que faltan", "Add_all_optional_parameters_95193": "Agregar todos los parámetros opcionales", "Add_annotation_of_type_0_90062": "Agregar anotación de tipo '{0}'", "Add_async_modifier_to_containing_function_90029": "Agregar el modificador async a la función contenedora", "Add_await_95083": "Agregar \"await\"", "Add_await_to_initializer_for_0_95084": "Agregar \"await\" al inicializador de \"{0}\"", "Add_await_to_initializers_95089": "Ag<PERSON><PERSON> \"await\" a los inicializadores", "Add_braces_to_arrow_function_95059": "Agregar llaves a la función de flecha", "Add_const_to_all_unresolved_variables_95082": "Agregar \"const\" a todas las variables no resueltas", "Add_const_to_unresolved_variable_95081": "Agregar \"const\" a la variable no resuelta", "Add_definite_assignment_assertion_to_property_0_95020": "Agregar aserción de asignación definitiva a la propiedad \"{0}\"", "Add_definite_assignment_assertions_to_all_uninitialized_properties_95028": "Agregar aserciones de asignación definitiva a todas las propiedades sin inicializar", "Add_export_to_make_this_file_into_a_module_95097": "Agregar \"export {}\" para transformar este archivo en un módulo", "Add_extends_constraint_2211": "Agregar restricción \"extends\".", "Add_extends_constraint_to_all_type_parameters_2212": "Agregar restricción \"extends\" a todos los parámetros de tipo", "Add_import_from_0_90057": "Agregar importación desde “{0}”", "Add_index_signature_for_property_0_90017": "Agregar una signatura de índice para la propiedad \"{0}\"", "Add_initializer_to_property_0_95019": "Agregar inicializador a la propiedad \"{0}\"", "Add_initializers_to_all_uninitialized_properties_95027": "Agregar inicializadores a todas las propiedades sin inicializar", "Add_missing_attributes_95167": "Agregar los atributos que faltan", "Add_missing_call_parentheses_95067": "Agregar los paréntesis de llamada que faltan", "Add_missing_comma_for_object_member_completion_0_95187": "Agregue la coma que falta para el '{0}' de finalización del miembro del objeto.", "Add_missing_enum_member_0_95063": "Agregar el miembro de enumeración \"{0}\" que falta", "Add_missing_function_declaration_0_95156": "Agregar la declaración de función \"{0}\" que falta", "Add_missing_new_operator_to_all_calls_95072": "Agregar el operador \"new\" que falta a todas las llamadas", "Add_missing_new_operator_to_call_95071": "Agregar el operador \"new\" que falta a la llamada", "Add_missing_parameter_to_0_95188": "Agregar parámetro que falta a '{0}'", "Add_missing_parameters_to_0_95189": "Agregar parámetros que faltan a '{0}'", "Add_missing_properties_95165": "Agregar propiedades que faltan", "Add_missing_super_call_90001": "Agregar la llamada a \"super()\" que falta", "Add_missing_typeof_95052": "Agregar el elemento \"typeof\" que falta", "Add_names_to_all_parameters_without_names_95073": "Agregar nombres a todos los parámetros sin nombres", "Add_optional_parameter_to_0_95191": "Agregar parámetro opcional a \"{0}\"", "Add_optional_parameters_to_0_95192": "Agregar parámetros opcionales a \"{0}\"", "Add_or_remove_braces_in_an_arrow_function_95058": "Agregar o quitar llaves en una función de flecha", "Add_override_modifier_95160": "Agregar el modificador \"override\"", "Add_parameter_name_90034": "Agregar un nombre de parámetro", "Add_qualifier_to_all_unresolved_variables_matching_a_member_name_95037": "Agregar un calificador a todas las variables no resueltas que coincidan con un nombre de miembro", "Add_resolution_mode_import_attribute_95196": "Agregar atributo de importación \"resolution-mode\"", "Add_resolution_mode_import_attribute_to_all_type_only_imports_that_need_it_95197": "Agregar el atributo de importación \"resolution-mode\" a todas las importaciones de solo tipo que lo necesiten", "Add_return_type_0_90063": "Agregar tipo de valor devuelto '{0}'", "Add_satisfies_and_a_type_assertion_to_this_expression_satisfies_T_as_T_to_make_the_type_explicit_9035": "Agregue satisfacciones y una aserción de tipo a esta expresión (satisfacciones T como T) para que el tipo sea explícito.", "Add_satisfies_and_an_inline_type_assertion_with_0_90068": "Agregar satisfacciones y una aserción de tipo insertado con '{0}'", "Add_to_all_uncalled_decorators_95044": "Agregar \"()\" a todos los elementos Decorator a los que no se llama", "Add_ts_ignore_to_all_error_messages_95042": "Agregar \"@ts-ignore\" a todos los mensajes de error", "Add_undefined_to_a_type_when_accessed_using_an_index_6674": "Agregue \"indefinido\" a un tipo cuando se acceda mediante un índice.", "Add_undefined_to_optional_property_type_95169": "Agregar 'undefined' al tipo de propiedad opcional", "Add_undefined_type_to_all_uninitialized_properties_95029": "Agregar un tipo no definido a todas las propiedades sin inicializar", "Add_undefined_type_to_property_0_95018": "Agregar un tipo \"undefined\" a la propiedad \"{0}\"", "Add_unknown_conversion_for_non_overlapping_types_95069": "Agregar una conversión \"unknown\" para los tipos que no se superponen", "Add_unknown_to_all_conversions_of_non_overlapping_types_95070": "Agregar \"unknown\" a todas las conversiones de tipos que no se superponen", "Add_void_to_Promise_resolved_without_a_value_95143": "Agregar \"void\" a la instancia de Promise resuelta sin un valor", "Add_void_to_all_Promises_resolved_without_a_value_95144": "Agregar \"void\" a todas las instancias de Promise resueltas sin un valor", "Adding_a_tsconfig_json_file_will_help_organize_projects_that_contain_both_TypeScript_and_JavaScript__5068": "Agregar un archivo tsconfig.json ayuda a organizar los proyectos que contienen archivos TypeScript y JavaScript. Más información en https://aka.ms/tsconfig.", "All_declarations_of_0_must_have_identical_constraints_2838": "Todas las declaraciones de \"{0}\" deben tener delimitaciones idénticas.", "All_declarations_of_0_must_have_identical_modifiers_2687": "Todas las declaraciones de '{0}' deben tener modificadores idénticos.", "All_declarations_of_0_must_have_identical_type_parameters_2428": "Todas las declaraciones de '{0}' deben tener parámetros de tipo idénticos.", "All_declarations_of_an_abstract_method_must_be_consecutive_2516": "Todas las declaraciones de un método abstracto deben ser consecutivas.", "All_destructured_elements_are_unused_6198": "Todos los elementos desestructurados están sin utilizar.", "All_imports_in_import_declaration_are_unused_6192": "Todas las importaciones de la declaración de importación están sin utilizar.", "All_type_parameters_are_unused_6205": "Ninguno de los parámetros de tipo se usa.", "All_variables_are_unused_6199": "<PERSON>das las variables son no utilizadas.", "Allow_JavaScript_files_to_be_a_part_of_your_program_Use_the_checkJS_option_to_get_errors_from_these__6600": "Permita que los archivos JavaScript formen parte del programa. Use la opción \"checkJS\" para obtener errores de estos archivos.", "Allow_accessing_UMD_globals_from_modules_6602": "Permite el acceso a las bibliotecas globales de UMD desde los módulos.", "Allow_default_imports_from_modules_with_no_default_export_This_does_not_affect_code_emit_just_typech_6011": "Permitir las importaciones predeterminadas de los módulos sin exportación predeterminada. Esto no afecta a la emisión de código, solo a la comprobación de tipos.", "Allow_import_x_from_y_when_a_module_doesn_t_have_a_default_export_6601": "Permita \"importar x desde y\" cuando un módulo no tiene una exportación predeterminada.", "Allow_importing_helper_functions_from_tslib_once_per_project_instead_of_including_them_per_file_6639": "Permita la importación de funciones de ayuda desde tslib una vez por proyecto, en lugar de incluirlas por archivo.", "Allow_imports_to_include_TypeScript_file_extensions_Requires_moduleResolution_bundler_and_either_noE_6407": "Permitir que las importaciones incluyan extensiones de archivo TypeScript. Requiere que se establezca \"--moduleResolution bundler\" y \"--noEmit\" o \"--emitDeclarationOnly\".", "Allow_javascript_files_to_be_compiled_6102": "Permitir que se compilen los archivos de JavaScript.", "Allow_multiple_folders_to_be_treated_as_one_when_resolving_modules_6691": "Permita que varias carpetas se consideren como una al resolver módulos.", "Already_included_file_name_0_differs_from_file_name_1_only_in_casing_1261": "El nombre de archivo \"{0}\" ya incluido es diferente del nombre de archivo \"{1}\" solo en el uso de mayúsculas y minúsculas.", "Ambient_module_declaration_cannot_specify_relative_module_name_2436": "La declaración de módulo de ambiente no puede especificar un nombre de módulo relativo.", "Ambient_modules_cannot_be_nested_in_other_modules_or_namespaces_2435": "Los módulos de ambiente no se pueden anidar en otros módulos o espacios de nombres.", "An_AMD_module_cannot_have_multiple_name_assignments_2458": "Un módulo AMD no puede tener varias asignaciones de nombre.", "An_abstract_accessor_cannot_have_an_implementation_1318": "Un descriptor de acceso abstracto no puede tener una implementación.", "An_accessibility_modifier_cannot_be_used_with_a_private_identifier_18010": "No se puede usar un modificador de accesibilidad con un identificador privado.", "An_accessor_cannot_have_type_parameters_1094": "Un descriptor de acceso no puede tener parámetros de tipo.", "An_accessor_property_cannot_be_declared_optional_1276": "Una propiedad 'accessor' no se puede declarar como opcional.", "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234": "Una declaración de módulo de ambiente solo se permite en el nivel superior de un archivo.", "An_argument_for_0_was_not_provided_6210": "No se proporcionó ningún argumento para \"{0}\".", "An_argument_matching_this_binding_pattern_was_not_provided_6211": "No se proporcionó ningún argumento que coincida con este patrón de enlace.", "An_arithmetic_operand_must_be_of_type_any_number_bigint_or_an_enum_type_2356": "Un operando aritmético debe ser de tipo \"any\", \"number\", \"bigint\" o un tipo de enumeración.", "An_arrow_function_cannot_have_a_this_parameter_2730": "Una función de flecha no puede tener un parámetro \"this\".", "An_async_function_or_method_in_ES5_requires_the_Promise_constructor_Make_sure_you_have_a_declaration_2705": "Una función o un método de asincronía en ES5 requiere el constructor \"Promise\".  Asegúrese de que tiene una declaración para el constructor \"Promise\" o incluya \"ES2015\" en su opción \"--lib\".", "An_async_function_or_method_must_return_a_Promise_Make_sure_you_have_a_declaration_for_Promise_or_in_2697": "Una función o un método asincrónico debe devolver un \"Promise\". Asegúrese de que tiene una declaración \"Promise\" o incluya \"ES2015\" en la opción \"--lib\".", "An_async_iterator_must_have_a_next_method_2519": "Un iterador de asincronía debe tener un método \"next()\".", "An_element_access_expression_should_take_an_argument_1011": "Una expresión de acceso de elemento debe admitir un argumento.", "An_enum_member_cannot_be_named_with_a_private_identifier_18024": "No se puede denominar un miembro de enumeración con un identificador privado.", "An_enum_member_cannot_have_a_numeric_name_2452": "Un miembro de enumeración no puede tener un nombre numérico.", "An_enum_member_name_must_be_followed_by_a_or_1357": "El nombre de un miembro de enumeración debe ir seguido de \",\", \"=\" o \"}\".", "An_expanded_version_of_this_information_showing_all_possible_compiler_options_6928": "Una versión expandida de esta información, que muestra todas las opciones posibles del compilador", "An_export_assignment_cannot_be_used_in_a_module_with_other_exported_elements_2309": "Una asignación de exportación no se puede usar en un módulo con otros elementos exportados.", "An_export_assignment_cannot_be_used_in_a_namespace_1063": "Una asignación de exportación no se puede usar en espacios de nombres.", "An_export_assignment_cannot_have_modifiers_1120": "Una asignación de exportación no puede tener modificadores.", "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231": "Una asignación de exportación debe estar en el nivel superior de una declaración de módulo o archivo.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_module_1474": "Una declaración de exportación solo se puede usar en el nivel superior de un módulo.", "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233": "Una declaración de exportación solo se puede usar en el nivel superior de un espacio de nombres o módulo.", "An_export_declaration_cannot_have_modifiers_1193": "Una declaración de exportación no puede tener modificadores.", "An_export_declaration_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolve_1283": "Una declaración \"export =\" debe hacer referencia a un valor real cuando \"verbatimModuleSyntax\" está habilitado, pero '{0}' se resuelve en una declaración de solo tipo.", "An_export_declaration_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers__1282": "Una declaración 'export =' debe hacer referencia a un valor cuando 'verbatimModuleSyntax' está habilitado, pero '{0}' solo hace referencia a un tipo.", "An_export_default_must_reference_a_real_value_when_verbatimModuleSyntax_is_enabled_but_0_resolves_to_1285": "Una declaración \"export default\" debe hacer referencia a un valor real cuando \"verbatimModuleSyntax\" está habilitado, pero '{0}' se resuelve en una declaración de solo tipo.", "An_export_default_must_reference_a_value_when_verbatimModuleSyntax_is_enabled_but_0_only_refers_to_a_1284": "Una declaración 'export default' debe hacer referencia a un valor cuando 'verbatimModuleSyntax' está habilitado, pero '{0}' solo hace referencia a un tipo.", "An_expression_of_type_void_cannot_be_tested_for_truthiness_1345": "No se puede probar la veracidad de una expresión de tipo \"void\".", "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198": "Un valor de escape Unicode extendido debe estar entre 0x0 y 0x10FFFF, incluidos.", "An_identifier_or_keyword_cannot_immediately_follow_a_numeric_literal_1351": "Un identificador o una palabra clave no puede seguir inmediatamente a un literal numérico.", "An_implementation_cannot_be_declared_in_ambient_contexts_1183": "Una implementación no se puede declarar en contextos de ambiente.", "An_import_alias_cannot_reference_a_declaration_that_was_exported_using_export_type_1379": "Un alias de importación no puede hacer referencia a una declaración que se exportó mediante \"export type\".", "An_import_alias_cannot_reference_a_declaration_that_was_imported_using_import_type_1380": "Un alias de importación no puede hacer referencia a una declaración que se importó mediante \"import type\".", "An_import_alias_cannot_resolve_to_a_type_or_type_only_declaration_when_verbatimModuleSyntax_is_enabl_1288": "Un alias de importación no se puede resolver en una declaración de tipo o solo tipo cuando \"verbatimModuleSyntax\" está habilitado.", "An_import_alias_cannot_use_import_type_1392": "Un alias de importación no puede usar \"import type\"", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_module_1473": "Una declaración de importación solo se puede usar en el nivel superior de un módulo.", "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232": "Una declaración de importación solo se puede usar en el nivel superior de un espacio de nombres o módulo.", "An_import_declaration_cannot_have_modifiers_1191": "Una declaración de importación no puede tener modificadores.", "An_import_path_can_only_end_with_a_0_extension_when_allowImportingTsExtensions_is_enabled_5097": "Una ruta de acceso de importación solo puede terminar con una extensión '{0}' cuando \"allowImportingTsExtensions\" está habilitado.", "An_index_signature_cannot_have_a_rest_parameter_1017": "Una signatura de índice no puede tener un parámetro rest.", "An_index_signature_cannot_have_a_trailing_comma_1025": "Una signatura de índice no puede finalizar con una coma.", "An_index_signature_must_have_a_type_annotation_1021": "Una signatura de índice debe tener una anotación de tipo.", "An_index_signature_must_have_exactly_one_parameter_1096": "Una signatura de índice debe tener exactamente un parámetro.", "An_index_signature_parameter_cannot_have_a_question_mark_1019": "Un parámetro de signatura de índice no puede tener un signo de interrogación.", "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018": "Un parámetro de signatura de índice no puede tener un modificador de accesibilidad.", "An_index_signature_parameter_cannot_have_an_initializer_1020": "Un parámetro de signatura de índice no puede tener un inicializador.", "An_index_signature_parameter_must_have_a_type_annotation_1022": "Un parámetro de signatura de índice debe tener una anotación de tipo.", "An_index_signature_parameter_type_cannot_be_a_literal_type_or_generic_type_Consider_using_a_mapped_o_1337": "Un tipo de parámetro de signatura de índice no puede ser un tipo literal o un tipo genérico. Considere la posibilidad de usar, en su lugar, uno de los tipos de objeto asignados.", "An_index_signature_parameter_type_must_be_string_number_symbol_or_a_template_literal_type_1268": "El tipo de parámetro de la signatura de índice debe ser \"string\", \"number\", \"symbol\" o un tipo literal de plantilla.", "An_instantiation_expression_cannot_be_followed_by_a_property_access_1477": "Una expresión de creación de una instancia no puede ir seguida de un acceso a una propiedad.", "An_interface_can_only_extend_an_identifier_Slashqualified_name_with_optional_type_arguments_2499": "Una interfaz solo puede extender un identificador o nombre completo con argumentos de tipo opcional.", "An_interface_can_only_extend_an_object_type_or_intersection_of_object_types_with_statically_known_me_2312": "Una interfaz solo puede extender un tipo de objeto o una intersección de tipos de objeto con miembros conocidos estáticamente.", "An_interface_cannot_extend_a_primitive_type_like_0_It_can_only_extend_other_named_object_types_2840": "Una interfaz no puede extender un tipo primitivo como '{0}'. Solo puede extender otros tipos de objeto con nombre.", "An_interface_property_cannot_have_an_initializer_1246": "Una propiedad de interfaz no puede tener un inicializador.", "An_iterator_must_have_a_next_method_2489": "Un iterador debe tener un método \"next()\".", "An_jsxFrag_pragma_is_required_when_using_an_jsx_pragma_with_JSX_fragments_17017": "Se necesita una pragma @jsxFrag cuando se usa una pragma @jsx con fragmentos de JSX.", "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118": "Un literal de objeto no puede tener varios descriptores de acceso get o set con el mismo nombre.", "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117": "Un literal de objeto no puede tener varias propiedades con el mismo nombre.", "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119": "Un literal de objeto no puede tener una propiedad y un descriptor de acceso con el mismo nombre.", "An_object_member_cannot_be_declared_optional_1162": "Un miembro de objeto no se puede declarar como opcional.", "An_object_s_Symbol_hasInstance_method_must_return_a_boolean_value_for_it_to_be_used_on_the_right_han_2861": "El método '[Symbol.hasInstance]' de un objeto debe devolver un valor booleano para que se use en el lado derecho de una expresión \"instanceof\".", "An_optional_chain_cannot_contain_private_identifiers_18030": "Una cadena opcional no puede contener identificadores privados.", "An_optional_element_cannot_follow_a_rest_element_1266": "Un elemento opcional no puede seguir a un elemento rest.", "An_outer_value_of_this_is_shadowed_by_this_container_2738": "Este contenedor reemplaza un valor externo de \"this\".", "An_overload_signature_cannot_be_declared_as_a_generator_1222": "Una signatura de sobrecarga no se puede declarar como generador.", "An_unary_expression_with_the_0_operator_is_not_allowed_in_the_left_hand_side_of_an_exponentiation_ex_17006": "No se admite una expresión unaria con el operador '{0}' en el lado izquierdo de una expresión de exponenciación. Considere la posibilidad de incluir la expresión entre paréntesis.", "Annotate_everything_with_types_from_JSDoc_95043": "Anotar todo con tipos de JSDoc", "Annotate_types_of_properties_expando_function_in_a_namespace_90071": "Anotar tipos de propiedades de función expando en un espacio de nombres", "Annotate_with_type_from_JSDoc_95009": "Anotar con tipo de JSDoc", "Another_export_default_is_here_2753": "Aquí hay otro valor export default.", "Any_Unicode_property_that_would_possibly_match_more_than_a_single_character_is_only_available_when_t_1528": "Cualquier propiedad Unicode que posiblemente coincida con más de un carácter solo está disponible cuando se establece la marca Unicode Sets (v).", "Anything_that_would_possibly_match_more_than_a_single_character_is_invalid_inside_a_negated_characte_1518": "Todo lo que posiblemente coincida con más de un carácter no es válido dentro de una clase de caracteres negada.", "Are_you_missing_a_semicolon_2734": "¿Falta un punto y coma?", "Argument_expression_expected_1135": "Se esperaba una expresión de argumento.", "Argument_for_0_option_must_be_Colon_1_6046": "El argumento para la opción \"{0}\" debe ser {1}.", "Argument_of_dynamic_import_cannot_be_spread_element_1325": "El argumento de importación dinámica no puede ser un elemento de propagación.", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_2345": "No se puede asignar un argumento de tipo \"{0}\" al parámetro de tipo \"{1}\".", "Argument_of_type_0_is_not_assignable_to_parameter_of_type_1_with_exactOptionalPropertyTypes_Colon_tr_2379": "El argumento de tipo '{0}' no se puede asignar al parámetro de tipo '{1}' con 'exactOptionalPropertyTypes: true'. Considere la posibilidad de agregar \"undefined\" a los tipos de propiedades del destino.", "Arguments_for_the_rest_parameter_0_were_not_provided_6236": "No se proporcionaron argumentos para el parámetro rest \"{0}\".", "Array_element_destructuring_pattern_expected_1181": "Se esperaba un patrón de desestructuración de elementos de matriz.", "Arrays_with_spread_elements_can_t_inferred_with_isolatedDeclarations_9018": "Las matrices con elementos spread no se pueden inferir con --isolatedDeclarations.", "Assertions_require_every_name_in_the_call_target_to_be_declared_with_an_explicit_type_annotation_2775": "Las aserciones requieren que todos los nombres del destino de llamada se declaren con una anotación de tipo explícito.", "Assertions_require_the_call_target_to_be_an_identifier_or_qualified_name_2776": "Las aserciones requieren que el destino de llamada sea un identificador o un nombre calificado.", "Assigning_properties_to_functions_without_declaring_them_is_not_supported_with_isolatedDeclarations__9023": "No se admite la asignación de propiedades a funciones sin declararlas con --isolatedDeclarations. Agregue una declaración explícita para las propiedades asignadas a esta función.", "Asterisk_Slash_expected_1010": "Se esperaba \"*/\".", "At_least_one_accessor_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9009": "Al menos un descriptor de acceso debe tener una anotación de tipo explícita con --isolatedDeclarations.", "Augmentations_for_the_global_scope_can_only_be_directly_nested_in_external_modules_or_ambient_module_2669": "Los aumentos del ámbito global solo pueden anidarse directamente en módulos externos o en declaraciones de módulos de ambiente.", "Augmentations_for_the_global_scope_should_have_declare_modifier_unless_they_appear_in_already_ambien_2670": "Los aumentos del ámbito global deben tener el modificador 'declare', a menos que aparezcan en un contexto de ambiente.", "Auto_discovery_for_typings_is_enabled_in_project_0_Running_extra_resolution_pass_for_module_1_using__6140": "La detección automática de escritura está habilitada en el proyecto '{0}'. Se va a ejecutar un paso de resolución extra para el módulo '{1}' usando la ubicación de caché '{2}'.", "BUILD_OPTIONS_6919": "OPCIONES DE COMPILACIÓN", "Backwards_Compatibility_6253": "Compatibilidad con versiones anteriores", "Base_class_expressions_cannot_reference_class_type_parameters_2562": "Las expresiones de clase base no pueden hacer referencia a parámetros de tipo de clase.", "Base_constructor_return_type_0_is_not_an_object_type_or_intersection_of_object_types_with_statically_2509": "El tipo de valor devuelto del constructor base \"{0}\" no es un tipo de objeto ni una intersección de tipos de objeto con miembros conocidos estáticamente.", "Base_constructors_must_all_have_the_same_return_type_2510": "Todos los constructores base deben tener el mismo tipo de valor devuelto.", "Base_directory_to_resolve_non_absolute_module_names_6083": "Directorio base para resolver nombres de módulos no absolutos.", "BigInt_literals_are_not_available_when_targeting_lower_than_ES2020_2737": "Los literales BigInt no están disponibles cuando el destino es anterior a ES2020.", "Binary_digit_expected_1177": "Se esperaba un dígito binario.", "Binding_element_0_implicitly_has_an_1_type_7031": "El elemento de enlace '{0}' tiene un tipo '{1}' implícito.", "Binding_elements_can_t_be_exported_directly_with_isolatedDeclarations_9019": "Los elementos de enlace no se pueden exportar directamente con --isolatedDeclarations.", "Block_scoped_variable_0_used_before_its_declaration_2448": "Variable con ámbito de bloque '{0}' usada antes de su declaración.", "Build_a_composite_project_in_the_working_directory_6925": "Compile un proyecto compuesto en el directorio de trabajo.", "Build_all_projects_including_those_that_appear_to_be_up_to_date_6636": "Compilar todos los proyectos, incluidos los que aparecen actualizados.", "Build_one_or_more_projects_and_their_dependencies_if_out_of_date_6364": "Generar uno o varios proyectos y sus dependencias, si no están actualizados", "Build_option_0_requires_a_value_of_type_1_5073": "La opción de compilación \"{0}\" requiere un valor de tipo {1}.", "Building_project_0_6358": "Compilando el proyecto \"{0}\"...", "Built_in_iterators_are_instantiated_with_a_TReturn_type_of_undefined_instead_of_any_6720": "Se crea una instancia de los iteradores integrados con un tipo 'TReturn' de 'undefined' en lugar de 'any'.", "COMMAND_LINE_FLAGS_6921": "MARCAS DE LÍNEA DE COMANDOS", "COMMON_COMMANDS_6916": "COMANDOS COMUNES", "COMMON_COMPILER_OPTIONS_6920": "OPCIONES COMUNES DEL COMPILADOR", "Call_decorator_expression_90028": "Llamar a la expresión decorador", "Call_signature_return_types_0_and_1_are_incompatible_2202": "Los tipos de valor devuelto de la signatura de llamada \"{0}\" y \"{1}\" son incompatibles.", "Call_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7020": "La signatura de llamada, que carece de una anotación de tipo de valor devuelto, tiene implícitamente un tipo de valor devuelto \"any\".", "Call_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2204": "Las signaturas de llamada sin argumentos tienen los tipos de valor devuelto \"{0}\" y \"{1}\" no compatibles.", "Can_only_convert_logical_AND_access_chains_95142": "Solo pueden convertirse las cadenas lógicas Y de acceso", "Can_only_convert_named_export_95164": "Solo se pueden convertir exportaciones con nombre.", "Can_only_convert_property_with_modifier_95137": "Solo se puede convertir la propiedad con el modificador", "Can_only_convert_string_concatenations_and_string_literals_95154": "Solo se pueden convertir concatenaciones de cadenas y literales de cadena", "Cannot_access_0_1_because_0_is_a_type_but_not_a_namespace_Did_you_mean_to_retrieve_the_type_of_the_p_2713": "No se puede acceder a \"{0}.{1}\" porque \"{0}\" es un tipo, no un espacio de nombres. ¿Su intención era recuperar el tipo de la propiedad \"{1}\" en \"{0}\" con \"{0}[\"{1}\"]\"?", "Cannot_access_0_from_another_file_without_qualification_when_1_is_enabled_Use_2_instead_1281": "No se puede obtener acceso a '{0}' desde otro archivo sin calificación cuando '{1}' está habilitado. Use \"{2}\" en su lugar.", "Cannot_access_ambient_const_enums_when_0_is_enabled_2748": "No se puede tener acceso a las enumeraciones const de ambiente cuando '{0}' está habilitado.", "Cannot_assign_a_0_constructor_type_to_a_1_constructor_type_2672": "No se puede asignar un tipo de constructor '{0}' a un tipo de constructor '{1}'.", "Cannot_assign_an_abstract_constructor_type_to_a_non_abstract_constructor_type_2517": "No se puede asignar un tipo de constructor abstracto a uno no abstracto.", "Cannot_assign_to_0_because_it_is_a_class_2629": "No se puede asignar a \"{0}\" porque es una clase.", "Cannot_assign_to_0_because_it_is_a_constant_2588": "No se puede asignar a \"{0}\" porque es una constante.", "Cannot_assign_to_0_because_it_is_a_function_2630": "No se puede asignar a \"{0}\" porque es una función.", "Cannot_assign_to_0_because_it_is_a_namespace_2631": "No se puede asignar a \"{0}\" porque es un espacio de nombres.", "Cannot_assign_to_0_because_it_is_a_read_only_property_2540": "No se puede asignar a \"{0}\" porque es una propiedad de solo lectura.", "Cannot_assign_to_0_because_it_is_an_enum_2628": "No se puede asignar a '{0}' porque es una enumeración.", "Cannot_assign_to_0_because_it_is_an_import_2632": "No se puede asignar a '{0}' porque es una importación.", "Cannot_assign_to_0_because_it_is_not_a_variable_2539": "No se puede asignar a '{0}' porque no es una variable.", "Cannot_assign_to_private_method_0_Private_methods_are_not_writable_2803": "No se puede asignar al método privado \"{0}\". No se puede escribir en los métodos privados.", "Cannot_augment_module_0_because_it_resolves_to_a_non_module_entity_2671": "No se puede aumentar el módulo '{0}' porque se resuelve como una entidad que no es un módulo.", "Cannot_augment_module_0_with_value_exports_because_it_resolves_to_a_non_module_entity_2649": "No se puede aumentar el módulo \"{0}\" con exportaciones de valores porque se resuelve como una entidad que no es un módulo.", "Cannot_compile_modules_using_option_0_unless_the_module_flag_is_amd_or_system_6131": "No se pueden compilar los módulos con la opción '{0}' a no ser que la marca \"--module\" sea \"amd\" o \"system\".", "Cannot_create_an_instance_of_an_abstract_class_2511": "No se puede crear una instancia de una clase abstracta.", "Cannot_delegate_iteration_to_value_because_the_next_method_of_its_iterator_expects_type_1_but_the_co_2766": "No se puede delegar la iteración en un valor porque el método \"next\" de su iterador espera el tipo \"{1}\", pero el generador que lo contiene siempre enviará \"{0}\".", "Cannot_export_0_Only_local_declarations_can_be_exported_from_a_module_2661": "No se puede exportar '{0}'. Solo se pueden exportar declaraciones locales desde un módulo.", "Cannot_extend_a_class_0_Class_constructor_is_marked_as_private_2675": "No se puede extender una clase '{0}'. El constructor de la clase está marcado como privado.", "Cannot_extend_an_interface_0_Did_you_mean_implements_2689": "No se puede extender una interfaz '{0}'. ¿Quiso decir 'implements'?", "Cannot_find_a_tsconfig_json_file_at_the_current_directory_Colon_0_5081": "No se encuentra ningún archivo tsconfig.json en el directorio actual: {0}", "Cannot_find_a_tsconfig_json_file_at_the_specified_directory_Colon_0_5057": "No se encuentra ningún archivo tsconfig.json en el directorio especificado: \"{0}\".", "Cannot_find_global_type_0_2318": "No se encuentra el tipo '{0}' global.", "Cannot_find_global_value_0_2468": "No se encuentra el valor '{0}' global.", "Cannot_find_lib_definition_for_0_2726": "No se encuentra la definición lib para \"{0}\".", "Cannot_find_lib_definition_for_0_Did_you_mean_1_2727": "No se encuentra la definición lib para \"{0}\". ¿Quiso decir \"{1}\"?", "Cannot_find_module_0_Consider_using_resolveJsonModule_to_import_module_with_json_extension_2732": "No se encuentra el módulo \"{0}\". Considere la posibilidad de usar \"--resolveJsonModule\" para importar el módulo con la extensión \".json\".", "Cannot_find_module_0_Did_you_mean_to_set_the_moduleResolution_option_to_nodenext_or_to_add_aliases_t_2792": "No se encuentra el módulo “{0}”. ¿Pretendía establecer la opción “moduleResolution” en “nodenext” o agregar alias a la opción “paths”?", "Cannot_find_module_0_or_its_corresponding_type_declarations_2307": "No se encuentra el módulo \"{0}\" ni sus declaraciones de tipos correspondientes.", "Cannot_find_name_0_2304": "No se encuentra el nombre '{0}'.", "Cannot_find_name_0_Did_you_mean_1_2552": "No se encuentra el nombre \"{0}\". ¿Quería decir \"{1}\"?", "Cannot_find_name_0_Did_you_mean_the_instance_member_this_0_2663": "No se encuentra el nombre '{0}'. ¿Quería decir el miembro de instancia 'this.{0}'?", "Cannot_find_name_0_Did_you_mean_the_static_member_1_0_2662": "No se encuentra el nombre '{0}'. ¿Quería decir el miembro estático '{1}.{0}'?", "Cannot_find_name_0_Did_you_mean_to_write_this_in_an_async_function_2311": "No se puede encontrar el nombre \"{0}\". ¿Ha querido escribir esto en una función asincrónica?", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2583": "No se encuentra el nombre \"{0}\". ¿Necesita cambiar la biblioteca de destino? Pruebe a cambiar la opción del compilador \"lib\" a \"{1}\" o posterior.", "Cannot_find_name_0_Do_you_need_to_change_your_target_library_Try_changing_the_lib_compiler_option_to_2584": "No se encuentra el nombre \"{0}\". ¿Necesita cambiar la biblioteca de destino? Pruebe a cambiar la opción del compilador \"lib\" para incluir \"dom\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2867": "No se encuentra el nombre '{0}'. ¿Necesita instalar definiciones de tipo para Bun? Pruebe `npm i --save-dev @types/bun`.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_Bun_Try_npm_i_save_dev_types_Slashbun_2868": "No se encuentra el nombre '{0}'. ¿Necesita instalar definiciones de tipo para Bun? Pruebe \"npm i --save-dev @types/bun\" y agregue \"bun\" al campo de tipos de tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2582": "No se encuentra el nombre \"{0}\". ¿Necesita instalar definiciones de tipo para un ejecutor de pruebas? Pruebe \"npm i --save-dev @types/jest\" o \"npm i --save-dev @types/mocha\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_a_test_runner_Try_npm_i_save_dev_type_2593": "No se encuentra el nombre \"{0}\". ¿Necesita instalar definiciones de tipo para un test runner? Pruebe \"npm i --save-dev @types/jest\" o \"npm i --save-dev @types/mocha\" y, a continuación, agregue \"jest\" o \"mocha\" al campo de tipos del archivo tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2581": "No se encuentra el nombre \"{0}\". ¿Necesita instalar definiciones de tipo para jQuery? Pruebe \"npm i --save-dev @types/jquery\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_jQuery_Try_npm_i_save_dev_types_Slash_2592": "No se encuentra el nombre \"{0}\". ¿Necesita instalar definiciones de tipo para jQuery? Pruebe \"npm i --save-dev @types/jquery\" y, a continuación, agregue \"jquery\" al campo de tipos del archivo tsconfig.", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2580": "No se encuentra el nombre \"{0}\". ¿Necesita instalar definiciones de tipo para el nodo? Pruebe \"npm i --save-dev @types/node\".", "Cannot_find_name_0_Do_you_need_to_install_type_definitions_for_node_Try_npm_i_save_dev_types_Slashno_2591": "No se encuentra el nombre \"{0}\". ¿Necesita instalar definiciones de tipo para el nodo? Pruebe \"npm i --save-dev @types/node\" y, a continuación, agregue \"node\" al campo de tipos del archivo tsconfig.", "Cannot_find_namespace_0_2503": "No se encuentra el espacio de nombres '{0}'.", "Cannot_find_namespace_0_Did_you_mean_1_2833": "No se encuentra el espacio de nombres \"{0}\". ¿Quería decir \"{1}\"?", "Cannot_find_parameter_0_1225": "No se encuentra el parámetro '{0}'.", "Cannot_find_the_common_subdirectory_path_for_the_input_files_5009": "No se encuentra la ruta de acceso de subdirectorio común para los archivos de entrada.", "Cannot_find_type_definition_file_for_0_2688": "No se puede encontrar el archivo de definición de tipo para '{0}'.", "Cannot_import_type_declaration_files_Consider_importing_0_instead_of_1_6137": "No se pueden importar archivos de declaración de tipos. Considere importar \"{0}\" en lugar de \"{1}\".", "Cannot_initialize_outer_scoped_variable_0_in_the_same_scope_as_block_scoped_declaration_1_2481": "No se puede inicializar la variable '{0}' de ámbito externo en el mismo ámbito que la declaración '{1}' con ámbito de bloque.", "Cannot_invoke_an_object_which_is_possibly_null_2721": "No se puede invocar un objeto que es posiblemente \"null\".", "Cannot_invoke_an_object_which_is_possibly_null_or_undefined_2723": "No se puede invocar un objeto que es posiblemente \"null\" o \"no definido\".", "Cannot_invoke_an_object_which_is_possibly_undefined_2722": "No se puede invocar un objeto que es posiblemente \"no definido\".", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_destructuring__2765": "No se puede iterar el valor porque el método \"next\" de su iterador espera el tipo \"{1}\", pero la desestructuración de matriz siempre enviará \"{0}\".", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_array_spread_will_al_2764": "No se puede iterar el valor porque el método \"next\" de su iterador espera el tipo \"{1}\", pero la propagación de matriz siempre enviará \"{0}\".", "Cannot_iterate_value_because_the_next_method_of_its_iterator_expects_type_1_but_for_of_will_always_s_2763": "No se puede iterar el valor porque el método \"next\" de su iterador espera el tipo \"{1}\", pero for-of siempre enviará \"{0}\".", "Cannot_move_statements_to_the_selected_file_95183": "No se pueden mover instrucciones al archivo seleccionado", "Cannot_move_to_file_selected_file_is_invalid_95179": "No se puede mover al archivo, el archivo seleccionado no es válido", "Cannot_read_file_0_5083": "No se puede leer el archivo \"{0}\".", "Cannot_read_file_0_Colon_1_5012": "No se puede leer el archivo \"{0}\": {1}.", "Cannot_redeclare_block_scoped_variable_0_2451": "No se puede volver a declarar la variable con ámbito de bloque '{0}'.", "Cannot_redeclare_exported_variable_0_2323": "No se puede volver a declarar la variable '{0}' exportada.", "Cannot_redeclare_identifier_0_in_catch_clause_2492": "No se puede volver a declarar el identificador \"{0}\" en la cláusula catch.", "Cannot_start_a_function_call_in_a_type_annotation_1441": "No se puede iniciar una llamada de función en una anotación de tipo.", "Cannot_use_JSX_unless_the_jsx_flag_is_provided_17004": "JSX no se puede usar si no se proporciona la marca \"--jsx\".", "Cannot_use_export_import_on_a_type_or_type_only_namespace_when_0_is_enabled_1269": "No se puede usar 'export import' en un tipo o espacio de nombres de solo tipo cuando '{0}' está habilitado.", "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148": "No se pueden usar importaciones, exportaciones o aumentos de módulos si el valor de \"--module\" es \"none\".", "Cannot_use_namespace_0_as_a_type_2709": "No se puede utilizar el espacio de nombres '{0}' como un tipo.", "Cannot_use_namespace_0_as_a_value_2708": "No se puede utilizar el espacio de nombres '{0}' como un valor.", "Cannot_use_this_in_a_static_property_initializer_of_a_decorated_class_2816": "No se puede usar 'this' en un inicializador de propiedad estática de una clase decorada.", "Cannot_write_file_0_because_it_will_overwrite_tsbuildinfo_file_generated_by_referenced_project_1_6377": "No se puede escribir en el archivo \"{0}\" porque este sobrescribirá el archivo \".tsbuildinfo\" que el proyecto \"{1}\" al que se hace referencia ha generado.", "Cannot_write_file_0_because_it_would_be_overwritten_by_multiple_input_files_5056": "No se puede escribir en el archivo '{0}' porque se sobrescribiría con varios archivos de entrada.", "Cannot_write_file_0_because_it_would_overwrite_input_file_5055": "No se puede escribir en el archivo '{0}' porque sobrescribiría el archivo de entrada.", "Catch_clause_variable_cannot_have_an_initializer_1197": "La variable de la cláusula catch no puede tener un inicializador.", "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196": "La anotación de tipo de variable de la cláusula catch debe ser \"any\" o \"unknown\" si se especifica.", "Change_0_to_1_90014": "Cambiar \"{0}\" a \"{1}\"", "Change_all_extended_interfaces_to_implements_95038": "Cambiar todas las interfaces mejoradas a \"implements\"", "Change_all_jsdoc_style_types_to_TypeScript_95030": "Cambiar todos los tipos de jsdoc-style a TypeScript", "Change_all_jsdoc_style_types_to_TypeScript_and_add_undefined_to_nullable_types_95031": "Cambiar todos los tipos de jsdoc-style a TypeScript (y agregar \"| undefined\" a los tipos que aceptan valores NULL)", "Change_extends_to_implements_90003": "Cambiar \"extends\" a \"implements\"", "Change_spelling_to_0_90022": "Cambiar la ortografía a \"{0}\"", "Check_for_class_properties_that_are_declared_but_not_set_in_the_constructor_6700": "Compruebe las propiedades de clase declaradas pero no establecidas en el constructor.", "Check_side_effect_imports_6806": "Compruebe las importaciones de efectos secundarios.", "Check_that_the_arguments_for_bind_call_and_apply_methods_match_the_original_function_6697": "Compruebe que los argumentos de los métodos 'bind', 'call' y 'apply' coinciden con la función original.", "Checking_if_0_is_the_longest_matching_prefix_for_1_2_6104": "Comprobando si '{0}' es el prefijo coincidente más largo para '{1}' - '{2}'.", "Circular_definition_of_import_alias_0_2303": "Definición circular del alias de importación '{0}'.", "Circularity_detected_while_resolving_configuration_Colon_0_18000": "Se detectó circularidad al resolver la configuración: {0}", "Circularity_originates_in_type_at_this_location_2751": "La circularidad se origina en el tipo de esta ubicación.", "Class_0_defines_instance_member_accessor_1_but_extended_class_2_defines_it_as_instance_member_functi_2426": "La clase '{0}' define el descriptor de acceso del miembro de instancia como '{1}', pero la clase extendida '{2}' lo define como función miembro de instancia.", "Class_0_defines_instance_member_function_1_but_extended_class_2_defines_it_as_instance_member_access_2423": "La clase '{0}' define la función miembro de instancia como '{1}', pero la clase extendida '{2}' la define como descriptor de acceso de miembro de instancia.", "Class_0_defines_instance_member_property_1_but_extended_class_2_defines_it_as_instance_member_functi_2425": "La clase '{0}' define la propiedad de miembro de instancia como '{1}', pero la clase extendida '{2}' la define como función miembro de instancia.", "Class_0_incorrectly_extends_base_class_1_2415": "La clase '{0}' extiende la clase base '{1}' de forma incorrecta.", "Class_0_incorrectly_implements_class_1_Did_you_mean_to_extend_1_and_inherit_its_members_as_a_subclas_2720": "La clase \"{0}\" no implementa correctamente la clase \"{1}\". ¿Pretendía extender \"{1}\" y heredar sus miembros como una subclase?", "Class_0_incorrectly_implements_interface_1_2420": "La clase '{0}' implementa la interfaz '{1}' de forma incorrecta.", "Class_0_used_before_its_declaration_2449": "Se ha usado la clase \"{0}\" antes de declararla.", "Class_constructor_may_not_be_a_generator_1368": "El constructor de clase no puede ser un generador.", "Class_constructor_may_not_be_an_accessor_1341": "El constructor de clase no puede ser un descriptor de acceso.", "Class_declaration_cannot_implement_overload_list_for_0_2813": "La declaración de clase no puede implementar la lista de sobrecarga para '{0}'.", "Class_declarations_cannot_have_more_than_one_augments_or_extends_tag_8025": "Las declaraciones de clase no pueden tener más de una etiqueta \"@augments\" o \"@extends\".", "Class_decorators_can_t_be_used_with_static_private_identifier_Consider_removing_the_experimental_dec_18036": "Los elementos Decorator de una clase no se pueden usar con un identificador privado estático. Pruebe a quitar el elemento Decorator experimental.", "Class_field_0_defined_by_the_parent_class_is_not_accessible_in_the_child_class_via_super_2855": "El campo de clase '{0}' definido por la clase primaria no es accesible en la clase secundaria a través de super.", "Class_name_cannot_be_0_2414": "El nombre de la clase no puede ser \"{0}\".", "Class_name_cannot_be_Object_when_targeting_ES5_with_module_0_2725": "El nombre de clase no puede ser \"Object\" cuando el destino es ES5 con un módulo {0}.", "Class_static_side_0_incorrectly_extends_base_class_static_side_1_2417": "El lado estático de la clase '{0}' extiende el lado estático de la clase base '{1}' de forma incorrecta.", "Classes_can_only_extend_a_single_class_1174": "Las clases solo pueden extender una clase única.", "Classes_may_not_have_a_field_named_constructor_18006": "Las clases no pueden tener un campo denominado \"constructor\".", "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210": "El código contenido en una clase se calcula en el modo STRICT de JavaScript que no permite este uso de '{0}'. Para obtener más información, consulte https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode.", "Command_line_Options_6171": "Opciones de la línea de comandos", "Compile_the_project_given_the_path_to_its_configuration_file_or_to_a_folder_with_a_tsconfig_json_6020": "Compila el proyecto teniendo en cuenta la ruta de acceso a su archivo de configuración o a una carpeta con un archivo \"tsconfig.json\".", "Compiler_Diagnostics_6251": "Diagnóstico del compilador", "Compiler_option_0_cannot_be_given_an_empty_string_18051": "No se puede proporcionar una cadena vacía a la opción del compilador '{0}'.", "Compiler_option_0_expects_an_argument_6044": "La opción '{0}' del compilador espera un argumento.", "Compiler_option_0_may_not_be_used_with_build_5094": "La opción \"--{0}\" del compilador no se puede usar con \"--build\".", "Compiler_option_0_may_only_be_used_with_build_5093": "La opción \"--{0}\" del compilador solo se puede usar con \"--build\".", "Compiler_option_0_of_value_1_is_unstable_Use_nightly_TypeScript_to_silence_this_error_Try_updating_w_4124": "La opción del compilador \"{0}\" del valor \"{1}\" es inestable. Use TypeScript nocturno para silenciar este error. Intente actualizar con \"npm install -D typescript@next\".", "Compiler_option_0_requires_a_value_of_type_1_5024": "La opción '{0}' del compilador requiere un valor de tipo {1}.", "Compiler_reserves_name_0_when_emitting_private_identifier_downlevel_18027": "El compilador reserva el nombre \"{0}\" al emitir un identificador privado válido para versiones anteriores.", "Compiles_the_TypeScript_project_located_at_the_specified_path_6927": "Compila el proyecto TypeScript ubicado en la ruta de acceso especificada.", "Compiles_the_current_project_tsconfig_json_in_the_working_directory_6923": "Compila el proyecto actual (tsconfig.json en el directorio de trabajo).", "Compiles_the_current_project_with_additional_settings_6929": "Compila el proyecto actual con opciones de configuración adicionales", "Completeness_6257": "Integridad", "Composite_projects_may_not_disable_declaration_emit_6304": "Los proyectos compuestos no pueden deshabilitar la emisión de declaración.", "Composite_projects_may_not_disable_incremental_compilation_6379": "Los proyectos compuestos no pueden deshabilitar la compilación incremental.", "Computed_from_the_list_of_input_files_6911": "Calculado a partir de la lista de archivos de entrada", "Computed_properties_must_be_number_or_string_literals_variables_or_dotted_expressions_with_isolatedD_9014": "Las propiedades calculadas deben ser literales de número o cadena, variables o expresiones de puntos con --isolatedDeclarations.", "Computed_property_names_are_not_allowed_in_enums_1164": "No se permiten nombres de propiedad calculada en las enumeraciones.", "Computed_property_names_on_class_or_object_literals_cannot_be_inferred_with_isolatedDeclarations_9038": "Los nombres de propiedad calculada en literales de clase u objeto no se pueden inferir con --isolatedDeclarations.", "Computed_values_are_not_permitted_in_an_enum_with_string_valued_members_2553": "No se permiten valores calculados en una enumeración que tiene miembros con valores de cadena.", "Concatenate_and_emit_output_to_single_file_6001": "Concatenar y emitir la salida en un único archivo.", "Conditions_to_set_in_addition_to_the_resolver_specific_defaults_when_resolving_imports_6410": "Condiciones que se establecerán además de los valores predeterminados específicos de la resolución al resolver las importaciones.", "Conflicts_are_in_this_file_6201": "Hay conflictos en este archivo.", "Consider_adding_a_declare_modifier_to_this_class_6506": "Considere agregar un modificador 'declare' a esta clase.", "Construct_signature_return_types_0_and_1_are_incompatible_2203": "Los tipos de valor devuelto de la signatura de construcción \"{0}\" y \"{1}\" son incompatibles.", "Construct_signature_which_lacks_return_type_annotation_implicitly_has_an_any_return_type_7013": "La signatura de construcción, que carece de una anotación de tipo de valor devuelto, tiene implícitamente un tipo de valor devuelto \"any\".", "Construct_signatures_with_no_arguments_have_incompatible_return_types_0_and_1_2205": "Las signaturas de construcción sin argumentos tienen los tipos de valor devuelto \"{0}\" y \"{1}\" no compatibles.", "Constructor_implementation_is_missing_2390": "Falta la implementación del constructor.", "Constructor_of_class_0_is_private_and_only_accessible_within_the_class_declaration_2673": "El constructor de la clase '{0}' es privado y solo es accesible desde la declaración de la clase.", "Constructor_of_class_0_is_protected_and_only_accessible_within_the_class_declaration_2674": "El constructor de la clase '{0}' está protegido y solo es accesible desde la declaración de la clase.", "Constructor_type_notation_must_be_parenthesized_when_used_in_a_union_type_1386": "La notación de tipo de constructor debe incluirse entre paréntesis cuando se use en un tipo de unión.", "Constructor_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1388": "La notación de tipo de constructor debe incluirse entre paréntesis cuando se use en un tipo de intersección.", "Constructors_for_derived_classes_must_contain_a_super_call_2377": "Los constructores de las clases derivadas deben contener una llamada a \"super\".", "Containing_file_is_not_specified_and_root_directory_cannot_be_determined_skipping_lookup_in_node_mod_6126": "El archivo contenedor no se ha especificado y no se puede determinar el directorio raíz. Se omitirá la búsqueda en la carpeta 'node_modules'.", "Containing_function_is_not_an_arrow_function_95128": "La función contenedora no es una función de flecha", "Control_what_method_is_used_to_detect_module_format_JS_files_1475": "Controlar qué método se usa para detectar archivos JS con formato de módulo.", "Conversion_of_type_0_to_type_1_may_be_a_mistake_because_neither_type_sufficiently_overlaps_with_the__2352": "La conversión del tipo \"{0}\" al tipo \"{1}\" puede ser un error, porque ninguno de los tipos se superpone suficientemente al otro. Si esto era intencionado, convierta primero la expresión en \"unknown\".", "Convert_0_to_1_in_0_95003": "Convertir \"{0}\" a \"{1} en \"{0}\"", "Convert_0_to_mapped_object_type_95055": "Convertir \"{0}\" en el tipo de objeto asignado", "Convert_all_const_to_let_95102": "Convertir todo \"const\" en \"let\"", "Convert_all_constructor_functions_to_classes_95045": "Convertir todas las funciones de constructor en clases", "Convert_all_invalid_characters_to_HTML_entity_code_95101": "Convertir todos los caracteres no válidos al código de entidad HTML", "Convert_all_re_exported_types_to_type_only_exports_1365": "Convertir todos los tipos reexportados en exportaciones solo de tipo", "Convert_all_require_to_import_95048": "Convertir todas las repeticiones de \"require\" en \"import\"", "Convert_all_to_async_functions_95066": "Convertir todo en funciones asincrónicas", "Convert_all_to_bigint_numeric_literals_95092": "Convertir todo en literales numéricos bigint", "Convert_all_to_default_imports_95035": "Convertir todo en importaciones predeterminadas", "Convert_all_type_literals_to_mapped_type_95021": "Convertir todos los literales de tipo en un tipo asignado", "Convert_all_typedef_to_TypeScript_types_95177": "Convertir typedef en todos los tipos TypeScript.", "Convert_arrow_function_or_function_expression_95122": "Convertir una función de flecha o una expresión de función", "Convert_const_to_let_95093": "Convertir \"const\" en \"let\"", "Convert_default_export_to_named_export_95061": "Convertir una exportación predeterminada en exportación con nombre", "Convert_function_declaration_0_to_arrow_function_95106": "Convertir la declaración de función \"{0}\" en función de flecha", "Convert_function_expression_0_to_arrow_function_95105": "Convertir la expresión de función \"{0}\" en función de flecha", "Convert_function_to_an_ES2015_class_95001": "Convertir la función en una clase ES2015", "Convert_invalid_character_to_its_html_entity_code_95100": "Convertir un carácter no válido a su código de entidad HTML", "Convert_named_export_to_default_export_95062": "Convertir una exportación con nombre en exportación predeterminada", "Convert_named_imports_to_default_import_95170": "Convertir importaciones con nombre en importación predeterminada", "Convert_named_imports_to_namespace_import_95057": "Convertir importaciones con nombre en una importación de espacio de nombres", "Convert_namespace_import_to_named_imports_95056": "Convertir una importación de espacio de nombres en importaciones con nombre", "Convert_overload_list_to_single_signature_95118": "Convertir lista de sobrecargas en firma única", "Convert_parameters_to_destructured_object_95075": "Convertir los parámetros en un objeto desestructurado", "Convert_require_to_import_95047": "Convertir \"require\" en \"import\"", "Convert_to_ES_module_95017": "Convertir en módulo ES", "Convert_to_a_bigint_numeric_literal_95091": "Convertir en un literal numérico bigint", "Convert_to_anonymous_function_95123": "Convertir en función anónima", "Convert_to_arrow_function_95125": "Convertir en función de flecha", "Convert_to_async_function_95065": "Convertir en función asincrónica", "Convert_to_default_import_95013": "Convertir en importación predeterminada", "Convert_to_named_function_95124": "Convertir en función con nombre", "Convert_to_optional_chain_expression_95139": "Convertir en expresión de cadena opcional", "Convert_to_template_string_95096": "Convertir en cadena de plantilla", "Convert_to_type_only_export_1364": "Convertir en exportación solo de tipo", "Convert_typedef_to_TypeScript_type_95176": "Convertir typedef en tipo TypeScript.", "Corrupted_locale_file_0_6051": "Archivo de configuración regional {0} dañado.", "Could_not_convert_to_anonymous_function_95153": "No se puede convertir a una función anónima", "Could_not_convert_to_arrow_function_95151": "No se puede convertir a una función de flecha", "Could_not_convert_to_named_function_95152": "No se puede convertir a una función con nombre", "Could_not_determine_function_return_type_95150": "No se puede determinar el tipo de valor devuelto de la función", "Could_not_find_a_containing_arrow_function_95127": "No se pudo encontrar una función de flecha contenedora", "Could_not_find_a_declaration_file_for_module_0_1_implicitly_has_an_any_type_7016": "No se encontró ningún archivo de declaración para el módulo '{0}'. '{1}' tiene un tipo \"any\" de forma implícita.", "Could_not_find_convertible_access_expression_95140": "No se encontró la expresión de acceso convertible.", "Could_not_find_export_statement_95129": "No se pudo encontrar la instrucción export", "Could_not_find_import_clause_95131": "No se pudo encontrar la cláusula import", "Could_not_find_matching_access_expressions_95141": "No se encontraron expresiones de acceso coincidentes.", "Could_not_find_name_0_Did_you_mean_1_2570": "No se ha encontrado el nombre \"{0}\". ¿Quiso decir \"{1}\"?", "Could_not_find_namespace_import_or_named_imports_95132": "No se pudo encontrar la importación del espacio de nombres ni las importaciones con nombre", "Could_not_find_property_for_which_to_generate_accessor_95135": "No se pudo encontrar la propiedad para la que se debe generar el descriptor de acceso", "Could_not_find_variable_to_inline_95185": "No se pudo encontrar la variable para insertar.", "Could_not_resolve_the_path_0_with_the_extensions_Colon_1_6231": "No se pudo resolver la ruta de acceso \"{0}\" con las extensiones: {1}.", "Could_not_write_file_0_Colon_1_5033": "No se puede escribir en el archivo \"{0}\": \"{1}\".", "Create_source_map_files_for_emitted_JavaScript_files_6694": "Cree archivos de mapa de origen para los archivos JavaScript emitidos.", "Create_sourcemaps_for_d_ts_files_6614": "Cree mapas de origen para archivos d.ts.", "Creates_a_tsconfig_json_with_the_recommended_settings_in_the_working_directory_6926": "Crea un archivo tsconfig.json con la configuración recomendada en el directorio de trabajo.", "DIRECTORY_6038": "DIRECTORIO", "Decimal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_1537": "No se permiten secuencias de escape decimales ni referencias inversas en una clase de caracteres.", "Decimals_with_leading_zeros_are_not_allowed_1489": "No se permiten decimales con ceros iniciales.", "Declaration_augments_declaration_in_another_file_This_cannot_be_serialized_6232": "La declaración aumenta una declaración en otro archivo. Esta operación no se puede serializar.", "Declaration_emit_for_this_file_requires_preserving_this_import_for_augmentations_This_is_not_support_9026": "La emisión de declaración para este archivo requiere conservar esta importación para aumentos. Esto no se admite con --isolatedDeclarations.", "Declaration_emit_for_this_file_requires_using_private_name_0_An_explicit_type_annotation_may_unblock_9005": "La emisión de declaración para este archivo requiere el uso del nombre privado \"{0}\". Una anotación de tipo explícito puede desbloquear la emisión de declaración.", "Declaration_emit_for_this_file_requires_using_private_name_0_from_module_1_An_explicit_type_annotati_9006": "La emisión de declaración para este archivo requiere el uso del nombre privado \"{0}\" del módulo \"{1}\". Una anotación de tipo explícito puede desbloquear la emisión de declaración.", "Declaration_emit_for_this_parameter_requires_implicitly_adding_undefined_to_its_type_This_is_not_sup_9025": "La emisión de declaración para este parámetro requiere agregar implícitamente un elemento no definido a su tipo. Esto no se admite con --isolatedDeclarations.", "Declaration_expected_1146": "Se esperaba una declaración.", "Declaration_name_conflicts_with_built_in_global_identifier_0_2397": "Conflictos entre nombres de declaración con el identificador global '{0}' integrado.", "Declaration_or_statement_expected_1128": "Se esperaba una declaración o una instrucción.", "Declaration_or_statement_expected_This_follows_a_block_of_statements_so_if_you_intended_to_write_a_d_2809": "Se esperaba una declaración o una instrucción. El elemento \"=\" sigue a un bloque de instrucciones por lo que, si pretendía escribir una asignación de desestructuración, puede que sea necesario incluir toda la asignación entre paréntesis.", "Declarations_with_definite_assignment_assertions_must_also_have_type_annotations_1264": "Las declaraciones con aserciones de asignación definitiva deben tener también anotaciones de tipo.", "Declarations_with_initializers_cannot_also_have_definite_assignment_assertions_1263": "Las declaraciones con inicializadores no pueden tener también aserciones de asignación definitiva.", "Declare_a_private_field_named_0_90053": "Declare un campo privado denominado \"{0}\".", "Declare_method_0_90023": "<PERSON><PERSON>ar el método \"{0}\"", "Declare_private_method_0_90038": "Declarar el método \"{0}\" privado", "Declare_private_property_0_90035": "<PERSON><PERSON>ar la propiedad \"{0}\" privada", "Declare_property_0_90016": "<PERSON><PERSON><PERSON> la propiedad \"{0}\"", "Declare_static_method_0_90024": "Declarar el método estático \"{0}\"", "Declare_static_property_0_90027": "<PERSON><PERSON>ar la propiedad estática \"{0}\"", "Decorator_function_return_type_0_is_not_assignable_to_type_1_1270": "El tipo de valor devuelto de la función Decorator \"{0}\" no se puede asignar al tipo \"{1}\".", "Decorator_function_return_type_is_0_but_is_expected_to_be_void_or_any_1271": "El tipo de valor devuelto de la función Decorator es \"{0}\" pero se espera que sea \"void\" o \"any\".", "Decorator_used_before_export_here_1486": "El elemento Decorator se usa antes de \"exportar\" aquí.", "Decorators_are_not_valid_here_1206": "Los elementos Decorator no son válidos aquí.", "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207": "No se pueden aplicar elementos Decorator a varios descriptores de acceso get o set con el mismo nombre.", "Decorators_may_not_appear_after_export_or_export_default_if_they_also_appear_before_export_8038": "Los elementos Decorator no pueden aparecer después de \"export\" o \"export default\" si también aparecen antes de \"export\".", "Decorators_must_precede_the_name_and_all_keywords_of_property_declarations_1436": "Los decoradores deben preceder al nombre y a todas las palabras clave de las declaraciones de propiedad.", "Default_catch_clause_variables_as_unknown_instead_of_any_6803": "Variables de cláusula catch predeterminadas como \"unknown\" en lugar de \"any\".", "Default_export_of_the_module_has_or_is_using_private_name_0_4082": "La exportación predeterminada del módulo tiene o usa el nombre privado '{0}'.", "Default_exports_can_t_be_inferred_with_isolatedDeclarations_9037": "Las exportaciones predeterminadas no se pueden inferir con --isolatedDeclarations.", "Default_library_1424": "Biblioteca predeterminada", "Default_library_for_target_0_1425": "Biblioteca predeterminada para el destino \"{0}\"", "Definitions_of_the_following_identifiers_conflict_with_those_in_another_file_Colon_0_6200": "Las definiciones de los identificadores siguientes entran en conflicto con las de otro archivo: {0}", "Delete_all_unused_declarations_95024": "Eliminar todas las declaraciones sin usar", "Delete_all_unused_imports_95147": "Eliminar todas las importaciones sin usar", "Delete_all_unused_param_tags_95172": "Eliminar todas las etiquetas \"@param\" sin usar", "Delete_the_outputs_of_all_projects_6365": "Eliminar las salidas de todos los proyectos.", "Delete_unused_param_tag_0_95171": "Eliminar la etiqueta \"@param\" sin usar \"{0}\"", "Deprecated_Use_jsxFactory_instead_Specify_the_object_invoked_for_createElement_when_targeting_react__6084": "[En desuso] Use \"--jsxFactory\" en su lugar. Especifique el objeto invocado para createElement cuando el destino sea la emisión de JSX \"react\"", "Deprecated_Use_outFile_instead_Concatenate_and_emit_output_to_single_file_6170": "[En desuso] Use \"--outFile\" en su lugar. Concatena y emite la salida en un solo archivo.", "Deprecated_Use_skipLibCheck_instead_Skip_type_checking_of_default_library_declaration_files_6160": "[En desuso] Use \"--skipLib<PERSON>heck\" en su lugar. Omite la comprobación de tipos de los archivos de declaración de biblioteca predeterminados.", "Deprecated_setting_Use_outFile_instead_6677": "Valor en desuso. Use \"outFile\" en su lugar.", "Did_you_forget_to_use_await_2773": "¿Olvidó usar \"await\"?", "Did_you_mean_0_1369": "¿Quiso decir \"{0}\"?", "Did_you_mean_for_0_to_be_constrained_to_type_new_args_Colon_any_1_2735": "¿Quiso decir que \"{0}\" se restrinja al tipo \"new (...args: any[]) => {1}\"?", "Did_you_mean_to_call_this_expression_6212": "¿Pretendía llamar a esta expresión?", "Did_you_mean_to_mark_this_function_as_async_1356": "¿Pretendía marcar esta función como \"async\"?", "Did_you_mean_to_use_a_Colon_An_can_only_follow_a_property_name_when_the_containing_object_literal_is_1312": "¿Pretendía usar \":\"? El símbolo \"=\" solo puede seguir a un nombre de propiedad cuando el literal de objeto contenedor forma parte de un patrón de desestructuración.", "Did_you_mean_to_use_new_with_this_expression_6213": "¿Pretendía usar \"new\" con esta expresión?", "Digit_expected_1124": "Se esperaba un dígito.", "Directory_0_does_not_exist_skipping_all_lookups_in_it_6148": "El directorio \"{0}\" no existe, se omitirán todas las búsquedas en él.", "Directory_0_has_no_containing_package_json_scope_Imports_will_not_resolve_6270": "El directorio \"{0}\" no tiene ningún ámbito que contenga package.json. Las importaciones no se resolverán.", "Disable_adding_use_strict_directives_in_emitted_JavaScript_files_6669": "Deshabilite la adición de directivas \"use strict\" en archivos JavaScript emitidos.", "Disable_checking_for_this_file_90018": "Deshabilitar la comprobación para este archivo", "Disable_emitting_comments_6688": "Deshabilite el poder escribir comentarios.", "Disable_emitting_declarations_that_have_internal_in_their_JSDoc_comments_6701": "Deshabilite la emisión de declaraciones que tienen \"@internal\" en los comentarios de JSDoc.", "Disable_emitting_files_from_a_compilation_6660": "Deshabilita la emisión de archivos de una compilación.", "Disable_emitting_files_if_any_type_checking_errors_are_reported_6662": "Deshabilite la emisión de archivos si se informa algún error de comprobación de tipos.", "Disable_erasing_const_enum_declarations_in_generated_code_6682": "Deshabilite el borrado de declaraciones \"enumeración const\" en el código generado.", "Disable_error_reporting_for_unreachable_code_6603": "Deshabilite los informes de errores para los códigos inaccesibles.", "Disable_error_reporting_for_unused_labels_6604": "Deshabilite los informes de errores para etiquetas sin usar.", "Disable_full_type_checking_only_critical_parse_and_emit_errors_will_be_reported_6805": "Deshabilitar la comprobación completa de tipos (solo se notificarán los errores críticos de análisis y emisión).", "Disable_generating_custom_helper_functions_like_extends_in_compiled_output_6661": "Deshabilite la generación de funciones auxiliares personalizadas como \"__extends\" en la salida compilada.", "Disable_including_any_library_files_including_the_default_lib_d_ts_6670": "Deshabilite la inclusión de cualquier archivo de biblioteca, incluido el archivo predeterminado lib.d.ts.", "Disable_loading_referenced_projects_6235": "Deshabilite la carga de proyectos a los que se hace referencia.", "Disable_preferring_source_files_instead_of_declaration_files_when_referencing_composite_projects_6620": "Deshabilite la preferencia de archivos de código fuente en lugar de archivos de declaración cuando haga referencia a proyectos compuestos.", "Disable_reporting_of_excess_property_errors_during_the_creation_of_object_literals_6702": "Deshabilite la creación de informes de errores de exceso de propiedad durante la creación de literales de objetos.", "Disable_resolving_symlinks_to_their_realpath_This_correlates_to_the_same_flag_in_node_6683": "Deshabilite la resolución de symlink a su realpath. Se corresponde con la misma marca en el nodo.", "Disable_size_limitations_on_JavaScript_projects_6162": "Deshabilitar los límites de tamaño de proyectos de JavaScript.", "Disable_solution_searching_for_this_project_6224": "Deshabilite la búsqueda de la solución para este proyecto.", "Disable_strict_checking_of_generic_signatures_in_function_types_6673": "Deshabilite la comprobación estricta de firmas genéricas en tipos de función.", "Disable_the_type_acquisition_for_JavaScript_projects_6625": "Deshabilitar el tipo de adquisición para proyectos de JavaScript", "Disable_truncating_types_in_error_messages_6663": "Deshabilite los tipos truncados en los mensajes de error.", "Disable_use_of_source_files_instead_of_declaration_files_from_referenced_projects_6221": "Deshabilite el uso de los archivos de código fuente en lugar de los archivos de declaración de los proyectos a los que se hace referencia.", "Disable_wiping_the_console_in_watch_mode_6684": "Deshabilita la eliminación de la consola en modo inspección.", "Disables_inference_for_type_acquisition_by_looking_at_filenames_in_a_project_6616": "Deshabilite la inferencia para la adquisición de tipos consultando los nombres de los archivos de un proyecto.", "Disallow_import_s_require_s_or_reference_s_from_expanding_the_number_of_files_TypeScript_should_add__6672": "No permita que ningún \"import\", \"require\" o \"<reference>\" amplíe el número de archivos que TypeScript debe agregar a un proyecto.", "Disallow_inconsistently_cased_references_to_the_same_file_6078": "No permitir referencias al mismo archivo con un uso incoherente de mayúsculas y minúsculas.", "Do_not_add_triple_slash_references_or_imported_modules_to_the_list_of_compiled_files_6159": "No agregar módulos importados ni referencias con triple barra diagonal a la lista de archivos compilados.", "Do_not_allow_runtime_constructs_that_are_not_part_of_ECMAScript_6721": "No permitir construcciones en tiempo de ejecución que no formen parte de ECMAScript.", "Do_not_emit_comments_to_output_6009": "No emitir comentarios en la salida.", "Do_not_emit_declarations_for_code_that_has_an_internal_annotation_6056": "No emitir declaraciones para el código que tiene una anotación \"@internal\".", "Do_not_emit_outputs_6010": "No emitir salidas.", "Do_not_emit_outputs_if_any_errors_were_reported_6008": "No emitir salidas si se informa de algún error.", "Do_not_emit_use_strict_directives_in_module_output_6112": "No emitir directivas 'use strict' en la salida del módulo.", "Do_not_erase_const_enum_declarations_in_generated_code_6007": "No borrar las declaraciones de enumeración const en el código generado.", "Do_not_generate_custom_helper_functions_like_extends_in_compiled_output_6157": "No generar funciones del asistente personalizadas como \"__extends\" en la salida compilada.", "Do_not_include_the_default_library_file_lib_d_ts_6158": "No incluir el archivo de biblioteca predeterminado (lib.d.ts).", "Do_not_report_errors_on_unreachable_code_6077": "No notificar los errores del código inaccesible.", "Do_not_report_errors_on_unused_labels_6074": "No notificar los errores de las etiquetas no usadas.", "Do_not_resolve_the_real_path_of_symlinks_6013": "No resolver la ruta de acceso real de los vínculos simbólicos.", "Do_not_transform_or_elide_any_imports_or_exports_not_marked_as_type_only_ensuring_they_are_written_i_6804": "No transforme ni evite ninguna importación o exportación no marcada como solo de tipo, asegurándose de que se escriben en el formato del archivo de salida en función de la configuración \"module\".", "Do_not_truncate_error_messages_6165": "No truncar los mensajes de error.", "Duplicate_function_implementation_2393": "Implementación de función duplicada.", "Duplicate_identifier_0_2300": "Identificador '{0}' duplicado.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_2441": "Identificador '{0}' duplicado. El compilador se reserva el nombre '{1}' en el ámbito de nivel superior de un módulo.", "Duplicate_identifier_0_Compiler_reserves_name_1_in_top_level_scope_of_a_module_containing_async_func_2529": "Identificador '{0}' duplicado. El compilador reserva el nombre '{1}' en el ámbito de nivel superior de un módulo que contiene funciones asincrónicas.", "Duplicate_identifier_0_Compiler_reserves_name_1_when_emitting_super_references_in_static_initializer_2818": "Duplicar identificador \"{0}\". El compilador reserva el nombre \"{1}\" al emitir referencias \"super\" en inicializadores estáticos.", "Duplicate_identifier_0_Compiler_uses_declaration_1_to_support_async_functions_2520": "Identificador '{0}' duplicado. El compilador usa la declaración '{1}' para admitir funciones asincrónicas.", "Duplicate_identifier_0_Static_and_instance_elements_cannot_share_the_same_private_name_2804": "El identificador \"{0}\" está duplicado. Los elementos estáticos y de instancia no pueden compartir el mismo nombre privado.", "Duplicate_identifier_arguments_Compiler_uses_arguments_to_initialize_rest_parameters_2396": "Identificador \"arguments\" duplicado. El compilador usa \"arguments\" para inicializar parámetros rest.", "Duplicate_identifier_newTarget_Compiler_uses_variable_declaration_newTarget_to_capture_new_target_me_2543": "Identificador duplicado \"_newTarget\". El compilador usa la declaración de variable \"_newTarget\" para capturar la referencia de la propiedad Meta \"new.target\".", "Duplicate_identifier_this_Compiler_uses_variable_declaration_this_to_capture_this_reference_2399": "Identificador \"_this\" duplicado. El compilador usa la declaración de variable \"_this\" para capturar una referencia \"this\".", "Duplicate_index_signature_for_type_0_2374": "Signatura de índice duplicada para el tipo \"{0}\".", "Duplicate_label_0_1114": "Etiqueta \"{0}\" duplicada.", "Duplicate_property_0_2718": "Propie<PERSON> \"{0}\" duplicada.", "Duplicate_regular_expression_flag_1500": "Marca de expresión regular duplicada.", "Dynamic_import_s_specifier_must_be_of_type_string_but_here_has_type_0_7036": "El especificador de la importación dinámica debe ser de tipo \"string\", pero aquí tiene el tipo \"{0}\".", "Dynamic_imports_are_only_supported_when_the_module_flag_is_set_to_es2020_es2022_esnext_commonjs_amd__1323": "Las importaciones dinámicas solo se admiten cuando la marca \"--módulo\" está establecida en \"es2020\", \"es2022\", \"esnext\", \"commonjs\", \"amd\", \"system\", \"umd\", \"node16\", \"node18\" o \"nodenext\".", "Dynamic_imports_can_only_accept_a_module_specifier_and_an_optional_set_of_attributes_as_arguments_1450": "Las importaciones dinámicas solo pueden aceptar un especificador de módulo y un set de atributos opcional como argumentos", "Dynamic_imports_only_support_a_second_argument_when_the_module_option_is_set_to_esnext_node16_node18_1324": "Las importaciones dinámicas solo admiten un segundo argumento cuando la opción \"--module\" está establecida en \"esnext\", \"node16\", \"node18\", \"nodenext\" o \"preserve\".", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_module_is_set_to_preserve_1293": "No se permite la sintaxis ESM en un módulo CommonJS cuando \"module\" está establecido en \"preserve\".", "ESM_syntax_is_not_allowed_in_a_CommonJS_module_when_verbatimModuleSyntax_is_enabled_1286": "No se permite la sintaxis ESM en un módulo CommonJS cuando \"verbatimModuleSyntax\" está habilitado.", "Each_declaration_of_0_1_differs_in_its_value_where_2_was_expected_but_3_was_given_4125": "Cada declaración de \"{0}.{1}\" difiere en su valor, donde se esperaba '{2}' pero se proporcionó '{3}'.", "Each_member_of_the_union_type_0_has_construct_signatures_but_none_of_those_signatures_are_compatible_2762": "Cada miembro del tipo de unión \"{0}\" tiene signaturas de construcción, pero ninguna de ellas es compatible entre sí.", "Each_member_of_the_union_type_0_has_signatures_but_none_of_those_signatures_are_compatible_with_each_2758": "Cada miembro del tipo de unión \"{0}\" tiene signaturas, pero ninguna de ellas es compatible entre sí.", "Editor_Support_6249": "Compatibilidad con el editor", "Element_implicitly_has_an_any_type_because_expression_of_type_0_can_t_be_used_to_index_type_1_7053": "El elemento tiene un tipo \"any\" de forma implícita porque la expresión de tipo \"{0}\" no se puede usar para indexar el tipo \"{1}\".", "Element_implicitly_has_an_any_type_because_index_expression_is_not_of_type_number_7015": "El elemento tiene un tipo 'any' implícito porque la expresión de índice no es de tipo 'number'.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_7017": "El elemento tiene un tipo \"any\" implícito porque el tipo '{0}' no tiene signatura de índice.", "Element_implicitly_has_an_any_type_because_type_0_has_no_index_signature_Did_you_mean_to_call_1_7052": "El elemento tiene un tipo \"any\" implícito porque el tipo \"{0}\" no tiene ninguna signatura de índice. ¿Pretendía llamar a \"{1}\"?", "Emit_6246": "<PERSON><PERSON><PERSON>", "Emit_ECMAScript_standard_compliant_class_fields_6712": "Emita campos de clases compatibles con el estándar de ECMAScript.", "Emit_a_UTF_8_Byte_Order_Mark_BOM_in_the_beginning_of_output_files_6622": "Emitir una marca BOM UTF-8 al principio de los archivos de salida.", "Emit_a_single_file_with_source_maps_instead_of_having_a_separate_file_6151": "Emitir un solo archivo con mapas de origen en lugar de tener un archivo aparte.", "Emit_a_v8_CPU_profile_of_the_compiler_run_for_debugging_6638": "Emita un perfil de CPU v8 de la ejecución del compilador para la depuración.", "Emit_additional_JavaScript_to_ease_support_for_importing_CommonJS_modules_This_enables_allowSyntheti_6626": "Emita un JavaScript adicional para facilitar la importación de módulos CommonJS. Esto habilita \"allowSyntheticDefaultImports\" para la compatibilidad de tipos.", "Emit_class_fields_with_Define_instead_of_Set_6222": "Emita campos de clase con Define en lugar de Set.", "Emit_design_type_metadata_for_decorated_declarations_in_source_files_6624": "Emita metadatos de tipo de diseño para las declaraciones decoradas en los archivos de origen.", "Emit_more_compliant_but_verbose_and_less_performant_JavaScript_for_iteration_6621": "Emita un JavaScript más compatible, pero más detallado y de menor rendimiento para la iteración.", "Emit_the_source_alongside_the_sourcemaps_within_a_single_file_requires_inlineSourceMap_or_sourceMap__6152": "Emitir el origen junto a los mapas de origen en un solo archivo; requiere que se establezca \"--inlineSourceMap\" o \"--sourceMap\".", "Enable_all_strict_type_checking_options_6180": "Habilitar todas las opciones de comprobación de tipos estricta.", "Enable_color_and_formatting_in_TypeScript_s_output_to_make_compiler_errors_easier_to_read_6685": "Habilite el color y el formato en la salida de TypeScript para facilitar la lectura de los errores del compilador.", "Enable_constraints_that_allow_a_TypeScript_project_to_be_used_with_project_references_6611": "Habilite restricciones que permitan usar un proyecto TypeScript con referencias del proyecto.", "Enable_error_reporting_for_codepaths_that_do_not_explicitly_return_in_a_function_6667": "Habilite el informe de errores para rutas de código que no devuelvan explícitamente una función.", "Enable_error_reporting_for_expressions_and_declarations_with_an_implied_any_type_6665": "Habilite el informe de errores para expresiones y declaraciones con un tipo \"any\" implícito.", "Enable_error_reporting_for_fallthrough_cases_in_switch_statements_6664": "Habilite los informes de errores para los casos de fallthrough en instrucciones switch.", "Enable_error_reporting_in_type_checked_JavaScript_files_6609": "Habilite el informe de errores en los archivos JavaScript de comprobación de tipos.", "Enable_error_reporting_when_local_variables_aren_t_read_6675": "Habilite el informe de errores cuando una variable local no se lea.", "Enable_error_reporting_when_this_is_given_the_type_any_6668": "Habilite el informe de errores cuando a 'this' se le asigna el tipo 'any'.", "Enable_experimental_support_for_legacy_experimental_decorators_6630": "Habilite la compatibilidad experimental con decoradores experimentales heredados.", "Enable_importing_files_with_any_extension_provided_a_declaration_file_is_present_6264": "Habilite la importación de archivos con cualquier extensión, siempre que haya un archivo de declaración presente.", "Enable_importing_json_files_6689": "Habilite la importación de archivos .json.", "Enable_lib_replacement_6808": "Habilite el reemplazo de bibliotecas.", "Enable_project_compilation_6302": "Habilitar la compilación de proyecto", "Enable_strict_bind_call_and_apply_methods_on_functions_6214": "Habilite los métodos estrictos \"bind\", \"call\" y \"apply\" en las funciones.", "Enable_strict_checking_of_function_types_6186": "Habilite la comprobación estricta de los tipos de función.", "Enable_strict_checking_of_property_initialization_in_classes_6187": "Habilite la comprobación estricta de inicialización de propiedades en las clases.", "Enable_strict_null_checks_6113": "Habilitar comprobaciones estrictas de elementos nulos.", "Enable_the_experimentalDecorators_option_in_your_configuration_file_95074": "Habilite la opción \"experimentalDecorators\" en el archivo de configuración.", "Enable_the_jsx_flag_in_your_configuration_file_95088": "Habilite la marca \"--jsx\" en el archivo de configuración.", "Enable_tracing_of_the_name_resolution_process_6085": "Habilitar seguimiento del proceso de resolución de nombres.", "Enable_verbose_logging_6713": "Habilitar el registro detallado.", "Enables_emit_interoperability_between_CommonJS_and_ES_Modules_via_creation_of_namespace_objects_for__7037": "Permite emitir interoperabilidad entre módulos CommonJS y ES mediante la creación de objetos de espacio de nombres para todas las importaciones. Implica \"allowSyntheticDefaultImports\".", "Enables_experimental_support_for_ES7_decorators_6065": "Habilita la compatibilidad experimental con los elementos Decorator de ES7.", "Enables_experimental_support_for_emitting_type_metadata_for_decorators_6066": "Habilita la compatibilidad experimental para emitir metadatos de tipo para los elementos Decorator.", "Enforces_using_indexed_accessors_for_keys_declared_using_an_indexed_type_6671": "Exige el uso de descriptores de acceso indexados para las claves declaradas mediante un tipo indexado.", "Ensure_overriding_members_in_derived_classes_are_marked_with_an_override_modifier_6666": "Asegúrese de que al invalidar miembros en clases derivadas, estos están marcados con un modificador de invalidación.", "Ensure_that_casing_is_correct_in_imports_6637": "Verifique el uso correcto de mayúsculas y minúsculas en las importaciones.", "Ensure_that_each_file_can_be_safely_transpiled_without_relying_on_other_imports_6645": "Asegúrese de que cada archivo pueda transpilarse con seguridad sin depender de otras importaciones.", "Ensure_use_strict_is_always_emitted_6605": "Asegúrese de que siempre se emite \"use strict\".", "Entering_conditional_exports_6413": "Entrando en exportaciones condicionales.", "Entry_point_for_implicit_type_library_0_1420": "Punto de entrada para la biblioteca de tipos implícitos \"{0}\"", "Entry_point_for_implicit_type_library_0_with_packageId_1_1421": "Punto de entrada para la biblioteca de tipos implícitos \"{0}\" con el valor packageId \"{1}\"", "Entry_point_of_type_library_0_specified_in_compilerOptions_1417": "Punto de entrada de la biblioteca de tipos \"{0}\" que se especifica en compilerOptions", "Entry_point_of_type_library_0_specified_in_compilerOptions_with_packageId_1_1418": "Punto de entrada de la biblioteca de tipos \"{0}\" que se especifica en compilerOptions con el valor packageId \"{1}\"", "Enum_0_used_before_its_declaration_2450": "Se ha usado la enumeración \"{0}\" antes de declararla.", "Enum_declarations_can_only_merge_with_namespace_or_other_enum_declarations_2567": "Las declaraciones de enumeración solo se pueden combinar con otras declaraciones de enumeración o de espacio de nombres.", "Enum_declarations_must_all_be_const_or_non_const_2473": "Todas las declaraciones de enumeración deben ser de tipo const o no const.", "Enum_member_expected_1132": "Se esperaba un miembro de enumeración.", "Enum_member_following_a_non_literal_numeric_member_must_have_an_initializer_when_isolatedModules_is__18056": "El miembro de enumeración que sigue a un miembro numérico que no sea literal debe tener un inicializador cuando \"isolatedModules\" está habilitado.", "Enum_member_initializers_must_be_computable_without_references_to_external_symbols_with_isolatedDecl_9020": "Los inicializadores de miembros de enumeración deben poder calcularse sin referencias a símbolos externos con --isolatedDeclarations.", "Enum_member_must_have_initializer_1061": "El miembro de enumeración debe tener un inicializador.", "Enum_name_cannot_be_0_2431": "El nombre de la enumeración no puede ser \"{0}\".", "Errors_Files_6041": "Archivos de errores", "Escape_sequence_0_is_not_allowed_1488": "No se permite la secuencia de escape \"{0}\".", "Examples_Colon_0_6026": "Ejemplos: {0}", "Excessive_complexity_comparing_types_0_and_1_2859": "Complejidad excesiva al comparar los tipos '{0}' y '{1}'.", "Excessive_stack_depth_comparing_types_0_and_1_2321": "Profundidad excesiva de la pila al comparar los tipos '{0}' y '{1}'.", "Exiting_conditional_exports_6416": "Saliendo de las exportaciones condicionales.", "Expected_0_1_type_arguments_provide_these_with_an_extends_tag_8027": "Se esperaban argumentos de tipo {0}-{1}; proporciónelos con una etiqueta \"@extends\".", "Expected_0_arguments_but_got_1_2554": "Se esperaban {0} argumentos, pero se obtuvieron {1}.", "Expected_0_arguments_but_got_1_Did_you_forget_to_include_void_in_your_type_argument_to_Promise_2794": "Se esperaban {0} argumentos, pero se obtuvo un total de {1}. ¿Olvidó incluir \"void\" en el argumento de tipo para \"Promise\"?", "Expected_0_type_arguments_but_got_1_2558": "Se esperaban {0} argumentos de tipo, pero se obtuvieron {1}.", "Expected_0_type_arguments_provide_these_with_an_extends_tag_8026": "Se esperaban argumentos de tipo {0}; proporciónelos con una etiqueta \"@extends\".", "Expected_1_argument_but_got_0_new_Promise_needs_a_JSDoc_hint_to_produce_a_resolve_that_can_be_called_2810": "Se esperaba 1 argumento, pero se obtuvo 0. \"new Promise()\" necesita una pista de JSDoc para producir un \"resolve\" que pueda llamarse sin argumentos.", "Expected_a_Unicode_property_name_1523": "Se esperaba un nombre de propiedad Unicode.", "Expected_a_Unicode_property_name_or_value_1527": "Se esperaba un valor o un nombre de propiedad Unicode.", "Expected_a_Unicode_property_value_1525": "Se esperaba un valor de propiedad Unicode.", "Expected_a_capturing_group_name_1514": "Se esperaba un nombre de grupo de captura.", "Expected_a_class_set_operand_1520": "Se esperaba un operando de conjunto de clases.", "Expected_at_least_0_arguments_but_got_1_2555": "Se esperaban al menos {0} argumentos, pero se obtuvieron {1}.", "Expected_corresponding_JSX_closing_tag_for_0_17002": "Se esperaba la etiqueta de cierre JSX correspondiente de '{0}'.", "Expected_corresponding_closing_tag_for_JSX_fragment_17015": "Se esperaba la etiqueta de cierre correspondiente para el fragmento de JSX.", "Expected_for_property_initializer_1442": "Se esperaba '=' para el inicializador de propiedades.", "Expected_type_of_0_field_in_package_json_to_be_1_got_2_6105": "Se esperaba que el tipo del campo \"{0}\" en \"package.json\" fuese \"{1}\", pero se obtuvo \"{2}\".", "Explicitly_specified_module_resolution_kind_Colon_0_6087": "Tipo de resolución de módulo especificado de forma explícita: '{0}'.", "Exponentiation_cannot_be_performed_on_bigint_values_unless_the_target_option_is_set_to_es2016_or_lat_2791": "No se puede realizar la exponenciación en los valores \"bigint\", a menos que la opción \"target\" esté establecida en \"es2016\" o posterior.", "Export_0_from_module_1_90059": "Exportar '{0}' desde el módulo '{1}'", "Export_all_referenced_locals_90060": "Exportar todas las variables locales a las que se hace referencia", "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203": "No se puede usar una asignación de exportación cuando se eligen módulos de ECMAScript como destino. Considere la posibilidad de usar \"export default\" u otro formato de módulo en su lugar.", "Export_assignment_is_not_supported_when_module_flag_is_system_1218": "La asignación de exportación no es compatible cuando la marca \"--module\" es \"system\".", "Export_declaration_conflicts_with_exported_declaration_of_0_2484": "La declaración de exportación está en conflicto con la declaración exportada de \"{0}\".", "Export_declarations_are_not_permitted_in_a_namespace_1194": "No se permiten declaraciones de exportación en un espacio de nombres.", "Export_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6276": "El especificador de exportación \"{0}\" no existe en el ámbito package.json en la ruta de acceso \"{1}\".", "Exported_type_alias_0_has_or_is_using_private_name_1_4081": "El alias de tipo exportado '{0}' tiene o usa el nombre privado '{1}'.", "Exported_type_alias_0_has_or_is_using_private_name_1_from_module_2_4084": "El alias de tipo exportado \"{0}\" tiene o usa el nombre privado \"{1}\" del módulo {2}.", "Exported_variable_0_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4023": "La variable exportada '{0}' tiene o usa el nombre '{1}' del módulo {2} externo, pero no se puede nombrar.", "Exported_variable_0_has_or_is_using_name_1_from_private_module_2_4024": "La variable exportada '{0}' tiene o usa el nombre '{1}' del módulo '{2}' privado.", "Exported_variable_0_has_or_is_using_private_name_1_4025": "La variable exportada '{0}' tiene o usa el nombre privado '{1}'.", "Exports_and_export_assignments_are_not_permitted_in_module_augmentations_2666": "En aumentos de módulos, no se admiten exportaciones ni asignaciones de exportación.", "Expression_expected_1109": "Se esperaba una expresión.", "Expression_must_be_enclosed_in_parentheses_to_be_used_as_a_decorator_1497": "La expresión debe ir entre paréntesis para usarse como un decorador.", "Expression_or_comma_expected_1137": "Se esperaba una expresión o una coma.", "Expression_produces_a_tuple_type_that_is_too_large_to_represent_2800": "La expresión genera un tipo de tupla demasiado grande para representarlo.", "Expression_produces_a_union_type_that_is_too_complex_to_represent_2590": "La expresión genera un tipo de unión demasiado complejo para representarlo.", "Expression_resolves_to_super_that_compiler_uses_to_capture_base_class_reference_2402": "La expresión se resuelve en el valor \"_super\" que el compilador usa para capturar una referencia a la clase base.", "Expression_resolves_to_variable_declaration_newTarget_that_compiler_uses_to_capture_new_target_meta__2544": "La expresión se resuelve en una declaración de variable \"_newTarget\" que el compilador usa para capturar la referencia de la propiedad Meta \"new.target\".", "Expression_resolves_to_variable_declaration_this_that_compiler_uses_to_capture_this_reference_2400": "La expresión se resuelve en la declaración de variable \"_this\" que el compilador usa para capturar una referencia \"this\".", "Expression_type_can_t_be_inferred_with_isolatedDeclarations_9013": "No se puede inferir el tipo de expresión con --isolatedDeclarations.", "Extends_clause_can_t_contain_an_expression_with_isolatedDeclarations_9021": "La cláusula Extends no puede contener una expresión con --isolatedDeclarations.", "Extends_clause_for_inferred_type_0_has_or_is_using_private_name_1_4085": "Extiende la cláusula para el tipo deducido '{0}', tiene o usa el nombre privado '{1}'.", "Extract_base_class_to_variable_90064": "Extraer clase base en variable", "Extract_binding_expressions_to_variable_90066": "Extraer expresiones de enlace en variable", "Extract_constant_95006": "<PERSON><PERSON> la constante", "Extract_default_export_to_variable_90065": "Extraer exportación predeterminada a la variable", "Extract_function_95005": "Extraer la función", "Extract_to_0_in_1_95004": "Extraer a {0} en {1}", "Extract_to_0_in_1_scope_95008": "Extraer a {0} en el ámbito {1}", "Extract_to_0_in_enclosing_scope_95007": "Extraer a {0} en el ámbito de inclusión", "Extract_to_interface_95090": "Extraer a la interfaz", "Extract_to_type_alias_95078": "Extraer al alias de tipo", "Extract_to_typedef_95079": "Extraer a typedef", "Extract_to_variable_and_replace_with_0_as_typeof_0_90069": "Extraer en variable y reemplazar por “{0} as typeof {0}”", "Extract_type_95077": "Extraer el tipo", "FILE_6035": "ARCHIVO", "FILE_OR_DIRECTORY_6040": "ARCHIVO O DIRECTORIO", "Failed_to_find_peerDependency_0_6283": "No se ha podido encontrar peerDependency “{0}”.", "Failed_to_resolve_under_condition_0_6415": "No se pudo resolver en la condición “{0}”.", "Fallthrough_case_in_switch_7029": "Caso de Fallthrough en instrucción switch.", "File_0_does_not_exist_6096": "El archivo '{0}' no existe.", "File_0_does_not_exist_according_to_earlier_cached_lookups_6240": "El archivo \"{0}\" no existe de acuerdo con las búsquedas en caché anteriores.", "File_0_exists_according_to_earlier_cached_lookups_6239": "El archivo \"{0}\" existe de acuerdo con las búsquedas en caché anteriores.", "File_0_exists_use_it_as_a_name_resolution_result_6097": "El archivo “{0}” existe. Utilícelo como resultado de resolución de nombres.", "File_0_has_an_unsupported_extension_The_only_supported_extensions_are_1_6054": "El archivo \"{0}\" tiene una extensión no compatible. Las únicas extensiones compatibles son {1}.", "File_0_is_a_JavaScript_file_Did_you_mean_to_enable_the_allowJs_option_6504": "\"{0}\" es un archivo JavaScript. ¿Pretendía habilitar la opción \"allowJs\"?", "File_0_is_not_a_module_2306": "El archivo '{0}' no es un módulo.", "File_0_is_not_listed_within_the_file_list_of_project_1_Projects_must_list_all_files_or_use_an_includ_6307": "El archivo \"{0}\" no está en la lista de archivos del proyecto \"{1}\". Los proyectos deben enumerar todos los archivos o usar un patrón \"include\".", "File_0_is_not_under_rootDir_1_rootDir_is_expected_to_contain_all_source_files_6059": "El archivo '{0}' no está en \"rootDir\" '{1}'. Se espera que \"rootDir\" contenga todos los archivos de origen.", "File_0_not_found_6053": "Archivo '{0}' no encontrado.", "File_Management_6245": "Administración de archivos", "File_appears_to_be_binary_1490": "Parece que el archivo es binario.", "File_change_detected_Starting_incremental_compilation_6032": "Se detectó un cambio de archivo. Iniciando la compilación incremental...", "File_is_CommonJS_module_because_0_does_not_have_field_type_1460": "El archivo es un módulo CommonJS porque “{0}” no tiene el campo “type”", "File_is_CommonJS_module_because_0_has_field_type_whose_value_is_not_module_1459": "El archivo es el módulo CommonJS porque “{0}” tiene el campo “type” cuyo valor no es “module”.", "File_is_CommonJS_module_because_package_json_was_not_found_1461": "El archivo es un módulo CommonJS porque no se encontró “package.json”", "File_is_ECMAScript_module_because_0_has_field_type_with_value_module_1458": "El archivo es un módulo ECMAScript porque “{0}” tiene el campo “type” con el valor “module”", "File_is_a_CommonJS_module_it_may_be_converted_to_an_ES_module_80001": "El archivo es un módulo CommonJS; se puede convertir en un módulo ES.", "File_is_default_library_for_target_specified_here_1426": "El archivo es la biblioteca predeterminada para el destino que se especifica aquí.", "File_is_entry_point_of_type_library_specified_here_1419": "El archivo es el punto de entrada de la biblioteca de tipos que se especifica aquí.", "File_is_included_via_import_here_1399": "El archivo se incluye aquí a través de la importación.", "File_is_included_via_library_reference_here_1406": "El archivo se incluye aquí a través de la referencia de la biblioteca.", "File_is_included_via_reference_here_1401": "El archivo se incluye aquí a través de la referencia.", "File_is_included_via_type_library_reference_here_1404": "El archivo se incluye aquí a través de la referencia de la biblioteca de tipos.", "File_is_library_specified_here_1423": "El archivo es la biblioteca que se especifica aquí.", "File_is_matched_by_files_list_specified_here_1410": "El archivo coincide con la lista de \"archivos\" que se especifica aquí.", "File_is_matched_by_include_pattern_specified_here_1408": "El archivo coincide con el patrón de inclusión que se especifica aquí.", "File_is_output_from_referenced_project_specified_here_1413": "El archivo es la salida del proyecto al que se hace referencia especificado aquí.", "File_is_output_of_project_reference_source_0_1428": "El archivo es la salida del origen de referencia del proyecto \"{0}\".", "File_is_source_from_referenced_project_specified_here_1416": "El archivo es el origen del proyecto al que se hace referencia especificado aquí.", "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149": "El nombre de archivo \"{0}\" es diferente del nombre de archivo \"{1}\" ya incluido solo en el uso de mayúsculas y minúsculas.", "File_name_0_has_a_1_extension_looking_up_2_instead_6262": "El nombre de archivo “{0}” tiene una extensión de “{1}”. Buscando “{2}” en su lugar.", "File_name_0_has_a_1_extension_stripping_it_6132": "El nombre de archivo \"{0}\" tiene una extensión \"{1}\" y se va a quitar.", "File_redirects_to_file_0_1429": "El archivo redirecciona al archivo \"{0}\".", "File_specification_cannot_contain_a_parent_directory_that_appears_after_a_recursive_directory_wildca_5065": "La especificación del archivo no puede contener un directorio primario ('..') que aparezca después de un comodín de directorios recursivo ('**'): '{0}'.", "File_specification_cannot_end_in_a_recursive_directory_wildcard_Asterisk_Asterisk_Colon_0_5010": "La especificación de archivo no puede finalizar en un comodín de directorio recursivo ('**'): '{0}'.", "Filters_results_from_the_include_option_6627": "Filtre resultados de la opción \"include\".", "Fix_all_detected_spelling_errors_95026": "Corregir todos los errores ortográficos detectados", "Fix_all_expressions_possibly_missing_await_95085": "Corregir todas las expresiones en las que posiblemente falte \"await\"", "Fix_all_implicit_this_errors_95107": "Corregir todos los errores de \"this\" implícitos", "Fix_all_incorrect_return_type_of_an_async_functions_90037": "Corregir todos los tipos de valor devuelto incorrectos de las funciones asincrónicas", "Fix_all_with_type_only_imports_95182": "Corregir todo con importaciones de solo tipo", "Found_0_errors_6217": "Se encontró {0} errores.", "Found_0_errors_Watching_for_file_changes_6194": "Se encontraron {0} errores. Supervisando los cambios del archivo.", "Found_0_errors_in_1_files_6261": "Se han encontrado {0} errores en {1} archivos.", "Found_0_errors_in_the_same_file_starting_at_Colon_1_6260": "Se han encontrado {0} errores en el mismo archivo, empezando por: {1}", "Found_1_error_6216": "Se encontró 1 error.", "Found_1_error_Watching_for_file_changes_6193": "Se encontró un error. Supervisando los cambios del archivo.", "Found_1_error_in_0_6259": "Se ha encontrado 1 error en {0}", "Found_package_json_at_0_6099": "Se encontró 'package.json' en '{0}'.", "Found_peerDependency_0_with_1_version_6282": "Se encontró el “{0}” peerDependency con versión “{1}”.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_1250": "No se permiten declaraciones de función en bloques en modo strict cuando el destino es “ES5”.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Class_definiti_1251": "No se permiten declaraciones de función en bloques en modo strict cuando el destino es “ES5”. Las definiciones de clase están en modo strict de forma automática.", "Function_declarations_are_not_allowed_inside_blocks_in_strict_mode_when_targeting_ES5_Modules_are_au_1252": "No se permiten declaraciones de función en bloques en modo strict cuando el destino es “ES5”. Los módulos están en modo strict de forma automática.", "Function_expression_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7011": "La expresión de función, que carece de una anotación de tipo de valor devuelto, tiene implícitamente un tipo de valor devuelto '{0}'.", "Function_implementation_is_missing_or_not_immediately_following_the_declaration_2391": "Falta la implementación de función o no sigue inmediatamente a la declaración.", "Function_implementation_name_must_be_0_2389": "El nombre de la implementación de función debe ser '{0}'.", "Function_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_ref_7024": "La función tiene el tipo de valor devuelto \"any\" implícitamente porque no tiene una anotación de tipo de valor devuelto y se hace referencia a ella directa o indirectamente en una de sus expresiones return.", "Function_lacks_ending_return_statement_and_return_type_does_not_include_undefined_2366": "Falta la instrucción \"return\" final en la función y el tipo de valor devuelto no incluye 'undefined'.", "Function_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9007": "La función debe tener una anotación de tipo de valor devuelto explícita con --isolatedDeclarations.", "Function_not_implemented_95159": "La función no está implementada.", "Function_overload_must_be_static_2387": "La sobrecarga de función debe ser estática.", "Function_overload_must_not_be_static_2388": "La sobrecarga de función no debe ser estática.", "Function_type_notation_must_be_parenthesized_when_used_in_a_union_type_1385": "La notación de tipo de función debe incluirse entre paréntesis cuando se use en un tipo de unión.", "Function_type_notation_must_be_parenthesized_when_used_in_an_intersection_type_1387": "La notación de tipo de función debe incluirse entre paréntesis cuando se use en un tipo de intersección.", "Function_type_which_lacks_return_type_annotation_implicitly_has_an_0_return_type_7014": "El tipo de función, que carece de una anotación de tipo de valor devuelto, tiene implícitamente un tipo de valor devuelto \"{0}\".", "Function_with_bodies_can_only_merge_with_classes_that_are_ambient_2814": "La función con cuerpos solo se puede combinar con clases que son ambientes.", "Generate_d_ts_files_from_TypeScript_and_JavaScript_files_in_your_project_6612": "Genere archivos .d.ts desde los archivos TypeScript y JavaScript del proyecto.", "Generate_get_and_set_accessors_95046": "Generar los descriptores de acceso \"get\" y \"set\"", "Generate_get_and_set_accessors_for_all_overriding_properties_95119": "Generar los descriptores de acceso \"get\" y \"set\" para todas las propiedades de reemplazo", "Generates_a_CPU_profile_6223": "Genera un perfil de CPU.", "Generates_a_sourcemap_for_each_corresponding_d_ts_file_6000": "Genera un mapa de origen para cada archivo \".d.ts\" correspondiente.", "Generates_an_event_trace_and_a_list_of_types_6237": "Genera un seguimiento de eventos y una lista de tipos.", "Generates_corresponding_d_ts_file_6002": "Genera el archivo \".d.ts\" correspondiente.", "Generates_corresponding_map_file_6043": "Genera el archivo \".map\" correspondiente.", "Generator_implicitly_has_yield_type_0_Consider_supplying_a_return_type_annotation_7025": "El generador tiene implícitamente el tipo de retorno \"{0}\". Considere la posibilidad de proporcionar una anotación de tipo de valor devuelto.", "Generators_are_not_allowed_in_an_ambient_context_1221": "Los generadores no se permiten en un contexto de ambiente.", "Generic_type_0_requires_1_type_argument_s_2314": "El tipo genérico '{0}' requiere los siguientes argumentos de tipo: {1}.", "Generic_type_0_requires_between_1_and_2_type_arguments_2707": "El tipo genérico \"{0}\" requiere entre {1} y {2} argumentos de tipo.", "Global_module_exports_may_only_appear_at_top_level_1316": "Las exportaciones de módulos globales solo pueden aparecer en el nivel superior.", "Global_module_exports_may_only_appear_in_declaration_files_1315": "Las exportaciones de módulos globales solo pueden aparecer en archivos de declaración.", "Global_module_exports_may_only_appear_in_module_files_1314": "Las exportaciones de módulos globales solo pueden aparecer en archivos de módulo.", "Global_type_0_must_be_a_class_or_interface_type_2316": "El tipo global '{0}' debe ser un tipo de clase o de interfaz.", "Global_type_0_must_have_1_type_parameter_s_2317": "El tipo global '{0}' debe tener los siguientes parámetros de tipo: {1}.", "Have_recompiles_in_incremental_and_watch_assume_that_changes_within_a_file_will_only_affect_files_di_6384": "Al volver a compilar en \"--incremental\" y \"--watch\" se asume que los cambios en un archivo solo afectarán a los archivos que dependan de este directamente.", "Have_recompiles_in_projects_that_use_incremental_and_watch_mode_assume_that_changes_within_a_file_wi_6606": "Hacer que las recompilaciones en los proyectos que utilizan el modo 'incremental' y 'inspección' supongan que los cambios dentro de un archivo sólo afectarán a los archivos que dependen directamente de él.", "Hexadecimal_digit_expected_1125": "Se esperaba un dígito hexadecimal.", "Identifier_expected_0_is_a_reserved_word_at_the_top_level_of_a_module_1262": "Se esperaba un identificador. \"{0}\" es una palabra reservada en el nivel superior de un módulo.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212": "Se esperaba un identificador. \"{0}\" es una palabra reservada en modo strict.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213": "Se esperaba un identificador. '{0}' es una palabra reservada en modo strict. Las definiciones de clase están en modo strict automáticamente.", "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214": "Se esperaba un identificador. '{0}' es una palabra reservada en modo strict. Los módulos están en modo strict automáticamente.", "Identifier_expected_0_is_a_reserved_word_that_cannot_be_used_here_1359": "Se esperaba un identificador. \"{0}\" es una palabra reservada que no se puede usar aquí.", "Identifier_expected_1003": "Se esperaba un identificador.", "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216": "Identificador esperado. \"__esModule\" está reservado como marcador exportado al transformar módulos ECMAScript.", "Identifier_or_string_literal_expected_1478": "Se esperaba un literal de cadena o identificador", "Identifier_string_literal_or_number_literal_expected_1496": "Se esperaba un identificador, un literal de cadena o un literal de número.", "If_the_0_package_actually_exposes_this_module_consider_sending_a_pull_request_to_amend_https_Colon_S_7040": "Si el paquete \"{0}\" expone realmente este módulo, considere la posibilidad de enviar una solicitud de incorporación de cambios para corregir \"https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/{1}\".", "If_the_0_package_actually_exposes_this_module_try_adding_a_new_declaration_d_ts_file_containing_decl_7058": "Si el paquete '{0}' realmente expone este módulo, intente agregar un nuevo archivo de declaración (.d.ts) que contenga 'declarar módulo '{1}';`", "Ignore_this_error_message_90019": "Ignorar este mensaje de error", "Ignoring_tsconfig_json_compiles_the_specified_files_with_default_compiler_options_6924": "Ignora tsconfig.json y se compilan los archivos especificados con las opciones predeterminadas del compilador.", "Implement_all_inherited_abstract_classes_95040": "Implementar todas las clases abstractas heredadas", "Implement_all_unimplemented_interfaces_95032": "Implementar todas las interfaces no implementadas", "Implement_inherited_abstract_class_90007": "Implementar clase abstracta heredada", "Implement_interface_0_90006": "Implementar la interfaz \"{0}\"", "Implements_clause_of_exported_class_0_has_or_is_using_private_name_1_4019": "La cláusula implements de la clase '{0}' exportada tiene o usa el nombre privado '{1}'.", "Implicit_conversion_of_a_symbol_to_a_string_will_fail_at_runtime_Consider_wrapping_this_expression_i_2731": "La conversión implícita de un elemento \"symbol\" en \"string\" dará un error en tiempo de ejecución. Considere la posibilidad de encapsular esta expresión en \"String (...)\".", "Import_0_conflicts_with_global_value_used_in_this_file_so_must_be_declared_with_a_type_only_import_w_2866": "La importación “{0}” entra en conflicto con el valor global usado en este archivo, por lo que debe declararse con una importación de solo tipo cuando “isolatedModules” está habilitado.", "Import_0_conflicts_with_local_value_so_must_be_declared_with_a_type_only_import_when_isolatedModules_2865": "Importe los conflictos “{0}” con el valor local, por lo que deben declararse con una importación de solo tipo cuando “isolatedModules” está habilitado.", "Import_0_from_1_90013": "Importar “{0}” desde “{1}”", "Import_assertion_values_must_be_string_literal_expressions_2837": "Los valores de aserción de importación deben ser expresiones literales de cadena.", "Import_assertions_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2836": "No se permiten aserciones de importación en instrucciones que se compilan en llamadas “require” de CommonJS.", "Import_assertions_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2821": "Las aserciones de importación solo se admiten cuando la opción \"--module\" está establecida en \"esnext\", \"node18\", \"nodenext\" o \"preserve\".", "Import_assertions_cannot_be_used_with_type_only_imports_or_exports_2822": "Las aserciones de importación no se pueden usar con importaciones o exportaciones de solo tipo.", "Import_assertions_have_been_replaced_by_import_attributes_Use_with_instead_of_assert_2880": "Las aserciones de importación se han reemplazado por atributos de importación. Use 'with' en lugar de 'assert'.", "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202": "No se puede usar una asignación de importación cuando se eligen módulos de ECMAScript como destino. Considere la posibilidad de usar \"import * as ns from 'mod'\", \"import {a} from 'mod'\", \"import d from 'mod'\" u otro formato de módulo en su lugar.", "Import_attribute_values_must_be_string_literal_expressions_2858": "Los valores de atributo de importación deben ser expresiones literales de cadena.", "Import_attributes_are_not_allowed_on_statements_that_compile_to_CommonJS_require_calls_2856": "No se permiten atributos de importación en instrucciones que se compilan en llamadas “require” de CommonJS.", "Import_attributes_are_only_supported_when_the_module_option_is_set_to_esnext_node18_nodenext_or_pres_2823": "Los atributos de importación solo se admiten cuando la opción \"--module\" está establecida en \"esnext\", \"node18\", \"nodenext\" o \"preserve\".", "Import_attributes_cannot_be_used_with_type_only_imports_or_exports_2857": "Los atributos de importación no se pueden usar con importaciones o exportaciones de solo tipo.", "Import_declaration_0_is_using_private_name_1_4000": "La declaración de importación '{0}' usa el nombre privado '{1}'.", "Import_declaration_conflicts_with_local_declaration_of_0_2440": "La declaración de importación está en conflicto con la declaración local de \"{0}\".", "Import_declarations_in_a_namespace_cannot_reference_a_module_1147": "Las declaraciones de importación de un espacio de nombres no pueden hacer referencia a un módulo.", "Import_emit_helpers_from_tslib_6139": "Importe asistentes de emisión de \"tslib\".", "Import_may_be_converted_to_a_default_import_80003": "La importación puede convertirse a una importación predeterminada.", "Import_name_cannot_be_0_2438": "El nombre de importación no puede ser \"{0}\".", "Import_or_export_declaration_in_an_ambient_module_declaration_cannot_reference_module_through_relati_2439": "La declaración de importación o exportación de una declaración de módulo de ambiente no puede hacer referencia al módulo a través de su nombre relativo.", "Import_specifier_0_does_not_exist_in_package_json_scope_at_path_1_6271": "El especificador de importación \"{0}\" no existe en el ámbito package.json en la ruta de acceso \"{1}\".", "Imported_via_0_from_file_1_1393": "Se importó mediante {0} desde el archivo \"{1}\".", "Imported_via_0_from_file_1_to_import_importHelpers_as_specified_in_compilerOptions_1395": "Se importó mediante {0} desde el archivo \"{1}\" para importar \"importHelpers\" tal y como se especifica en compilerOptions.", "Imported_via_0_from_file_1_to_import_jsx_and_jsxs_factory_functions_1397": "Se importó mediante {0} desde el archivo \"{1}\" para importar las funciones de fábrica \"jsx\" y \"jsxs\".", "Imported_via_0_from_file_1_with_packageId_2_1394": "Se importó mediante {0} desde el archivo \"{1}\" con el valor packageId \"{2}\".", "Imported_via_0_from_file_1_with_packageId_2_to_import_importHelpers_as_specified_in_compilerOptions_1396": "Se importó mediante {0} desde el archivo \"{1}\" con el valor packageId \"{2}\" para importar \"importHelpers\" tal y como se especifica en compilerOptions.", "Imported_via_0_from_file_1_with_packageId_2_to_import_jsx_and_jsxs_factory_functions_1398": "Se importó mediante {0} desde el archivo \"{1}\" con el valor packageId \"{2}\" para importar las funciones de fábrica \"jsx\" y \"jsxs\".", "Importing_a_JSON_file_into_an_ECMAScript_module_requires_a_type_Colon_json_import_attribute_when_mod_1543": "La importación de un archivo JSON en un módulo ECMAScript requiere un atributo de importación \"type: \"json\"\" cuando \"module\" se establece en \"{0}\".", "Imports_are_not_permitted_in_module_augmentations_Consider_moving_them_to_the_enclosing_external_mod_2667": "No se permiten importaciones en aumentos de módulos. Considere la posibilidad de moverlas al módulo externo envolvente.", "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066": "En las declaraciones de enumeración de ambiente, el inicializador de miembro debe ser una expresión constante.", "In_an_enum_with_multiple_declarations_only_one_declaration_can_omit_an_initializer_for_its_first_enu_2432": "En una enumeración con varias declaraciones, solo una declaración puede omitir un inicializador para el primer elemento de la enumeración.", "Include_a_list_of_files_This_does_not_support_glob_patterns_as_opposed_to_include_6635": "Incluya una lista de archivos. Esto no admite patrones globales, contrario a \"include\".", "Include_modules_imported_with_json_extension_6197": "Incluir módulos importados con la extensión \".json\"", "Include_source_code_in_the_sourcemaps_inside_the_emitted_JavaScript_6644": "Incluya el código fuente en los mapas de origen dentro del JavaScript emitido.", "Include_sourcemap_files_inside_the_emitted_JavaScript_6643": "Incluir archivos de mapas de origen dentro del JavaScript emitido.", "Includes_imports_of_types_referenced_by_0_90054": "Incluye importaciones de tipos a los que hace referencia \"{0}\"", "Including_watch_w_will_start_watching_the_current_project_for_the_file_changes_Once_set_you_can_conf_6914": "Al incluir --watch, -w empezar<PERSON> a ver el proyecto actual por los cambios de archivo. Una vez establecido, puede configurar el modo de inspección con:", "Incomplete_quantifier_Digit_expected_1505": "Cuantificador incompleto. Se esperaba un dígito.", "Index_signature_for_type_0_is_missing_in_type_1_2329": "Falta la signatura de índice para el tipo \"{0}\" en el tipo \"{1}\".", "Index_signature_in_type_0_only_permits_reading_2542": "La signatura de índice del tipo '{0}' solo permite lectura.", "Individual_declarations_in_merged_declaration_0_must_be_all_exported_or_all_local_2395": "Las declaraciones individuales de la declaración '{0}' combinada deben ser todas exportadas o todas locales.", "Infer_all_types_from_usage_95023": "Deducir todos los tipos del uso", "Infer_function_return_type_95148": "Deducir el tipo de valor devuelto de función", "Infer_parameter_types_from_usage_95012": "Deducir los tipos de parámetro del uso", "Infer_this_type_of_0_from_usage_95080": "Inferir el tipo \"this\" de \"{0}\" a partir del uso", "Infer_type_of_0_from_usage_95011": "Deducir el tipo de \"{0}\" del uso", "Inference_from_class_expressions_is_not_supported_with_isolatedDeclarations_9022": "La inferencia de expresiones de clase no se admite con --isolatedDeclarations.", "Initialize_property_0_in_the_constructor_90020": "Iniciali<PERSON> la propiedad \"{0}\" en el constructor", "Initialize_static_property_0_90021": "Inicializar la propiedad estática \"{0}\"", "Initializer_for_property_0_2811": "Inicializador para la propiedad \"{0}\"", "Initializer_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2301": "El inicializador de la variable miembro de instancia '{0}' no puede hacer referencia al identificador '{1}' declarado en el constructor.", "Initializers_are_not_allowed_in_ambient_contexts_1039": "No se permiten inicializadores en los contextos de ambiente.", "Initializes_a_TypeScript_project_and_creates_a_tsconfig_json_file_6070": "Inicializa un proyecto de TypeScript y crea un archivo tsconfig.json.", "Inline_variable_95184": "Variable insertada", "Insert_command_line_options_and_files_from_a_file_6030": "Inserte opciones de la línea de comandos y archivos desde un archivo.", "Install_0_95014": "Instalar \"{0}\"", "Install_all_missing_types_packages_95033": "Instalar todos los paquetes de tipos que faltan", "Interface_0_cannot_simultaneously_extend_types_1_and_2_2320": "La interfaz '{0}' no puede extender los tipos '{1}' y '{2}' simultáneamente.", "Interface_0_incorrectly_extends_interface_1_2430": "La interfaz '{0}' extiende la interfaz '{1}' de forma incorrecta.", "Interface_declaration_cannot_have_implements_clause_1176": "La declaración de interfaz no puede tener una cláusula \"implements\".", "Interface_must_be_given_a_name_1438": "Se debe asignar un nombre a la interfaz.", "Interface_name_cannot_be_0_2427": "El nombre de la interfaz no puede ser \"{0}\".", "Interop_Constraints_6252": "Restricciones de interoperabilidad", "Interpret_optional_property_types_as_written_rather_than_adding_undefined_6243": "Interprete los tipos de propiedad opcionales como escritos en lugar de agregar \"undefined\".", "Invalid_character_1127": "Carácter no válido.", "Invalid_import_specifier_0_has_no_possible_resolutions_6272": "El especificador de importación no válido \"{0}\" no tiene resoluciones posibles.", "Invalid_module_name_in_augmentation_Module_0_resolves_to_an_untyped_module_at_1_which_cannot_be_augm_2665": "Nombre de módulo no válido en el aumento. El módulo '{0}' se resuelve como un módulo sin tipo en '{1}', que no se puede aumentar.", "Invalid_module_name_in_augmentation_module_0_cannot_be_found_2664": "Nombre de módulo no válido en un aumento, no se encuentra el módulo '{0}'.", "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209": "Cadena opcional no válida de la nueva expresión. ¿Quería llamar a \"{0}()\"?", "Invalid_reference_directive_syntax_1084": "Sintaxis de la directiva \"reference\" no válida.", "Invalid_syntax_in_decorator_1498": "Sintaxis no válida en Decorator.", "Invalid_use_of_0_It_cannot_be_used_inside_a_class_static_block_18039": "Uso no válido de '{0}'. No se puede usar dentro de un bloque estático de clase.", "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215": "Uso de '{0}' no válido. Los módulos están en modo strict automáticamente.", "Invalid_use_of_0_in_strict_mode_1100": "Uso no válido de '{0}' en modo strict.", "Invalid_value_for_ignoreDeprecations_5103": "Valor no válido para “--ignoreDeprecations”.", "Invalid_value_for_jsxFactory_0_is_not_a_valid_identifier_or_qualified_name_5067": "Valor no válido para \"jsxFactory\". \"{0}\" no es un nombre calificado o un identificador válido.", "Invalid_value_for_jsxFragmentFactory_0_is_not_a_valid_identifier_or_qualified_name_18035": "Valor no válido para \"jsxFactory\". \"{0}\" no es un nombre cualificado o un identificador válidos.", "Invalid_value_for_reactNamespace_0_is_not_a_valid_identifier_5059": "Valor no válido para '--reactNamespace'. '{0}' no es un identificador válido.", "It_is_likely_that_you_are_missing_a_comma_to_separate_these_two_template_expressions_They_form_a_tag_2796": "Es probable que falte una coma para separar estas dos expresiones de plantilla. Forman una expresión de plantilla con etiquetas que no se puede invocar.", "Its_element_type_0_is_not_a_valid_JSX_element_2789": "El tipo de elemento \"{0}\" no es un elemento JSX válido.", "Its_instance_type_0_is_not_a_valid_JSX_element_2788": "El tipo de instancia \"{0}\" no es un elemento JSX válido.", "Its_return_type_0_is_not_a_valid_JSX_element_2787": "El tipo de valor devuelto \"{0}\" no es un elemento JSX válido.", "Its_type_0_is_not_a_valid_JSX_element_type_18053": "El tipo “{0}” no es un tipo de elemento JSX válido.", "JSDoc_0_1_does_not_match_the_extends_2_clause_8023": "La etiqueta \"@{0} {1}\" de JSDoc no coincide con la cláusula \"extends {2}\".", "JSDoc_0_is_not_attached_to_a_class_8022": "La etiqueta \"@{0}\" de JSDoc no está asociada a una clase.", "JSDoc_may_only_appear_in_the_last_parameter_of_a_signature_8028": "\"...\" de JSDoc solo puede aparecer en el último parámetro de una signatura.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_8024": "La etiqueta \"@param\" de JSDoc tiene el nombre \"{0}\", pero no hay ningún parámetro con ese nombre.", "JSDoc_param_tag_has_name_0_but_there_is_no_parameter_with_that_name_It_would_match_arguments_if_it_h_8029": "La etiqueta de JSDoc \"@param\" tiene el nombre \"{0}\", pero no hay ningún parámetro con ese nombre. Coincidiría con \"arguments\" si tuviera un tipo de matriz.", "JSDoc_typedef_may_be_converted_to_TypeScript_type_80009": "El typedef de JSDoc se puede convertir al tipo TypeScript.", "JSDoc_typedef_tag_should_either_have_a_type_annotation_or_be_followed_by_property_or_member_tags_8021": "La etiqueta \"@typedef\" de JSDoc debe tener una anotación de tipo o ir seguida de las etiquetas \"@property\" o \"@member\".", "JSDoc_typedefs_may_be_converted_to_TypeScript_types_80010": "Las definiciones de tipos JSDoc se pueden convertir en tipos TypeScript.", "JSDoc_types_can_only_be_used_inside_documentation_comments_8020": "Los tipos JSDoc solo se pueden usar en los comentarios de la documentación.", "JSDoc_types_may_be_moved_to_TypeScript_types_80004": "Los tipos de JSDoc pueden moverse a tipos de TypeScript.", "JSX_attributes_must_only_be_assigned_a_non_empty_expression_17000": "A los atributos JSX se les debe asignar únicamente un elemento \"expression\" que no esté vacío.", "JSX_element_0_has_no_corresponding_closing_tag_17008": "El elemento JSX '{0}' no tiene la etiqueta de cierre correspondiente.", "JSX_element_class_does_not_support_attributes_because_it_does_not_have_a_0_property_2607": "La clase de elemento JSX no admite atributos porque no tiene una propiedad \"{0}\".", "JSX_element_implicitly_has_type_any_because_no_interface_JSX_0_exists_7026": "El elemento JSX tiene el tipo \"any\" implícitamente porque no existe ninguna interfaz \"JSX.{0}\".", "JSX_element_implicitly_has_type_any_because_the_global_type_JSX_Element_does_not_exist_2602": "El elemento JSX tiene el tipo \"any\" implícitamente porque no existe el tipo global \"JSX.Element\".", "JSX_element_type_0_does_not_have_any_construct_or_call_signatures_2604": "El tipo de elemento JSX '{0}' no tiene ninguna signatura de construcción ni de llamada.", "JSX_elements_cannot_have_multiple_attributes_with_the_same_name_17001": "Los elementos JSX no pueden tener varios atributos con el mismo nombre.", "JSX_expressions_may_not_use_the_comma_operator_Did_you_mean_to_write_an_array_18007": "Las expresiones JSX no pueden usar el operador de coma. ¿Pretendía escribir una matriz?", "JSX_expressions_must_have_one_parent_element_2657": "Las expresiones JSX deben tener un elemento primario.", "JSX_fragment_has_no_corresponding_closing_tag_17014": "El fragmento de JSX no tiene la etiqueta de cierre correspondiente.", "JSX_property_access_expressions_cannot_include_JSX_namespace_names_2633": "Las expresiones de acceso a la propiedad JSX no pueden incluir nombres de espacios de nombres JSX", "JSX_spread_child_must_be_an_array_type_2609": "El elemento secundario de propagación JSX debe ser de tipo matriz.", "JavaScript_Support_6247": "Compatibilidad con JavaScript", "Jump_target_cannot_cross_function_boundary_1107": "Un destino de salto no puede atravesar el límite de función.", "KIND_6034": "TIPO", "Keywords_cannot_contain_escape_characters_1260": "Las palabras clave no pueden contener caracteres de escape.", "LOCATION_6037": "UBICACIÓN", "Language_and_Environment_6254": "Lenguaje y ambiente", "Left_side_of_comma_operator_is_unused_and_has_no_side_effects_2695": "La parte izquierda del operador de coma no se usa y no tiene efectos secundarios.", "Library_0_specified_in_compilerOptions_1422": "La biblioteca \"{0}\" se especifica en compilerOptions", "Library_referenced_via_0_from_file_1_1405": "Biblioteca a la que se hace referencia mediante \"{0}\" desde el archivo \"{1}\"", "Line_break_not_permitted_here_1142": "No se permite el salto de línea aquí.", "Line_terminator_not_permitted_before_arrow_1200": "No se permite usar un terminador de línea antes de una flecha.", "List_of_file_name_suffixes_to_search_when_resolving_a_module_6931": "Lista de sufijos de nombre de archivo para buscar al resolver un módulo.", "List_of_folders_to_include_type_definitions_from_6161": "Lista de carpetas de donde se deben incluir las definiciones de tipos.", "List_of_root_folders_whose_combined_content_represents_the_structure_of_the_project_at_runtime_6168": "Lista de carpetas raíz cuyo contenido combinado representa la estructura del proyecto en tiempo de ejecución.", "Loading_0_from_the_root_dir_1_candidate_location_2_6109": "Cargando \"{0}\" del directorio raíz \"{1}\", ubicación candidata: \"{2}\"", "Loading_module_0_from_node_modules_folder_target_file_types_Colon_1_6098": "Se cargará el módulo “{0}” de la carpeta “node_modules”, tipos de archivo de destino “{1}”.", "Loading_module_as_file_Slash_folder_candidate_module_location_0_target_file_types_Colon_1_6095": "Se cargará el módulo como archivo/carpeta, ubicación del módulo candidato “{0}”, tipos de archivo de destino “{1}”.", "Locale_must_be_of_the_form_language_or_language_territory_For_example_0_or_1_6048": "La configuración regional debe tener el formato <idioma> o <idioma>-<territorio>. <PERSON><PERSON> eje<PERSON><PERSON>, '{0}' o '{1}'.", "Log_paths_used_during_the_moduleResolution_process_6706": "Rutas de acceso de registro usadas durante el proceso \"moduleResolution\".", "Longest_matching_prefix_for_0_is_1_6108": "El prefijo coincidente más largo para \"{0}\" es \"{1}\".", "Looking_up_in_node_modules_folder_initial_location_0_6125": "Buscando en la carpeta \"node_modules\", ubicación inicial: \"{0}\".", "Make_all_super_calls_the_first_statement_in_their_constructor_95036": "Convertir todas las llamadas a \"super()\" en la primera instrucción de su constructor", "Make_keyof_only_return_strings_instead_of_string_numbers_or_symbols_Legacy_option_6650": "Haga que keyof solo devuelva cadenas en lugar de cadenas, números o símbolos. Opción heredada.", "Make_super_call_the_first_statement_in_the_constructor_90002": "Hacer que la llamada a \"super()\" sea la primera instrucción del constructor", "Mapped_object_type_implicitly_has_an_any_template_type_7039": "El tipo de objeto asignado tiene implícitamente un tipo de plantilla \"any\".", "Mark_array_literal_as_const_90070": "Marcar literal de matriz como const", "Matched_0_condition_1_6403": "Coincidente con '{0}' condición '{1}'.", "Matched_by_default_include_pattern_Asterisk_Asterisk_Slash_Asterisk_1457": "La coincidencia de forma predeterminada incluye el patrón '**/*'", "Matched_by_include_pattern_0_in_1_1407": "Coincidencia con el patrón de inclusión \"{0}\" en \"{1}\"", "Member_0_implicitly_has_an_1_type_7008": "El miembro '{0}' tiene un tipo '{1}' implícitamente.", "Member_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7045": "El miembro \"{0}\" tiene un tipo \"{1}\" de forma implícita, pero se puede inferir un tipo más adecuado a partir del uso.", "Merge_conflict_marker_encountered_1185": "Se encontró un marcador de conflicto de combinación.", "Merged_declaration_0_cannot_include_a_default_export_declaration_Consider_adding_a_separate_export_d_2652": "La declaración combinada '{0}' no puede incluir una declaración de exportación predeterminada. Considere la posibilidad de agregar una declaración \"export default {0}\" independiente en su lugar.", "Meta_property_0_is_only_allowed_in_the_body_of_a_function_declaration_function_expression_or_constru_17013": "La propiedad Meta \"{0}\" solo se permite en el cuerpo de una declaración de función, una expresión de función o un constructor.", "Method_0_cannot_have_an_implementation_because_it_is_marked_abstract_1245": "El método '{0}' no puede tener ninguna implementación porque está marcado como abstracto.", "Method_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4101": "El método \"{0}\" de la interfaz exportada tiene o usa el nombre \"{1}\" del módulo privado \"{2}\".", "Method_0_of_exported_interface_has_or_is_using_private_name_1_4102": "El método \"{0}\" de la interfaz exportada tiene o usa el nombre privado \"{1}\".", "Method_must_have_an_explicit_return_type_annotation_with_isolatedDeclarations_9008": "El método debe tener una anotación de tipo de valor devuelto explícita con --isolatedDeclarations.", "Method_not_implemented_95158": "El método no está implementado.", "Modifiers_cannot_appear_here_1184": "Los modificadores no pueden aparecer aquí.", "Module_0_can_only_be_default_imported_using_the_1_flag_1259": "El módulo \"{0}\" solo puede importarse de forma predeterminada con la marca \"{1}\".", "Module_0_cannot_be_imported_using_this_construct_The_specifier_only_resolves_to_an_ES_module_which_c_1471": "El módulo \"{0}\" no se puede importar con esta construcción. El especificador solo se resuelve en un módulo ES, que no se puede importar con \"require\". En su lugar, use una importación de ECMAScript.", "Module_0_declares_1_locally_but_it_is_exported_as_2_2460": "El módulo \"{0}\" declara \"{1}\" localmente, pero se exporta como \"{2}\".", "Module_0_declares_1_locally_but_it_is_not_exported_2459": "El módulo \"{0}\" declara \"{1}\" localmente, pero no se exporta.", "Module_0_does_not_refer_to_a_type_but_is_used_as_a_type_here_Did_you_mean_typeof_import_0_1340": "El módulo \"{0}\" no hace referencia a un tipo, pero aquí se usa como tipo. ¿Quiso decir \"typeof import('{0}')\"?", "Module_0_does_not_refer_to_a_value_but_is_used_as_a_value_here_1339": "El módulo \"{0}\" no hace referencia a un valor, pero aquí se usa como valor.", "Module_0_has_already_exported_a_member_named_1_Consider_explicitly_re_exporting_to_resolve_the_ambig_2308": "El módulo {0} ya ha exportado un miembro denominado '{1}'. Considere la posibilidad de volver a exportarlo de forma explícita para resolver la ambigüedad.", "Module_0_has_no_default_export_1192": "El módulo '{0}' no tiene ninguna exportación predeterminada.", "Module_0_has_no_default_export_Did_you_mean_to_use_import_1_from_0_instead_2613": "El módulo \"{0}\" no tiene ninguna exportación predeterminada. ¿Pretendía usar \"import { {1} } from {0}\" en su lugar?", "Module_0_has_no_exported_member_1_2305": "El módulo '{0}' no tiene ningún miembro '{1}' exportado.", "Module_0_has_no_exported_member_1_Did_you_mean_to_use_import_1_from_0_instead_2614": "El módulo \"{0}\" no tiene ningún miembro \"{1}\" exportado. ¿Pretendía usar \"import {1} from {0}\" en su lugar?", "Module_0_is_hidden_by_a_local_declaration_with_the_same_name_2437": "El módulo \"{0}\" está oculto por una declaración local con el mismo nombre.", "Module_0_uses_export_and_cannot_be_used_with_export_Asterisk_2498": "El módulo '{0}' usa \"export =\" y no se puede usar con \"export *\".", "Module_0_was_resolved_as_locally_declared_ambient_module_in_file_1_6144": "El módulo '{0}' se resolvió como un módulo de ambiente declarado localmente en el archivo '{1}'.", "Module_0_was_resolved_to_1_but_allowArbitraryExtensions_is_not_set_6263": "El módulo “{0}” se ha resuelto en “{1}”, pero “--allowArbitraryExtensions” no está establecido.", "Module_0_was_resolved_to_1_but_jsx_is_not_set_6142": "El módulo '{0}' se resolvió en '{1}', pero \"--jsx\" no está establecido.", "Module_0_was_resolved_to_1_but_resolveJsonModule_is_not_used_7042": "El módulo \"{0}\" se resolvió en \"{1}\", pero no se usa \"--resolveJsonModule\".", "Module_declaration_names_may_only_use_or_quoted_strings_1443": "Los nombres de declaración de módulo solo pueden usar cadenas con las comillas \" o '.", "Module_name_0_matched_pattern_1_6092": "Nombre del módulo: '{0}', patr<PERSON> coincidente: '{1}'.", "Module_name_0_was_not_resolved_6090": "======== No se resolvió el nombre de módulo '{0}'. ========", "Module_name_0_was_successfully_resolved_to_1_6089": "======== El nombre del módulo '{0}' se resolvió correctamente como '{1}'. ========", "Module_name_0_was_successfully_resolved_to_1_with_Package_ID_2_6218": "======== El nombre del módulo '{0}' se resolvió correctamente como \"{1}\" con el identificador de paquete \"{2}\". ========", "Module_resolution_kind_is_not_specified_using_0_6088": "No se ha especificado el tipo de resolución del módulo, se usará '{0}'.", "Module_resolution_using_rootDirs_has_failed_6111": "No se pudo resolver el módulo con \"rootDirs\".", "Modules_6244": "<PERSON><PERSON><PERSON><PERSON>", "Move_labeled_tuple_element_modifiers_to_labels_95117": "Mover modificadores de elemento de tupla etiquetados a etiquetas", "Move_the_expression_in_default_export_to_a_variable_and_add_a_type_annotation_to_it_9036": "Mueva la expresión de exportación predeterminada a una variable y agréguele una anotación de tipo.", "Move_to_a_new_file_95049": "Mover a un nuevo archivo", "Move_to_file_95178": "Mover a archivo", "Multiple_consecutive_numeric_separators_are_not_permitted_6189": "No se permiten varios separadores numéricos consecutivos.", "Multiple_constructor_implementations_are_not_allowed_2392": "No se permiten varias implementaciones del constructor.", "NEWLINE_6061": "NUEVA LÍNEA", "Name_is_not_valid_95136": "El nombre no es válido", "Named_capturing_groups_are_only_available_when_targeting_ES2018_or_later_1503": "Los grupos de captura con nombre solo están disponibles cuando el destino es “ES2018” o posterior.", "Named_capturing_groups_with_the_same_name_must_be_mutually_exclusive_to_each_other_1515": "Los grupos de captura con nombre que tengan el mismo nombre deben ser mutuamente excluyentes entre sí.", "Named_imports_from_a_JSON_file_into_an_ECMAScript_module_are_not_allowed_when_module_is_set_to_0_1544": "No se permiten las importaciones con nombre de un archivo JSON en un módulo ECMAScript cuando \"module\" está establecido en \"{0}\".", "Named_property_0_of_types_1_and_2_are_not_identical_2319": "La propiedad '{0}' con nombre de los tipos '{1}' y '{2}' no es idéntica en ambos.", "Namespace_0_has_no_exported_member_1_2694": "El espacio de nombres '{0}' no tiene ningún miembro '{1}' exportado.", "Namespace_must_be_given_a_name_1437": "Se debe asignar un nombre al espacio de nombres.", "Namespace_name_cannot_be_0_2819": "El nombre de espacio no puede ser \"{0}\".", "Namespaces_are_not_allowed_in_global_script_files_when_0_is_enabled_If_this_file_is_not_intended_to__1280": "No se permiten espacios de nombres en archivos de script globales cuando “{0}” está habilitado. Si este archivo no está pensado para ser un script global, establezca “moduleDetection” en “force” o agregue una instrucción “export {}” vacía.", "Neither_decorators_nor_modifiers_may_be_applied_to_this_parameters_1433": "No se pueden aplicar modificadores ni decoradores a los parámetros “this”.", "No_base_constructor_has_the_specified_number_of_type_arguments_2508": "No hay ningún constructor base con el número especificado de argumentos de tipo.", "No_constituent_of_type_0_is_callable_2755": "No se puede llamar a ningún constituyente del tipo \"{0}\".", "No_constituent_of_type_0_is_constructable_2759": "No se puede construir ningún constituyente del tipo \"{0}\".", "No_index_signature_with_a_parameter_of_type_0_was_found_on_type_1_7054": "No se encontró ninguna signatura de índice con un parámetro de tipo \"{0}\" en el tipo \"{1}\".", "No_inputs_were_found_in_config_file_0_Specified_include_paths_were_1_and_exclude_paths_were_2_18003": "No se encontraron entradas en el archivo de configuración '{0}'. Las rutas 'include' especificadas fueron '{1}' y las rutas 'exclude' fueron '{2}'.", "No_longer_supported_In_early_versions_manually_set_the_text_encoding_for_reading_files_6608": "Ya no se admite. En versiones anteriores, establezca manualmente la codificación de texto para leer archivos.", "No_overload_expects_0_arguments_but_overloads_do_exist_that_expect_either_1_or_2_arguments_2575": "Ninguna sobrecarga espera argumentos {0}, pero existen sobrecargas que esperan argumentos {1} o {2}.", "No_overload_expects_0_type_arguments_but_overloads_do_exist_that_expect_either_1_or_2_type_arguments_2743": "Ninguna sobrecarga espera argumentos de tipo {0}, pero existen sobrecargas que esperan argumentos de tipo {1} o {2}.", "No_overload_matches_this_call_2769": "Ninguna sobrecarga coincide con esta llamada.", "No_type_could_be_extracted_from_this_type_node_95134": "No se pudo extraer ningún tipo de este nodo de tipo", "No_value_exists_in_scope_for_the_shorthand_property_0_Either_declare_one_or_provide_an_initializer_18004": "No existe ningún valor en el ámbito para la propiedad abreviada \"{0}\". Declare uno o proporcione un inicializador.", "Non_abstract_class_0_does_not_implement_inherited_abstract_member_1_from_class_2_2515": "La clase '{0}' no abstracta no implementa el miembro abstracto heredado '{1}' de la clase '{2}'.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_2654": "Faltan implementaciones para los siguientes miembros de “{0}” en la clase no abstracta: “{1}”: {2}.", "Non_abstract_class_0_is_missing_implementations_for_the_following_members_of_1_Colon_2_and_3_more_2655": "Faltan implementaciones para los siguientes miembros de “{0}” en la clase no abstracta: “{1}”: {2} y {3} más.", "Non_abstract_class_expression_does_not_implement_inherited_abstract_member_0_from_class_1_2653": "Una expresión de clase no abstracta no implementa el miembro abstracto heredado '{0}' de la clase '{1}'.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_2656": "Faltan implementaciones para los siguientes miembros de “{0}” en la expresión de clase no abstracta: {1}.", "Non_abstract_class_expression_is_missing_implementations_for_the_following_members_of_0_Colon_1_and__2650": "Faltan implementaciones para los siguientes miembros de “{0}” en la expresión de clase no abstracta: {1} y {2} más.", "Non_null_assertions_can_only_be_used_in_TypeScript_files_8013": "Las aserciones no nulas solo se pueden usar en los archivos TypeScript.", "Non_relative_paths_are_not_allowed_when_baseUrl_is_not_set_Did_you_forget_a_leading_Slash_5090": "No se permiten rutas de acceso no relativas si no se ha establecido \"baseUrl\". ¿Ha olvidado poner \"./\" al inicio?", "Non_simple_parameter_declared_here_1348": "Se ha declarado un parámetro no simple aquí.", "Not_all_code_paths_return_a_value_7030": "No todas las rutas de acceso de código devuelven un valor.", "Not_all_constituents_of_type_0_are_callable_2756": "No se puede llamar a todos los constituyentes del tipo \"{0}\".", "Not_all_constituents_of_type_0_are_constructable_2760": "No se pueden construir todos los constituyentes del tipo \"{0}\".", "Numbers_out_of_order_in_quantifier_1506": "Números desordenado en el cuantificador.", "Numeric_literals_with_absolute_values_equal_to_2_53_or_greater_are_too_large_to_be_represented_accur_80008": "Los literales numéricos con valores absolutos iguales a 2^53 o superiores son demasiado grandes para representarlos de forma precisa como enteros.", "Numeric_separators_are_not_allowed_here_6188": "Aquí no se permiten separadores numéricos.", "Object_is_of_type_unknown_2571": "El objeto es de tipo \"desconocido\".", "Object_is_possibly_null_2531": "El objeto es posiblemente \"null\".", "Object_is_possibly_null_or_undefined_2533": "El objeto es posiblemente \"null\" o \"undefined\".", "Object_is_possibly_undefined_2532": "El objeto es posiblemente \"undefined\".", "Object_literal_may_only_specify_known_properties_and_0_does_not_exist_in_type_1_2353": "El literal de objeto solo puede especificar propiedades conocidas y '{0}' no existe en el tipo '{1}'.", "Object_literal_may_only_specify_known_properties_but_0_does_not_exist_in_type_1_Did_you_mean_to_writ_2561": "El literal de objeto solo puede especificar propiedades conocidas, pero \"{0}\" no existe en el tipo \"{1}\". ¿Quería escribir \"{2}\"?", "Object_literal_s_property_0_implicitly_has_an_1_type_7018": "La propiedad '{0}' del literal de objeto tiene un tipo '{1}' implícitamente.", "Objects_that_contain_shorthand_properties_can_t_be_inferred_with_isolatedDeclarations_9016": "Los objetos que contienen propiedades abreviadas no se pueden inferir con --isolatedDeclarations.", "Objects_that_contain_spread_assignments_can_t_be_inferred_with_isolatedDeclarations_9015": "Los objetos que contienen asignaciones de propagación no se pueden inferir con --isolatedDeclarations.", "Octal_digit_expected_1178": "Se esperaba un dígito octal.", "Octal_escape_sequences_and_backreferences_are_not_allowed_in_a_character_class_If_this_was_intended__1536": "No se permiten secuencias de escape decimales ni referencias inversas en una clase de caracteres. Si la intención era una secuencia de escape, use la sintaxis “{0}” en su lugar.", "Octal_escape_sequences_are_not_allowed_Use_the_syntax_0_1487": "No se permiten secuencias de escape octal. Use la sintaxis “{0}”.", "Octal_literals_are_not_allowed_Use_the_syntax_0_1121": "No se permiten literales octal. Use la sintaxis “{0}”.", "One_value_of_0_1_is_the_string_2_and_the_other_is_assumed_to_be_an_unknown_numeric_value_4126": "Un valor de “{0}.{1}” es la cadena “{2}” y se supone que el otro es un valor numérico desconocido.", "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091": "Solo se permite una declaración de variable en una instrucción \"for...in\".", "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188": "Solo se permite una declaración de variable en una instrucción \"for...of\".", "Only_a_void_function_can_be_called_with_the_new_keyword_2350": "Solo se puede llamar a una función void con la palabra clave \"new\".", "Only_ambient_modules_can_use_quoted_names_1035": "Solo los módulos de ambiente pueden usar nombres entrecomillados.", "Only_amd_and_system_modules_are_supported_alongside_0_6082": "Solo los módulos \"amd\" y \"system\" se admiten con --{0}.", "Only_const_arrays_can_be_inferred_with_isolatedDeclarations_9017": "Solo se pueden inferir matrices const con --isolatedDeclarations.", "Only_emit_d_ts_declaration_files_6014": "Solo deben emitirse archivos de declaración \".d.ts\".", "Only_output_d_ts_files_and_not_JavaScript_files_6623": "Genere solo archivos d.ts y no archivos JavaScript.", "Only_public_and_protected_methods_of_the_base_class_are_accessible_via_the_super_keyword_2340": "Solo es posible tener acceso a los métodos públicos y protegidos de la clase base mediante la palabra clave \"super\".", "Operator_0_cannot_be_applied_to_type_1_2736": "El operador \"{0}\" no se puede aplicar al tipo \"{1}\".", "Operator_0_cannot_be_applied_to_types_1_and_2_2365": "El operador '{0}' no se puede aplicar a los tipos '{1}' y '{2}'.", "Operators_must_not_be_mixed_within_a_character_class_Wrap_it_in_a_nested_class_instead_1519": "Los operadores no deben mezclarse dentro de una clase de caracteres. Envuelve en una clase anidada en su lugar.", "Opt_a_project_out_of_multi_project_reference_checking_when_editing_6619": "Opte por excluir un proyecto de la comprobación de referencias de varios proyectos al editar.", "Option_0_1_has_been_removed_Please_remove_it_from_your_configuration_5108": "Se ha quitado la opción “{0}={1}”. Elimínela de la configuración.", "Option_0_1_is_deprecated_and_will_stop_functioning_in_TypeScript_2_Specify_compilerOption_ignoreDepr_5107": "La opción “{0}={1}” está en desuso y dejará de funcionar en TypeScript {2}. Especifique compilerOption “'ignoreDeprecations': '{3}'” para silenciar este error.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_false_or_null_on_command_line_6230": "La opción \"{0}\" solo puede especificarse en el archivo \"tsconfig.json\" o establecerse en \"false\" o \"null\" en la línea de comandos.", "Option_0_can_only_be_specified_in_tsconfig_json_file_or_set_to_null_on_command_line_6064": "La opción \"{0}\" solo puede especificarse en el archivo \"tsconfig.json\" o establecerse en \"null\" en la línea de comandos.", "Option_0_can_only_be_specified_on_command_line_6266": "La opción “{0}” solo se puede especificar en la línea de comandos.", "Option_0_can_only_be_used_when_either_option_inlineSourceMap_or_option_sourceMap_is_provided_5051": "La opción '{0}' solo se puede usar cuando se proporciona '--inlineSourceMap' o '--sourceMap'.", "Option_0_can_only_be_used_when_moduleResolution_is_set_to_node16_nodenext_or_bundler_5098": "La opción “{0}” solo se puede usar cuando “moduleResolution” está establecido en “node16”, “nodenext” o “bundler”.", "Option_0_can_only_be_used_when_module_is_set_to_preserve_or_to_es2015_or_later_5095": "La opción “{0}” solo se puede usar cuando “module” está establecido en “preserve” o en “es2015” o posterior.", "Option_0_cannot_be_specified_when_option_jsx_is_1_5089": "No se puede especificar la opción \"{0}\" cuando la opción \"jsx\" es \"{1}\".", "Option_0_cannot_be_specified_with_option_1_5053": "La opción '{0}' no se puede especificar con la opción '{1}'.", "Option_0_cannot_be_specified_without_specifying_option_1_5052": "La opción '{0}' no se puede especificar sin la opción '{1}'.", "Option_0_cannot_be_specified_without_specifying_option_1_or_option_2_5069": "La opción \"{0}\" no se puede especificar sin la opción \"{1}\" o la opción \"{2}\".", "Option_0_has_been_removed_Please_remove_it_from_your_configuration_5102": "Se ha quitado la opción “{0}”. Elimínela de la configuración.", "Option_0_is_deprecated_and_will_stop_functioning_in_TypeScript_1_Specify_compilerOption_ignoreDeprec_5101": "La opción “{0}” está en desuso y dejará de funcionar en TypeScript {1}. Especifique compilerOption “'ignoreDeprecations': '{2}'” para silenciar este error.", "Option_0_is_redundant_and_cannot_be_specified_with_option_1_5104": "La opción “{0}” es redundante y no se puede especificar con la opción “{1}”.", "Option_allowImportingTsExtensions_can_only_be_used_when_either_noEmit_or_emitDeclarationOnly_is_set_5096": "La opción “allowImportingTsExtensions” solo se puede usar cuando se establece “noEmit” o “emitDeclarationOnly”.", "Option_build_must_be_the_first_command_line_argument_6369": "La opción \"--build\" debe ser el primer argumento de la línea de comandos.", "Option_incremental_can_only_be_specified_using_tsconfig_emitting_to_single_file_or_when_option_tsBui_5074": "La opción \"--incremental\" solo puede especificarse si se usa tsconfig, se emite en un solo archivo o se especifica la opción \"--tsBuildInfoFile\".", "Option_isolatedModules_can_only_be_used_when_either_option_module_is_provided_or_option_target_is_ES_5047": "La opción \"isolatedModules\" solo se puede usar cuando se proporciona la opción \"--module\" o si la opción \"target\" es \"ES2015\" o una versión posterior.", "Option_moduleResolution_must_be_set_to_0_or_left_unspecified_when_option_module_is_set_to_1_5109": "La opción “moduleResolution” debe establecerse en “{0}” (o no se especificó) cuando la opción “module” está establecida en “{1}”.", "Option_module_must_be_set_to_0_when_option_moduleResolution_is_set_to_1_5110": "La opción “module” debe establecerse en “{0}” cuando la opción “moduleResolution” esté establecida en “{1}”.", "Option_preserveConstEnums_cannot_be_disabled_when_0_is_enabled_5091": "La opción “preserveConstEnums” no se puede deshabilitar cuando “{0}” está habilitado.", "Option_project_cannot_be_mixed_with_source_files_on_a_command_line_5042": "La opción \"project\" no se puede combinar con archivos de origen en una línea de comandos.", "Option_resolveJsonModule_cannot_be_specified_when_moduleResolution_is_set_to_classic_5070": "No se puede especificar la opción “--resolveJsonModule” cuando “moduleResolution” está establecido en “classic”.", "Option_resolveJsonModule_cannot_be_specified_when_module_is_set_to_none_system_or_umd_5071": "No se puede especificar la opción “--resolveJsonModule” cuando “module” esté establecido en “none”, “system” o “umd”.", "Option_verbatimModuleSyntax_cannot_be_used_when_module_is_set_to_UMD_AMD_or_System_5105": "La opción “verbatimModuleSyntax” no se puede usar cuando “module” está establecido en “UMD”, “AMD” o “System”.", "Options_0_and_1_cannot_be_combined_6370": "\"{0}\" y \"{1}\" no se pueden combinar.", "Options_Colon_6027": "Opciones:", "Output_Formatting_6256": "Formato de salida", "Output_compiler_performance_information_after_building_6615": "Información de rendimiento resultante del compilador después de la compilación.", "Output_directory_for_generated_declaration_files_6166": "Directorio de salida para los archivos de declaración generados.", "Output_file_0_has_not_been_built_from_source_file_1_6305": "El archivo de salida \"{0}\" no se compiló desde el archivo de origen \"{1}\".", "Output_from_referenced_project_0_included_because_1_specified_1411": "La salida del proyecto \"{0}\" al que se hace referencia se ha incluido porque se ha especificado \"{1}\".", "Output_from_referenced_project_0_included_because_module_is_specified_as_none_1412": "La salida del proyecto \"{0}\" al que se hace referencia se ha incluido porque \"--module\" se ha especificado como \"none\".", "Output_more_detailed_compiler_performance_information_after_building_6632": "Produzca información más detallada del rendimiento resultante del compilador después de la compilación.", "Overload_0_of_1_2_gave_the_following_error_2772": "La sobrecarga {0} de {1}, \"{2}\", dio el error siguiente.", "Overload_signatures_must_all_be_abstract_or_non_abstract_2512": "Las signaturas de sobrecarga deben ser todas abstractas o no abstractas.", "Overload_signatures_must_all_be_ambient_or_non_ambient_2384": "Las signaturas de sobrecarga deben ser todas de ambiente o de no ambiente.", "Overload_signatures_must_all_be_exported_or_non_exported_2383": "Las signaturas de sobrecarga deben ser todas exportadas o no exportadas.", "Overload_signatures_must_all_be_optional_or_required_2386": "Las signaturas de sobrecarga deben ser todas opcionales u obligatorias.", "Overload_signatures_must_all_be_public_private_or_protected_2385": "Las signaturas de sobrecarga deben ser todas públicas, privadas o protegidas.", "Parameter_0_cannot_reference_identifier_1_declared_after_it_2373": "El parámetro \"{0}\" no puede hacer referencia al identificador \"{1}\" declarado después de este.", "Parameter_0_cannot_reference_itself_2372": "El parámetro \"{0}\" no puede hacer referencia a sí mismo.", "Parameter_0_implicitly_has_an_1_type_7006": "El parámetro '{0}' tiene un tipo '{1}' implícitamente.", "Parameter_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7044": "El parámetro \"{0}\" tiene un tipo \"{1}\" de forma implícita, pero se puede inferir un tipo más adecuado a partir del uso.", "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227": "El parámetro '{0}' no está en la misma posición que el parámetro '{1}'.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4108": "El parámetro \"{0}\" del descriptor de acceso tiene o usa el nombre \"{1}\" del módulo \"{2}\" externo, pero no se puede nombrar.", "Parameter_0_of_accessor_has_or_is_using_name_1_from_private_module_2_4107": "El parámetro \"{0}\" del descriptor de acceso tiene o usa el nombre \"{1}\" del módulo \"{2}\" privado.", "Parameter_0_of_accessor_has_or_is_using_private_name_1_4106": "El parámetro \"{0}\" del descriptor de acceso tiene o usa el nombre privado \"{1}\".", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4066": "El parámetro '{0}' de la signatura de llamada de una interfaz exportada tiene o usa el nombre '{1}' del módulo '{2}' privado.", "Parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4067": "El parámetro '{0}' de la signatura de llamada de una interfaz exportada tiene o usa el nombre privado '{1}'.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_can_4061": "El parámetro '{0}' del constructor de la clase exportada tiene o usa el nombre '{1}' del módulo {2} externo, pero no se puede nombrar.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_name_1_from_private_module_2_4062": "El parámetro '{0}' del constructor de la clase exportada tiene o usa el nombre '{1}' del módulo '{2}' privado.", "Parameter_0_of_constructor_from_exported_class_has_or_is_using_private_name_1_4063": "El parámetro '{0}' del constructor de la clase exportada tiene o usa el nombre privado '{1}'.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_name_1_from_private_mod_4064": "El parámetro '{0}' de la signatura de constructor de la interfaz exportada tiene o usa el nombre '{1}' del módulo '{2}' privado.", "Parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4065": "El parámetro '{0}' de la signatura de constructor de la interfaz exportada tiene o usa el nombre privado '{1}'.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4076": "El parámetro '{0}' de la función exportada tiene o usa el nombre '{1}' del módulo {2} externo, pero no se puede nombrar.", "Parameter_0_of_exported_function_has_or_is_using_name_1_from_private_module_2_4077": "El parámetro '{0}' de la función exportada tiene o usa el nombre '{1}' del módulo {2} privado.", "Parameter_0_of_exported_function_has_or_is_using_private_name_1_4078": "El parámetro '{0}' de la función exportada tiene o usa el nombre privado '{1}'.", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4091": "El parámetro \"{0}\" de la signatura de índice de la interfaz exportada tiene o usa el nombre \"{1}\" del módulo privado \"{2}\".", "Parameter_0_of_index_signature_from_exported_interface_has_or_is_using_private_name_1_4092": "El parámetro \"{0}\" de la signatura de índice de la interfaz exportada tiene o usa el nombre privado \"{1}\".", "Parameter_0_of_method_from_exported_interface_has_or_is_using_name_1_from_private_module_2_4074": "El parámetro '{0}' del método de la interfaz exportada tiene o usa el nombre '{1}' del módulo '{2}' privado.", "Parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4075": "El parámetro '{0}' del método de la interfaz exportada tiene o usa el nombre privado '{1}'.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_c_4071": "El parámetro '{0}' del método público de la clase exportada tiene o usa el nombre '{1}' del módulo {2} externo, pero no se puede nombrar.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4072": "El parámetro '{0}' del método público de la clase exportada tiene o usa el nombre '{1}' del módulo {2} privado.", "Parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4073": "El parámetro '{0}' del método público de la clase exportada tiene o usa el nombre privado '{1}'.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_external_module__4068": "El parámetro '{0}' del método estático público de la clase exportada tiene o usa el nombre '{1}' del módulo {2} externo, pero no se puede nombrar.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_name_1_from_private_module_2_4069": "El parámetro '{0}' del método estático público de la clase exportada tiene o usa el nombre '{1}' del módulo {2} privado.", "Parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4070": "El parámetro '{0}' del método estático público de la clase exportada tiene o usa el nombre privado '{1}'.", "Parameter_cannot_have_question_mark_and_initializer_1015": "El parámetro no puede tener un signo de interrogación y un inicializador.", "Parameter_declaration_expected_1138": "Se espera una declaración de parámetros.", "Parameter_has_a_name_but_no_type_Did_you_mean_0_Colon_1_7051": "El parámetro tiene un nombre, pero no un tipo. ¿Pretendía usar \"{0}: {1}\"?", "Parameter_modifiers_can_only_be_used_in_TypeScript_files_8012": "Los modificadores de parámetro solo se pueden usar en los archivos TypeScript.", "Parameter_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9011": "El parámetro debe tener una anotación de tipo explícita con --isolatedDeclarations.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4036": "El tipo de parámetro del establecedor público \"{0}\" de la clase exportada tiene o usa el nombre \"{1}\" del módulo \"{2}\" privado.", "Parameter_type_of_public_setter_0_from_exported_class_has_or_is_using_private_name_1_4037": "El tipo de parámetro del establecedor público \"{0}\" de la clase exportada tiene o usa el nombre privado \"{1}\".", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_name_1_from_private_mod_4034": "El tipo de parámetro del establecedor estático público \"{0}\" de la clase exportada tiene o usa el nombre \"{1}\" del módulo \"{2}\" privado.", "Parameter_type_of_public_static_setter_0_from_exported_class_has_or_is_using_private_name_1_4035": "El tipo de parámetro del establecedor estático público \"{0}\" de la clase exportada tiene o usa el nombre privado \"{1}\".", "Parse_in_strict_mode_and_emit_use_strict_for_each_source_file_6141": "Analiza en modo strict y emite \"use strict\" para cada archivo de código fuente.", "Part_of_files_list_in_tsconfig_json_1409": "Parte de la lista de \"archivos\" de tsconfig.json", "Pattern_0_can_have_at_most_one_Asterisk_character_5061": "El patrón \"{0}\" puede tener un carácter '*' como máximo.", "Performance_timings_for_diagnostics_or_extendedDiagnostics_are_not_available_in_this_session_A_nativ_6386": "Los intervalos de rendimiento de \"--diagnostics\" o \"--extendedDiagnostics\" no están disponibles en esta sesión. No se encontró ninguna implementación nativa de la API de rendimiento web.", "Platform_specific_6912": "Específico de plataforma", "Prefix_0_with_an_underscore_90025": "Prefijo \"{0}\" con guion bajo", "Prefix_all_incorrect_property_declarations_with_declare_95095": "Agregar el prefijo \"declare\" a todas las declaraciones de propiedad <PERSON>as", "Prefix_all_unused_declarations_with_where_possible_95025": "Agregar \"_\" como prefijo a todas las declaraciones sin usar, cuando sea posible", "Prefix_with_declare_95094": "Agregar el prefijo \"declare\"", "Preserve_unused_imported_values_in_the_JavaScript_output_that_would_otherwise_be_removed_1449": "Conserva los valores importados no usados en la salida de JavaScript que, de lo contrario, se quitarían.", "Print_all_of_the_files_read_during_the_compilation_6653": "Imprima todos los archivos leídos durante la compilación.", "Print_files_read_during_the_compilation_including_why_it_was_included_6631": "Imprima los archivos leídos durante la compilación, incluyendo la razón por la que se incluyó.", "Print_names_of_files_and_the_reason_they_are_part_of_the_compilation_6505": "Imprima los nombres de los archivos y el motivo por el que forman parte de la compilación.", "Print_names_of_files_part_of_the_compilation_6155": "Imprimir los nombres de los archivos que forman parte de la compilación.", "Print_names_of_files_that_are_part_of_the_compilation_and_then_stop_processing_6503": "Imprima los nombres de los archivos que forman parte de la compilación y, a continuación, detenga el procesamiento.", "Print_names_of_generated_files_part_of_the_compilation_6154": "Imprimir los nombres de los archivos generados que forman parte de la compilación.", "Print_the_compiler_s_version_6019": "Imprima la versión del compilador.", "Print_the_final_configuration_instead_of_building_1350": "Imprima la configuración final en lugar de compilar.", "Print_the_names_of_emitted_files_after_a_compilation_6652": "Imprima los nombres de los archivos emitidos después de una compilación.", "Print_this_message_6017": "Imprima este mensaje.", "Private_accessor_was_defined_without_a_getter_2806": "El descriptor de acceso privado se ha definido sin un captador.", "Private_field_0_must_be_declared_in_an_enclosing_class_1111": "El campo privado “{0}” debe declararse en una clase envolvente.", "Private_identifiers_are_not_allowed_in_variable_declarations_18029": "No se permiten identificadores privados en las declaraciones de variables.", "Private_identifiers_are_not_allowed_outside_class_bodies_18016": "No se permiten identificadores privados fuera de los cuerpos de clase.", "Private_identifiers_are_only_allowed_in_class_bodies_and_may_only_be_used_as_part_of_a_class_member__1451": "Los identificadores privados solo están permitidos en cuerpos de clase y solo se pueden utilizan como parte de una declaración de un miembro de clase, acceso de propiedad o en la parte izquierda de una expresión \"in\".", "Private_identifiers_are_only_available_when_targeting_ECMAScript_2015_and_higher_18028": "Los identificadores privados solo están disponibles cuando el destino es ECMAScript 2015 y versiones posteriores.", "Private_identifiers_cannot_be_used_as_parameters_18009": "Los identificadores privados no se pueden usar como parámetros.", "Private_or_protected_member_0_cannot_be_accessed_on_a_type_parameter_4105": "No se puede acceder al miembro \"{0}\" privado o protegido en un parámetro de tipo.", "Project_0_can_t_be_built_because_its_dependency_1_has_errors_6363": "Project '{0}' can't be built because its dependency '{1}' has errors", "Project_0_can_t_be_built_because_its_dependency_1_was_not_built_6383": "Project '{0}' can't be built because its dependency '{1}' was not built", "Project_0_is_being_forcibly_rebuilt_6388": "El proyecto \"{0}\" se está recompilando de manera forzada.", "Project_0_is_out_of_date_because_1_6420": "El proyecto “{0}” no está actualizado porque {1}.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_file_2_was_root_file_of_compilation_6412": "El proyecto “{0}” no está actualizado porque el archivo buildinfo “{1}” indica que el archivo “{2}” era el archivo raíz de la compilación, pero ya no.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_program_needs_to_report_errors_6419": "El “{0}” del proyecto no está actualizado porque el archivo buildinfo “{1}” indica que el programa debe informar de errores.", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_that_some_of_the_changes_were_not_emitte_6399": "El proyecto \"{0}\" no está actualizado porque el archivo buildinfo \"{1}\" indica que algunos de los cambios no se emitieron", "Project_0_is_out_of_date_because_buildinfo_file_1_indicates_there_is_change_in_compilerOptions_6406": "El proyecto “{0}” no está actualizado porque el archivo buildinfo “{1}” indica que hay cambios en compilerOptions", "Project_0_is_out_of_date_because_its_dependency_1_is_out_of_date_6353": "El proyecto \"{0}\" está obsoleto porque su dependencia \"{1}\" no está actualizada", "Project_0_is_out_of_date_because_output_1_is_older_than_input_2_6350": "El proyecto \"{0}\" está obsoleto porque la salida \"{1}\" es anterior a la entrada \"{2}\"", "Project_0_is_out_of_date_because_output_file_1_does_not_exist_6352": "El proyecto \"{0}\" está obsoleto porque el archivo de salida \"{1}\" no existe", "Project_0_is_out_of_date_because_output_for_it_was_generated_with_version_1_that_differs_with_curren_6381": "El proyecto \"{0}\" está obsoleto porque su salida se generó con la versión \"{1}\", que es distinta a la versión actual \"{2}\".", "Project_0_is_out_of_date_because_there_was_error_reading_file_1_6401": "El proyecto \"{0}\" no está actualizado porque se produjo un error al leer el archivo \"{1}\"", "Project_0_is_up_to_date_6361": "El proyecto \"{0}\" está actualizado", "Project_0_is_up_to_date_because_newest_input_1_is_older_than_output_2_6351": "El proyecto \"{0}\" está actualizado porque la entrada más reciente \"{1}\" es anterior a la salida \"{2}\"", "Project_0_is_up_to_date_but_needs_to_update_timestamps_of_output_files_that_are_older_than_input_fil_6400": "El proyecto \"{0}\" está actualizado, pero debe actualizar las marcas de tiempo de los archivos de salida anteriores a los archivos de entrada", "Project_0_is_up_to_date_with_d_ts_files_from_its_dependencies_6354": "El proyecto \"{0}\" está actualizado con archivos .d.ts de sus dependencias", "Project_references_may_not_form_a_circular_graph_Cycle_detected_Colon_0_6202": "Las referencias del proyecto no pueden formar un gráfico circular. Ciclo detectado: {0}", "Projects_6255": "Proyectos", "Projects_in_this_build_Colon_0_6355": "Proyectos de esta compilación: {0}", "Properties_with_the_accessor_modifier_are_only_available_when_targeting_ECMAScript_2015_and_higher_18045": "Las propiedades con el modificador 'accessor' solo están disponibles cuando el destino es ECMAScript 2015 y versiones posteriores.", "Property_0_cannot_have_an_initializer_because_it_is_marked_abstract_1267": "La propiedad '{0}' no puede tener un mediador porque se marca como abstracto.", "Property_0_comes_from_an_index_signature_so_it_must_be_accessed_with_0_4111": "La propiedad \"{0}\" procede de una signatura de índice, por lo que debe accederse a ella con [\"{0}\"].", "Property_0_does_not_exist_on_type_1_2339": "La propiedad '{0}' no existe en el tipo '{1}'.", "Property_0_does_not_exist_on_type_1_Did_you_mean_2_2551": "La propiedad \"{0}\" no existe en el tipo \"{1}\". ¿Quería decir \"{2}\"?", "Property_0_does_not_exist_on_type_1_Did_you_mean_to_access_the_static_member_2_instead_2576": "La propiedad \"{0}\" no existe en el tipo \"{1}\". ¿Pretendía acceder al miembro estático \"{2}\"?", "Property_0_does_not_exist_on_type_1_Do_you_need_to_change_your_target_library_Try_changing_the_lib_c_2550": "No existe la propiedad \"{0}\" en el tipo \"{1}\". ¿Necesita cambiar la biblioteca de destino? Pruebe a cambiar la opción del compilador \"lib\" a \"{2}\" o posterior.", "Property_0_does_not_exist_on_type_1_Try_changing_the_lib_compiler_option_to_include_dom_2812": "La propiedad \"{0}\" no existe en el tipo \"{1}\". Intente cambiar la opción del compilador \"lib\" para incluir \"dom\".", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_a_class_static_block_2817": "La propiedad '{0}' no tiene inicializador y no está asignada de forma definitiva en el bloque estático de clase.", "Property_0_has_no_initializer_and_is_not_definitely_assigned_in_the_constructor_2564": "La propiedad \"{0}\" no tiene inicializador y no está asignada de forma definitiva en el constructor.", "Property_0_implicitly_has_type_any_because_its_get_accessor_lacks_a_return_type_annotation_7033": "La propiedad '{0}' tiene el tipo 'any' de forma implícita, porque a su descriptor de acceso get le falta una anotación de tipo de valor devuelto.", "Property_0_implicitly_has_type_any_because_its_set_accessor_lacks_a_parameter_type_annotation_7032": "La propiedad '{0}' tiene el tipo 'any' de forma implícita, porque a su descriptor de acceso set le falta una anotación de tipo de parámetro.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_get_accessor_may_be_inferred_from_usage_7048": "La propiedad \"{0}\" tiene el tipo \"any\" de forma implícita, pero se puede inferir un tipo más adecuado para su descriptor de acceso get a partir del uso.", "Property_0_implicitly_has_type_any_but_a_better_type_for_its_set_accessor_may_be_inferred_from_usage_7049": "La propiedad \"{0}\" tiene el tipo \"any\" de forma implícita, pero se puede inferir un tipo más adecuado para su descriptor de acceso set a partir del uso.", "Property_0_in_type_1_is_not_assignable_to_the_same_property_in_base_type_2_2416": "La propiedad \"{0}\" del tipo \"{1}\" no se puede asignar a la misma propiedad del tipo base \"{2}\".", "Property_0_in_type_1_is_not_assignable_to_type_2_2603": "La propiedad \"{0}\" del tipo \"{1}\" no se puede asignar al tipo \"{2}\".", "Property_0_in_type_1_refers_to_a_different_member_that_cannot_be_accessed_from_within_type_2_18015": "La propiedad \"{0}\" del tipo \"{1}\" hace referencia a un miembro distinto al que no se puede acceder desde el tipo \"{2}\".", "Property_0_is_declared_but_its_value_is_never_read_6138": "La propiedad \"{0}\" se declara, pero su valor no se lee nunca.", "Property_0_is_incompatible_with_index_signature_2530": "La propiedad '{0}' es incompatible con la signatura de índice.", "Property_0_is_missing_in_type_1_2324": "Falta la propiedad '{0}' en el tipo '{1}'.", "Property_0_is_missing_in_type_1_but_required_in_type_2_2741": "La propiedad \"{0}\" falta en el tipo \"{1}\", pero es obligatoria en el tipo \"{2}\".", "Property_0_is_not_accessible_outside_class_1_because_it_has_a_private_identifier_18013": "No se puede acceder a la propiedad \"{0}\" fuera de la clase \"{1}\" porque tiene un identificador privado.", "Property_0_is_optional_in_type_1_but_required_in_type_2_2327": "La propiedad '{0}' es opcional en el tipo '{1}', pero obligatoria en el tipo '{2}'.", "Property_0_is_private_and_only_accessible_within_class_1_2341": "La propiedad '{0}' es privada y solo se puede acceder a ella en la clase '{1}'.", "Property_0_is_private_in_type_1_but_not_in_type_2_2325": "La propiedad '{0}' es privada en el tipo '{1}', pero no en el tipo '{2}'.", "Property_0_is_protected_and_only_accessible_through_an_instance_of_class_1_This_is_an_instance_of_cl_2446": "La propiedad \"{0}\" está protegida y solo puede accederse a ella a través de una instancia de la clase \"{1}\". Esta es una instancia de la clase \"{2}\".", "Property_0_is_protected_and_only_accessible_within_class_1_and_its_subclasses_2445": "La propiedad '{0}' está protegida y solo se puede acceder a ella en la clase '{1}' y las subclases de esta.", "Property_0_is_protected_but_type_1_is_not_a_class_derived_from_2_2443": "La propiedad '{0}' está protegida, pero el tipo '{1}' no es una clase derivada de '{2}'.", "Property_0_is_protected_in_type_1_but_public_in_type_2_2444": "La propiedad '{0}' está protegida en el tipo '{1}', pero es pública en el tipo '{2}'.", "Property_0_is_used_before_being_assigned_2565": "La propiedad \"{0}\" se usa antes de asignarla.", "Property_0_is_used_before_its_initialization_2729": "La propiedad \"{0}\" se usa antes de su inicialización.", "Property_0_may_not_exist_on_type_1_Did_you_mean_2_2568": "La propiedad \"{0}\" no existe en el tipo \"{1}\". ¿Quería decir \"{2}\"?", "Property_0_of_JSX_spread_attribute_is_not_assignable_to_target_property_2606": "La propiedad '{0}' del atributo spread de JSX no se puede asignar a la propiedad de destino.", "Property_0_of_exported_anonymous_class_type_may_not_be_private_or_protected_4094": "La propiedad “{0}” del tipo de clase anónima exportada no puede ser privada ni estar protegida.", "Property_0_of_exported_interface_has_or_is_using_name_1_from_private_module_2_4032": "La propiedad '{0}' de la interfaz exportada tiene o usa el nombre '{1}' del módulo '{2}' privado.", "Property_0_of_exported_interface_has_or_is_using_private_name_1_4033": "La propiedad '{0}' de la interfaz exportada tiene o usa el nombre privado '{1}'.", "Property_0_of_type_1_is_not_assignable_to_2_index_type_3_2411": "La propiedad \"{0}\" de tipo \"{1}\" no se puede asignar al tipo de índice \"{2}\" \"{3}\".", "Property_0_was_also_declared_here_2733": "La propiedad \"{0}\" también se ha declarado aquí.", "Property_0_will_overwrite_the_base_property_in_1_If_this_is_intentional_add_an_initializer_Otherwise_2612": "La propiedad \"{0}\" sobrescribirá la propiedad base en \"{1}\" Si esto es intencionado, agregue un inicializador. De lo contrario, agregue un modificador \"declare\" o quite la declaración redundante.", "Property_assignment_expected_1136": "Se esperaba una asignación de propiedad.", "Property_destructuring_pattern_expected_1180": "Se esperaba un patrón de desestructuración de propiedad.", "Property_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9012": "La propiedad debe tener una anotación de tipo explícita con --isolatedDeclarations.", "Property_or_signature_expected_1131": "Se esperaba una propiedad o una signatura.", "Property_value_can_only_be_string_literal_numeric_literal_true_false_null_object_literal_or_array_li_1328": "El valor de la propiedad puede ser solo un literal de cadena, literal numérico, 'true', 'false', 'null', literal de objeto o literal de matriz.", "Provide_full_support_for_iterables_in_for_of_spread_and_destructuring_when_targeting_ES5_6179": "Proporcionar compatibilidad total con objetos iterables en “for-of”, propagaciones y desestructuraciones cuando el destino es “ES5”.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_named_4098": "El método público \"{0}\" de la clase exportada tiene o usa el nombre \"{1}\" del módulo externo {2}, pero no puede tener nombre.", "Public_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4099": "El método público \"{0}\" de la clase exportada tiene o usa el nombre \"{1}\" del módulo privado \"{2}\".", "Public_method_0_of_exported_class_has_or_is_using_private_name_1_4100": "El método público \"{0}\" de la clase exportada tiene o usa el nombre privado \"{1}\".", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_name_4029": "La propiedad pública '{0}' de la clase exportada tiene o usa el nombre '{1}' del módulo {2} externo, pero no se puede nombrar.", "Public_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4030": "La propiedad pública '{0}' de la clase exportada tiene o usa el nombre '{1}' del módulo '{2}' privado.", "Public_property_0_of_exported_class_has_or_is_using_private_name_1_4031": "La propiedad pública '{0}' de la clase exportada tiene o usa el nombre privado '{1}'.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot_be_4095": "El método estático público \"{0}\" de la clase exportada tiene o usa el nombre \"{1}\" del módulo externo {2}, pero no puede tener nombre.", "Public_static_method_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4096": "El método estático público \"{0}\" de la clase exportada tiene o usa el nombre \"{1}\" del módulo privado \"{2}\".", "Public_static_method_0_of_exported_class_has_or_is_using_private_name_1_4097": "El método estático público \"{0}\" de la clase exportada tiene o usa el nombre privado \"{1}\".", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_external_module_2_but_cannot__4026": "La propiedad estática pública '{0}' de la clase exportada tiene o usa el nombre '{1}' del módulo {2} externo, pero no se puede nombrar.", "Public_static_property_0_of_exported_class_has_or_is_using_name_1_from_private_module_2_4027": "La propiedad estática pública '{0}' de la clase exportada tiene o usa el nombre '{1}' del módulo {2} privado.", "Public_static_property_0_of_exported_class_has_or_is_using_private_name_1_4028": "La propiedad estática pública '{0}' de la clase exportada tiene o usa el nombre privado '{1}'.", "Qualified_name_0_is_not_allowed_without_a_leading_param_object_1_8032": "No se permite el nombre calificado \"{0}\" sin un elemento \"@param {object} {1}\" inicial.", "Raise_an_error_when_a_function_parameter_isn_t_read_6676": "Genera un error cuando no se lee un parámetro de función.", "Raise_error_on_expressions_and_declarations_with_an_implied_any_type_6052": "Generar un error en las expresiones y las declaraciones con un tipo \"any\" implícito.", "Raise_error_on_this_expressions_with_an_implied_any_type_6115": "Generar un error en expresiones 'this' con un tipo 'any' implícito.", "Range_out_of_order_in_character_class_1517": "<PERSON>ngo des<PERSON> en la clase de caracteres.", "Re_exporting_a_type_when_0_is_enabled_requires_using_export_type_1205": "Volver a exportar un tipo cuando “{0}” está habilitado requiere el uso de “export type”.", "React_components_cannot_include_JSX_namespace_names_2639": "Los componentes de React no pueden incluir nombres de espacio de nombres JSX", "Redirect_output_structure_to_the_directory_6006": "Redirija la estructura de salida al directorio.", "Reduce_the_number_of_projects_loaded_automatically_by_TypeScript_6617": "Reduzca el número de proyectos cargados automáticamente por TypeScript.", "Referenced_project_0_may_not_disable_emit_6310": "El proyecto \"{0}\" al que se hace referencia no puede deshabilitar la emisión.", "Referenced_project_0_must_have_setting_composite_Colon_true_6306": "El proyecto \"{0}\" al que se hace referencia debe tener el valor \"composite\": true.", "Referenced_via_0_from_file_1_1400": "Se hace referencia mediante \"{0}\" desde el archivo \"{1}\".", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2834": "Las rutas de acceso de importación relativas necesitan extensiones de archivo explícitas en las importaciones ECMAScript cuando “--moduleResolution” es “node16” o “nodenext”. Considere la posibilidad de agregar una extensión a la ruta de acceso de importación.", "Relative_import_paths_need_explicit_file_extensions_in_ECMAScript_imports_when_moduleResolution_is_n_2835": "Las rutas de acceso de importación relativas necesitan extensiones de archivo explícitas en las importaciones ECMAScript cuando “--moduleResolution” es “node16” o “nodenext”. ¿Quiso decir “{0}”?", "Remove_a_list_of_directories_from_the_watch_process_6628": "Quite una lista de directorios del proceso de inspección.", "Remove_a_list_of_files_from_the_watch_mode_s_processing_6629": "Quite una lista de archivos del procesamiento del modo de inspección.", "Remove_all_unnecessary_override_modifiers_95163": "<PERSON><PERSON>ar todos los modificadores \"override\" innecesarios", "Remove_all_unnecessary_uses_of_await_95087": "<PERSON><PERSON><PERSON> todos los usos innecesarios de \"await\"", "Remove_all_unreachable_code_95051": "Quitar todo el código inaccesible", "Remove_all_unused_labels_95054": "Quitar todas las etiquetas no utilizadas", "Remove_braces_from_all_arrow_function_bodies_with_relevant_issues_95115": "Quitar las llaves de todos los cuerpos de función de flecha con problemas relevantes", "Remove_braces_from_arrow_function_95060": "Quitar las llaves de la función de flecha", "Remove_braces_from_arrow_function_body_95112": "Quitar las llaves del cuerpo de función de flecha", "Remove_import_from_0_90005": "Quitar importación de \"{0}\"", "Remove_override_modifier_95161": "Quitar el modificador \"override\"", "Remove_parentheses_95126": "Quitar los paréntesis", "Remove_template_tag_90011": "Quitar la etiqueta de plantilla", "Remove_the_20mb_cap_on_total_source_code_size_for_JavaScript_files_in_the_TypeScript_language_server_6618": "Elimine el límite de 20 MB del tamaño total del código fuente para los archivos JavaScript en el servidor de lenguaje TypeScript.", "Remove_type_from_import_declaration_from_0_90055": "Quitar \"type\" de la declaración de importación de \"{0}\"", "Remove_type_from_import_of_0_from_1_90056": "Quitar \"type\" de la importación de '{0}' de \"{1}\"", "Remove_type_parameters_90012": "Quitar los parámetros de tipo", "Remove_unnecessary_await_95086": "<PERSON><PERSON><PERSON> elementos \"await\" innecesarios", "Remove_unreachable_code_95050": "Quitar el código inaccesible", "Remove_unused_declaration_for_Colon_0_90004": "Quitar la declaración sin usar para \"{0}\"", "Remove_unused_declarations_for_Colon_0_90041": "Quite las declaraciones sin usar para \"{0}\"", "Remove_unused_destructuring_declaration_90039": "Quite la declaración de desestructuración no utilizada", "Remove_unused_label_95053": "Quitar etiqueta no utilizada", "Remove_variable_statement_90010": "Quitar la declaración de variable", "Rename_param_tag_name_0_to_1_95173": "Cambiar el nombre de la etiqueta \"@param\" \"{0}\" a \"{1}\"", "Replace_0_with_Promise_1_90036": "Reemplazar \"{0}\" por \"Promise<{1}>\"", "Replace_all_unused_infer_with_unknown_90031": "Reem<PERSON><PERSON><PERSON> todos los elementos \"infer\" sin usar por \"unknown\"", "Replace_import_with_0_95015": "Reemplazar importación por \"{0}\".", "Replace_infer_0_with_unknown_90030": "Reemplazar \"infer {0}\" por \"unknown\"", "Report_error_when_not_all_code_paths_in_function_return_a_value_6075": "Notificar un error cuando no todas las rutas de acceso de código en funcionamiento devuelven un valor.", "Report_errors_for_fallthrough_cases_in_switch_statement_6076": "Notificar errores de los casos de fallthrough en la instrucción switch.", "Report_errors_in_js_files_8019": "Notifique los errores de los archivos .js.", "Report_errors_on_unused_locals_6134": "Informe de errores sobre variables locales no usadas.", "Report_errors_on_unused_parameters_6135": "Informe de errores sobre parámetros no usados.", "Require_sufficient_annotation_on_exports_so_other_tools_can_trivially_generate_declaration_files_6719": "Requiere una anotación suficiente en las exportaciones para que otras herramientas puedan generar archivos de declaración de forma trivial.", "Require_undeclared_properties_from_index_signatures_to_use_element_accesses_6717": "Se requieren propiedades no declaradas de las signaturas de índice para usar los accesos de elemento.", "Required_type_parameters_may_not_follow_optional_type_parameters_2706": "Los parámetros de tipo requeridos pueden no seguir parámetros de tipo opcionales.", "Resolution_for_module_0_was_found_in_cache_from_location_1_6147": "La resolución del módulo \"{0}\" se encontró en la memoria caché de la ubicación \"{1}\".", "Resolution_for_type_reference_directive_0_was_found_in_cache_from_location_1_6241": "La resolución de la directiva de referencia de tipo \"{0}\" se encontró en la memoria caché de la ubicación \"{1}\".", "Resolution_of_non_relative_name_failed_trying_with_modern_Node_resolution_features_disabled_to_see_i_6277": "Error en la resolución del nombre no relativo; probando con las características modernas de resolución de nodos deshabilitadas para ver si la biblioteca npm necesita una actualización de la configuración.", "Resolution_of_non_relative_name_failed_trying_with_moduleResolution_bundler_to_see_if_project_may_ne_6279": "Error en la resolución del nombre no relativo; intentando con “--moduleResolution bundler” para ver si el proyecto puede necesitar una actualización de la configuración.", "Resolve_keyof_to_string_valued_property_names_only_no_numbers_or_symbols_6195": "Resolver \"keyof\" exclusivamente como nombres de propiedad con valores de cadena (sin números ni símbolos).", "Resolved_under_condition_0_6414": "Resuelto bajo condición “{0}”.", "Resolving_in_0_mode_with_conditions_1_6402": "Resolviendo en modo {0} con condiciones {1}.", "Resolving_module_0_from_1_6086": "======== Resolviendo el módulo '{0}' de '{1}'. ========", "Resolving_module_name_0_relative_to_base_url_1_2_6094": "Resolviendo el nombre de módulo '{0}' relativo a la dirección URL base '{1}' - '{2}'.", "Resolving_real_path_for_0_result_1_6130": "Resolviendo la ruta de acceso real de \"{0}\", resultado: \"{1}\".", "Resolving_type_reference_directive_0_containing_file_1_6242": "======== Resolviendo la directiva de referencia de tipo \"{0}\", archivo contenedor \"{1}\". ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_2_6116": "======== Resolviendo la directiva de referencia de tipo '{0}', archivo contenedor: '{1}', directorio raíz: '{2}'. ========", "Resolving_type_reference_directive_0_containing_file_1_root_directory_not_set_6123": "======== Resolviendo la directiva de referencia de tipo '{0}', archivo contenedor: '{1}', directorio raíz no establecido. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_1_6127": "======== Resolviendo la directiva de referencia de tipo '{0}', archivo contenedor no establecido, directorio raíz: '{1}'. ========", "Resolving_type_reference_directive_0_containing_file_not_set_root_directory_not_set_6128": "======== Resolviendo la directiva de referencia de tipo '{0}', archivo contenedor no establecido, directorio raíz no establecido. ========", "Resolving_type_reference_directive_for_program_that_specifies_custom_typeRoots_skipping_lookup_in_no_6265": "Resolviendo la directiva de referencia de tipo para el programa que especifica typeRoots personalizado, omitiendo la búsqueda en la carpeta “node_modules”.", "Resolving_with_primary_search_path_0_6121": "Resolviendo con la ruta de búsqueda principal \"{0}\".", "Rest_parameter_0_implicitly_has_an_any_type_7019": "El parámetro rest '{0}' tiene un tipo \"any[]\" implícitamente.", "Rest_parameter_0_implicitly_has_an_any_type_but_a_better_type_may_be_inferred_from_usage_7047": "El parámetro rest \"{0}\" tiene un tipo \"any[]\" de forma implícita, pero se puede inferir un tipo más adecuado a partir del uso.", "Rest_types_may_only_be_created_from_object_types_2700": "Los tipos rest solo se pueden crear a partir de tipos de objeto.", "Return_type_annotation_circularly_references_itself_2577": "La anotación de tipo de valor devuelto se hace referencia a sí misma de forma circular.", "Return_type_must_be_inferred_from_a_function_95149": "El tipo de valor devuelto debe inferirse de una función", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4046": "El tipo de valor devuelto de la signatura de llamada de la interfaz exportada tiene o usa el nombre '{0}' del módulo '{1}' privado.", "Return_type_of_call_signature_from_exported_interface_has_or_is_using_private_name_0_4047": "El tipo de valor devuelto de la signatura de llamada de la interfaz exportada tiene o usa el nombre privado '{0}'.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_name_0_from_private_mod_4044": "El tipo de valor devuelto de la signatura de constructor de la interfaz exportada tiene o usa el nombre '{0}' del módulo '{1}' privado.", "Return_type_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_0_4045": "El tipo de valor devuelto de la signatura de constructor de la interfaz exportada tiene o usa el nombre privado '{0}'.", "Return_type_of_constructor_signature_must_be_assignable_to_the_instance_type_of_the_class_2409": "El tipo de valor devuelto de la signatura de constructor se debe poder asignar al tipo de instancia de la clase.", "Return_type_of_exported_function_has_or_is_using_name_0_from_external_module_1_but_cannot_be_named_4058": "El tipo de valor devuelto de la función exportada tiene o usa el nombre '{0}' del módulo {1} externo, pero no se puede nombrar.", "Return_type_of_exported_function_has_or_is_using_name_0_from_private_module_1_4059": "El tipo de valor devuelto de la función exportada tiene o usa el nombre '{0}' del módulo {1} privado.", "Return_type_of_exported_function_has_or_is_using_private_name_0_4060": "El tipo de valor devuelto de la función exportada tiene o usa el nombre privado '{0}'.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4048": "El tipo de valor devuelto de la signatura de índice de la interfaz exportada tiene o usa el nombre '{0}' del módulo '{1}' privado.", "Return_type_of_index_signature_from_exported_interface_has_or_is_using_private_name_0_4049": "El tipo de valor devuelto de la signatura de índice de la interfaz exportada tiene o usa el nombre privado '{0}'.", "Return_type_of_method_from_exported_interface_has_or_is_using_name_0_from_private_module_1_4056": "El tipo de valor devuelto del método de la interfaz exportada tiene o usa el nombre '{0}' del módulo '{1}' privado.", "Return_type_of_method_from_exported_interface_has_or_is_using_private_name_0_4057": "El tipo de valor devuelto del método de la interfaz exportada tiene o usa el nombre privado '{0}'.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_external_module_2_but_4041": "El tipo de valor devuelto del captador público \"{0}\" de la clase exportada tiene o usa el nombre \"{1}\" del módulo {2} externo, pero no se puede nombrar.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_2_4042": "El tipo de valor devuelto del captador público \"{0}\" de la clase exportada tiene o usa el nombre \"{1}\" del módulo \"{2}\" privado.", "Return_type_of_public_getter_0_from_exported_class_has_or_is_using_private_name_1_4043": "El tipo de valor devuelto del captador público \"{0}\" de la clase exportada tiene o usa el nombre privado \"{1}\".", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_external_module_1_but_c_4053": "El tipo de valor devuelto del método público de la clase exportada tiene o usa el nombre '{0}' del módulo {1} externo, pero no se puede nombrar.", "Return_type_of_public_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4054": "El tipo de valor devuelto del método público de la clase exportada tiene o usa el nombre '{0}' del módulo {1} privado.", "Return_type_of_public_method_from_exported_class_has_or_is_using_private_name_0_4055": "El tipo de valor devuelto del método público de la clase exportada tiene o usa el nombre privado '{0}'.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_external_modul_4038": "El tipo de valor devuelto del captador estático público \"{0}\" de la clase exportada tiene o usa el nombre \"{1}\" del módulo {2} externo, pero no se puede nombrar.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_name_1_from_private_module_4039": "El tipo de valor devuelto del captador estático público \"{0}\" de la clase exportada tiene o usa el nombre \"{1}\" del módulo \"{2}\" privado.", "Return_type_of_public_static_getter_0_from_exported_class_has_or_is_using_private_name_1_4040": "El tipo de valor devuelto del captador estático público \"{0}\" de la clase exportada tiene o usa el nombre privado \"{1}\".", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_external_module__4050": "El tipo de valor devuelto del método estático público de la clase exportada tiene o usa el nombre '{0}' del módulo {1} externo, pero no se puede nombrar.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_name_0_from_private_module_1_4051": "El tipo de valor devuelto del método estático público de la clase exportada tiene o usa el nombre '{0}' del módulo {1} privado.", "Return_type_of_public_static_method_from_exported_class_has_or_is_using_private_name_0_4052": "El tipo de valor devuelto del método estático público de la clase exportada tiene o usa el nombre privado '{0}'.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_not_resolved_6395": "La reutilización de la resolución del módulo \"{0}\" de \"{1}\" que se encuentra en la memoria caché desde la ubicación \"{2}\" no se resolvió.", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6393": "La reutilización de la resolución del módulo \"{0}\" de \"{1}\" que se encuentra en la memoria caché desde la ubicación \"{2}\" se resolvió correctamente en \"{3}\".", "Reusing_resolution_of_module_0_from_1_found_in_cache_from_location_2_it_was_successfully_resolved_to_6394": "La reutilización de la resolución del módulo \"{0}\" de \"{1}\" que se encuentra en la memoria caché desde la ubicación \"{2}\" se resolvió correctamente en \"{3}\" con el identificador de paquete \"{4}\".", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_not_resolved_6389": "La reutilización de la resolución del módulo \"{0}\" del programa anterior \"{1}\" no se resolvió.", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_6183": "La reutilización de la resolución del módulo \"{0}\" del programa anterior \"{1}\" se resolvió correctamente en \"{2}\".", "Reusing_resolution_of_module_0_from_1_of_old_program_it_was_successfully_resolved_to_2_with_Package__6184": "La reutilización de la resolución del módulo \"{0}\" del programa anterior \"{1}\" se resolvió correctamente en \"{2}\" con el identificador del paquete \"{3}\".", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_not_re_6398": "La reutilización de la resolución de la directiva de referencia de tipo \"{0}\" de \"{1}\" que se encuentra en la memoria caché desde la ubicación \"{2}\" no se resolvió.", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6396": "La reutilización de la resolución de la directiva de referencia de tipo \"{0}\" de \"{1}\" que se encuentra en la memoria caché desde la ubicación \"{2}\" se resolvió correctamente en \"{3}\".", "Reusing_resolution_of_type_reference_directive_0_from_1_found_in_cache_from_location_2_it_was_succes_6397": "La reutilización de la resolución de la directiva de referencia de tipo \"{0}\" de \"{1}\" que se encuentra en la memoria caché desde la ubicación \"{2}\" se resolvió correctamente en \"{3}\" con el identificador del paquete \"{4}\".", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_not_resolved_6392": "La reutilización de la resolución de la directiva de referencia de tipo \"{0}\" de \"{1}\" del programa anterior no se resolvió.", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6390": "La reutilización de la resolución de la directiva de referencia de tipo \"{0}\" de \"{1}\" del programa anterior se resolvió correctamente en \"{2}\".", "Reusing_resolution_of_type_reference_directive_0_from_1_of_old_program_it_was_successfully_resolved__6391": "La reutilización de la resolución de la directiva de referencia de tipo \"{0}\" de \"{1}\" del programa anterior se resolvió correctamente en \"{2}\" con el identificador de paquete \"{3}\".", "Rewrite_all_as_indexed_access_types_95034": "Reescribir todo como tipos de acceso indexados", "Rewrite_as_the_indexed_access_type_0_90026": "Reescribir como tipo de acceso indexado \"{0}\"", "Rewrite_ts_tsx_mts_and_cts_file_extensions_in_relative_import_paths_to_their_JavaScript_equivalent_i_6421": "Vuelva a escribir las extensiones de archivo \".ts\", \".tsx\", \".mts\" y \".cts\" en rutas de acceso de importación relativas a su equivalente de JavaScript en los archivos de salida.", "Right_operand_of_is_unreachable_because_the_left_operand_is_never_nullish_2869": "El operando derecho de ?? es inaccesible porque el operando izquierdo nunca es nulo.", "Root_directory_cannot_be_determined_skipping_primary_search_paths_6122": "No se puede determinar el directorio raíz, se omitirán las rutas de búsqueda principales.", "Root_file_specified_for_compilation_1427": "Archivo raíz especificado para la compilación", "STRATEGY_6039": "ESTRATEGIA", "Save_tsbuildinfo_files_to_allow_for_incremental_compilation_of_projects_6642": "Guarde archivos .tsbuildinfo para permitir la compilación incremental de proyectos.", "Saw_non_matching_condition_0_6405": "Se vio una condición no coincidente '{0}'.", "Scoped_package_detected_looking_in_0_6182": "Se detectó un paquete con ámbito al buscar en \"{0}\"", "Searching_all_ancestor_node_modules_directories_for_fallback_extensions_Colon_0_6418": "Buscando extensiones de reserva en todos los directorios de node_modules antecesores: {0}.", "Searching_all_ancestor_node_modules_directories_for_preferred_extensions_Colon_0_6417": "Buscando extensiones preferidas en todos los directorios de node_modules antecesores: {0}.", "Selection_is_not_a_valid_statement_or_statements_95155": "La selección no es una instrucción ni instrucciones válidas", "Selection_is_not_a_valid_type_node_95133": "La selección no es un nodo de tipo válido", "Set_the_JavaScript_language_version_for_emitted_JavaScript_and_include_compatible_library_declaratio_6705": "Establezca la versión del lenguaje de JavaScript para las JavaScript emitidas e incluya las declaraciones de bibliotecas compatibles.", "Set_the_language_of_the_messaging_from_TypeScript_This_does_not_affect_emit_6654": "Establezca el lenguaje de la mensajería de TypeScript. No afecta a la emisión.", "Set_the_module_option_in_your_configuration_file_to_0_95099": "Establecer la opción \"module\" del archivo de configuración en \"{0}\"", "Set_the_newline_character_for_emitting_files_6659": "Establezca el carácter de nueva línea para emitir archivos.", "Set_the_target_option_in_your_configuration_file_to_0_95098": "Establecer la opción \"target\" del archivo de configuración en \"{0}\"", "Setters_cannot_return_a_value_2408": "Los establecedores no pueden devolver un valor.", "Show_all_compiler_options_6169": "Mostrar todas las opciones de compilador.", "Show_diagnostic_information_6149": "Mostrar información de diagnóstico.", "Show_verbose_diagnostic_information_6150": "Mostrar información de diagnóstico detallada.", "Show_what_would_be_built_or_deleted_if_specified_with_clean_6367": "Mostrar lo que podría compilarse (o eliminarse, si se especifica con \"--clean\")", "Signature_0_must_be_a_type_predicate_1224": "La signatura '{0}' debe tener un predicado de tipo.", "Signature_declarations_can_only_be_used_in_TypeScript_files_8017": "Las declaraciones de signatura solo se pueden usar en los archivos TypeScript.", "Skip_building_downstream_projects_on_error_in_upstream_project_6640": "Omitir la compilación de proyectos que siguen en la cadena debido a un error en el proyecto ascendente.", "Skip_type_checking_all_d_ts_files_6693": "Omita la comprobación de tipos de todos los archivos .d.ts.", "Skip_type_checking_d_ts_files_that_are_included_with_TypeScript_6692": "Omita la comprobación de tipos de archivo .d.ts que se incluyen con TypeScript.", "Skip_type_checking_of_declaration_files_6012": "Omita la comprobación de tipos de los archivos de declaración.", "Skipping_build_of_project_0_because_its_dependency_1_has_errors_6362": "Skipping build of project '{0}' because its dependency '{1}' has errors", "Skipping_build_of_project_0_because_its_dependency_1_was_not_built_6382": "Skipping build of project '{0}' because its dependency '{1}' was not built", "Skipping_module_0_that_looks_like_an_absolute_URI_target_file_types_Colon_1_6164": "Omitiendo el módulo “{0}” que parece un URI absoluto, tipos de archivo de destino: {1}.", "Source_from_referenced_project_0_included_because_1_specified_1414": "El origen del proyecto \"{0}\" al que se hace referencia se ha incluido porque se ha especificado \"{1}\".", "Source_from_referenced_project_0_included_because_module_is_specified_as_none_1415": "El origen del proyecto \"{0}\" al que se hace referencia se ha incluido porque \"--module\" se ha especificado como \"none\".", "Source_has_0_element_s_but_target_allows_only_1_2619": "El origen tiene {0} elemento(s), pero el destino solo permite {1}.", "Source_has_0_element_s_but_target_requires_1_2618": "El origen tiene {0} elemento(s), pero el destino requiere {1}.", "Source_provides_no_match_for_required_element_at_position_0_in_target_2623": "El origen no proporciona ninguna coincidencia para el elemento requerido en la posición {0} del destino.", "Source_provides_no_match_for_variadic_element_at_position_0_in_target_2624": "El origen no proporciona ninguna coincidencia para el elemento variádico en la posición {0} del destino.", "Specify_ECMAScript_target_version_6015": "Especifique la versión de destino de ECMAScript.", "Specify_JSX_code_generation_6080": "Especifique la generación de código JSX.", "Specify_a_file_that_bundles_all_outputs_into_one_JavaScript_file_If_declaration_is_true_also_designa_6679": "Especifique un archivo que agrupe todas las salidas en un archivo JavaScript. Si 'declaración' es verdadera, también designa un archivo que agrupa toda la salida .d.ts.", "Specify_a_list_of_glob_patterns_that_match_files_to_be_included_in_compilation_6641": "Especifique una lista de patrones globales que coincidan con los archivos que se incluirán en la compilación.", "Specify_a_list_of_language_service_plugins_to_include_6681": "Especifique una lista de complementos de servicio de lenguaje para incluirlos.", "Specify_a_set_of_bundled_library_declaration_files_that_describe_the_target_runtime_environment_6651": "Especifique un conjunto de archivos de declaración de biblioteca agrupados que describan el entorno de tiempo de ejecución de destino.", "Specify_a_set_of_entries_that_re_map_imports_to_additional_lookup_locations_6680": "Especifique un conjunto de entradas que reasignan las importaciones a ubicaciones de búsqueda adicionales.", "Specify_an_array_of_objects_that_specify_paths_for_projects_Used_in_project_references_6687": "Especifique una matriz de objetos que especifique las rutas de acceso a los proyectos. Usada en las referencias del proyecto.", "Specify_an_output_folder_for_all_emitted_files_6678": "Especifique una carpeta de salida para todos los archivos emitidos.", "Specify_emit_Slashchecking_behavior_for_imports_that_are_only_used_for_types_6718": "Especificar el comportamiento de emisión o comprobación para las importaciones que solo se usan para los tipos.", "Specify_file_to_store_incremental_compilation_information_6380": "Especificar un archivo para almacenar la información de compilación incremental", "Specify_how_TypeScript_looks_up_a_file_from_a_given_module_specifier_6658": "Especifique cómo busca TypeScript un archivo a partir del especificador de módulo que se le indique.", "Specify_how_directories_are_watched_on_systems_that_lack_recursive_file_watching_functionality_6714": "Especifique cómo se vigilan los directorios en los sistemas que carecen de la funcionalidad de vigilancia recursiva de archivos.", "Specify_how_the_TypeScript_watch_mode_works_6715": "Especifique cómo funciona el modo de inspección de TypeScript.", "Specify_library_files_to_be_included_in_the_compilation_6079": "Especifique los archivos de biblioteca que se van a incluir en la compilación.", "Specify_module_code_generation_6016": "Especifique la generación de código del módulo.", "Specify_module_specifier_used_to_import_the_JSX_factory_functions_when_using_jsx_Colon_react_jsx_Ast_6649": "Especifique el especificador de módulo que se usa para importar las funciones de fábrica de JSX cuando se usa \"jsx: react-jsx*\".", "Specify_multiple_folders_that_act_like_Slashnode_modules_Slash_types_6710": "Especifique varias carpetas que actúen como \"./node_modules/@types\".", "Specify_one_or_more_path_or_node_module_references_to_base_configuration_files_from_which_settings_a_6633": "Especifique una o varias referencias de ruta o de módulo de nodo a los archivos de configuración base desde los que se herede la configuración.", "Specify_options_for_automatic_acquisition_of_declaration_files_6709": "Especifique las opciones para la adquisición automática de los archivos de declaración.", "Specify_strategy_for_creating_a_polling_watch_when_it_fails_to_create_using_file_system_events_Colon_6227": "Especifique la estrategia para crear una inspección de sondeo cuando no se pueda crear con eventos del sistema de archivos: \"FixedInterval\" (valor predeterminado), \"PriorityInterval\", \"DynamicPriority\", \"FixedChunkSize\".", "Specify_strategy_for_watching_directory_on_platforms_that_don_t_support_recursive_watching_natively__6226": "Especifique la estrategia para inspeccionar el directorio en las plataformas que no admiten las inspecciones recursivas de forma nativa: \"UseFsEvents\" (valor predeterminado), \"FixedPollingInterval\", \"DynamicPriorityPolling\", \"FixedChunkSizePolling\".", "Specify_strategy_for_watching_file_Colon_FixedPollingInterval_default_PriorityPollingInterval_Dynami_6225": "Especifique la estrategia para inspeccionar el archivo: \"FixedPollingInterval\" (valor predeterminado), \"PriorityPollingInterval\", \"DynamicPriorityPolling\", \"FixedChunkSizePolling\", \"UseFsEvents\", \"UseFsEventsOnParentDirectory\".", "Specify_the_JSX_Fragment_reference_used_for_fragments_when_targeting_React_JSX_emit_e_g_React_Fragme_6648": "Especifique la referencia de fragmento de JSX utilizada para los fragmentos cuando se dirige a la emisión de JSX de React, por ejemplo, \"React.Fragment\" o \"Fragment\".", "Specify_the_JSX_factory_function_to_use_when_targeting_react_JSX_emit_e_g_React_createElement_or_h_6146": "Especifique la función de generador JSX que se usará cuando el destino sea la emisión de JSX \"react\"; por eje<PERSON><PERSON>, \"React.createElement\" o \"h\".", "Specify_the_JSX_factory_function_used_when_targeting_React_JSX_emit_e_g_React_createElement_or_h_6647": "Especifique la función de generador JSX que se usa al establecer como destino la emisión JSX de React; por ejemplo, \"React.createElement\" o \"h\".", "Specify_the_JSX_fragment_factory_function_to_use_when_targeting_react_JSX_emit_with_jsxFactory_compi_18034": "Especifique la función de la fábrica de fragmentos de JSX que se va a usar cuando se especifique como destino la emisión de JSX \"react\" con la opción del compilador \"jsxFactory\", por ejemplo, \"fragmento\".", "Specify_the_base_directory_to_resolve_non_relative_module_names_6607": "Especifique el directorio base para resolver nombres de módulos no relativos.", "Specify_the_end_of_line_sequence_to_be_used_when_emitting_files_Colon_CRLF_dos_or_LF_unix_6060": "Especifique la secuencia de final de línea que debe usarse para emitir archivos: 'CRLF' (Dos) o 'LF' (Unix).", "Specify_the_location_where_debugger_should_locate_TypeScript_files_instead_of_source_locations_6004": "Especifique la ubicación donde el depurador debe colocar los archivos de TypeScript en lugar de sus ubicaciones de origen.", "Specify_the_location_where_debugger_should_locate_map_files_instead_of_generated_locations_6655": "Especifique la ubicación donde el depurador debe colocar los archivos de asignaciones en lugar de las ubicaciones generadas.", "Specify_the_maximum_folder_depth_used_for_checking_JavaScript_files_from_node_modules_Only_applicabl_6656": "Especifique la profundidad máxima de carpeta usada para comprobar archivos JavaScript de \"node_modules\". Solo es compatible con \"allowJs\".", "Specify_the_module_specifier_to_be_used_to_import_the_jsx_and_jsxs_factory_functions_from_eg_react_6238": "Especifique el especificador de módulo que se va a usar para importar las funciones de fábrica \"jsx\" y \"jsxs\"; por ejemplo, react", "Specify_the_object_invoked_for_createElement_This_only_applies_when_targeting_react_JSX_emit_6686": "Especifique el objeto invocado para 'createElement'. Esto solo se aplica cuando el destino es la emisión JSX \"react\".", "Specify_the_output_directory_for_generated_declaration_files_6613": "Especifique el directorio de salida para los archivos de declaración generados.", "Specify_the_path_to_tsbuildinfo_incremental_compilation_file_6707": "Especifique la ruta de acceso para el archivo de compilación incremental .tsbuildinfo.", "Specify_the_root_directory_of_input_files_Use_to_control_the_output_directory_structure_with_outDir_6058": "Especifique el directorio raíz de los archivos de entrada. Úselo para controlar la estructura del directorio de salida con --outDir.", "Specify_the_root_folder_within_your_source_files_6690": "Especifique la carpeta raíz en los archivos de código fuente.", "Specify_the_root_path_for_debuggers_to_find_the_reference_source_code_6695": "Especifique la ruta raíz para que los depuradores encuentren el código de origen de referencia.", "Specify_type_package_names_to_be_included_without_being_referenced_in_a_source_file_6711": "Especifique los nombres de los paquetes de tipo que se incluyen sin ser referenciados en un archivo fuente.", "Specify_what_JSX_code_is_generated_6646": "Especifique qué código de JSX se generará.", "Specify_what_approach_the_watcher_should_use_if_the_system_runs_out_of_native_file_watchers_6634": "Especifique el enfoque que debe usar el monitor si el sistema agota los monitores de archivos nativos.", "Specify_what_module_code_is_generated_6657": "Especifique qué código de módulo se generará.", "Split_all_invalid_type_only_imports_1367": "Dividir todas las importaciones solo de tipo no válidas", "Split_into_two_separate_import_declarations_1366": "Dividir en dos declaraciones de importación independientes", "Spread_operator_in_new_expressions_is_only_available_when_targeting_ECMAScript_5_and_higher_2472": "El operador spread de las expresiones \"new\" solo está disponible si el destino es ECMAScript 5 y versiones posteriores.", "Spread_types_may_only_be_created_from_object_types_2698": "Los tipos spread solo se pueden crear a partir de tipos de objeto.", "Starting_compilation_in_watch_mode_6031": "Iniciando la compilación en modo de inspección...", "Statement_expected_1129": "Se esperaba una instrucción.", "Statements_are_not_allowed_in_ambient_contexts_1036": "No se permiten instrucciones en los contextos de ambiente.", "Static_members_cannot_reference_class_type_parameters_2302": "Los miembros estáticos no pueden hacer referencia a parámetros de tipo de clase.", "Static_property_0_conflicts_with_built_in_property_Function_0_of_constructor_function_1_2699": "La propiedad estática \"{0}\" está en conflicto con la propiedad integrada \"Function.{0}\" de la función de constructor \"{1}\".", "String_literal_expected_1141": "Se esperaba un literal de cadena.", "String_literal_import_and_export_names_are_not_supported_when_the_module_flag_is_set_to_es2015_or_es_18057": "No se admiten los nombres de importación y exportación de literales de cadena cuando la marca “--module” está establecida en “es2015” o “es2020”.", "String_literal_with_double_quotes_expected_1327": "Se esperaba un literal de cadena entre comillas dobles.", "Stylize_errors_and_messages_using_color_and_context_experimental_6073": "Use color y contexto para estilizar los errores y los mensajes (experimental).", "Subpattern_flags_must_be_present_when_there_is_a_minus_sign_1504": "Las marcas de subpatrones deben estar presentes cuando hay un signo menos.", "Subsequent_property_declarations_must_have_the_same_type_Property_0_must_be_of_type_1_but_here_has_t_2717": "Las declaraciones de propiedad subsiguientes deben tener el mismo tipo. La propiedad \"{0}\" debe ser de tipo \"{1}\", pero aquí tiene el tipo \"{2}\".", "Subsequent_variable_declarations_must_have_the_same_type_Variable_0_must_be_of_type_1_but_here_has_t_2403": "Las declaraciones de variable subsiguientes deben tener el mismo tipo. La variable '{0}' debe ser de tipo '{1}', pero aquí tiene el tipo '{2}'.", "Substitution_0_for_pattern_1_has_incorrect_type_expected_string_got_2_5064": "La sustitución '{0}' para el patrón '{1}' tiene un tipo incorrecto. Se esperaba 'string', pero se obtuvo '{2}'.", "Substitution_0_in_pattern_1_can_have_at_most_one_Asterisk_character_5062": "La sustitución \"{0}\" del patrón \"{1}\" puede tener un carácter \"*\" como máximo.", "Substitutions_for_pattern_0_should_be_an_array_5063": "Las sustituciones para el patrón '{0}' deben ser una matriz.", "Substitutions_for_pattern_0_shouldn_t_be_an_empty_array_5066": "Las sustituciones para el patrón '{0}' no deben ser una matriz vacía.", "Successfully_created_a_tsconfig_json_file_6071": "Archivo tsconfig.json creado correctamente.", "Super_calls_are_not_permitted_outside_constructors_or_in_nested_functions_inside_constructors_2337": "No se permiten llamadas a \"super\" fuera de los constructores o en funciones anidadas dentro de estos.", "Suppress_excess_property_checks_for_object_literals_6072": "Suprima las comprobaciones de propiedades en exceso de los literales de objeto.", "Suppress_noImplicitAny_errors_for_indexing_objects_lacking_index_signatures_6055": "Eliminar errores de noImplicitAny para los objetos de indexación a los que les falten firmas de índice.", "Suppress_noImplicitAny_errors_when_indexing_objects_that_lack_index_signatures_6703": "Suprima los errores \"noImplicitAny\" al indexar objetos que carecen de firmas de índice.", "Switch_each_misused_0_to_1_95138": "Cambie cada elemento \"{0}\" usado incorrectamente a \"{1}\"", "Synchronously_call_callbacks_and_update_the_state_of_directory_watchers_on_platforms_that_don_t_supp_6704": "Llame a las devoluciones de llamada de forma sincrónica y actualice el estado de los monitores de directorio en las plataformas que no admitan la supervisión recursiva de forma nativa.", "Syntax_Colon_0_6023": "Sintaxis: {0}", "Tag_0_expects_at_least_1_arguments_but_the_JSX_factory_2_provides_at_most_3_6229": "La etiqueta \"{0}\" espera al menos \"{1}\" argumentos, pero el generador de JSX \"{2}\" proporciona como máximo \"{3}\".", "Tagged_template_expressions_are_not_permitted_in_an_optional_chain_1358": "No se permiten expresiones de plantilla con etiquetas en una cadena opcional.", "Target_allows_only_0_element_s_but_source_may_have_more_2621": "El destino solo permite {0} elemento(s), pero el origen puede tener más.", "Target_requires_0_element_s_but_source_may_have_fewer_2620": "El destino requiere {0} elemento(s), pero el origen puede tener menos.", "Target_signature_provides_too_few_arguments_Expected_0_or_more_but_got_1_2849": "La firma de destino proporciona muy pocos argumentos. Se esperaba {0} o más, pero se obtuvo {1}.", "The_0_modifier_can_only_be_used_in_TypeScript_files_8009": "El modificador \"{0}\" solo se puede usar en los archivos TypeScript.", "The_0_operator_cannot_be_applied_to_type_symbol_2469": "El operador '{0}' no se puede aplicar al tipo \"symbol\".", "The_0_operator_is_not_allowed_for_boolean_types_Consider_using_1_instead_2447": "No se permite usar el operador '{0}' para los tipos booleanos. Como alternativa, puede usar '{1}'.", "The_0_property_of_an_async_iterator_must_be_a_method_2768": "La propiedad \"{0}\" de un iterador de asincronía debe ser un método.", "The_0_property_of_an_iterator_must_be_a_method_2767": "La propiedad \"{0}\" de un iterador debe ser un método.", "The_Object_type_is_assignable_to_very_few_other_types_Did_you_mean_to_use_the_any_type_instead_2696": "El tipo 'Object' se puede asignar a muy pocos tipos. ¿Se refería a usar el tipo 'any' en realidad?", "The_Unicode_u_flag_and_the_Unicode_Sets_v_flag_cannot_be_set_simultaneously_1502": "Las marcas Unicode (u) y Unicode Sets (v) no se pueden establecer simultáneamente.", "The_arguments_object_cannot_be_referenced_in_an_arrow_function_in_ES5_Consider_using_a_standard_func_2496": "No se puede hacer referencia al objeto “arguments” en una función de flecha en ES5 ni ES5. Considere la posibilidad de usar una expresión de función estándar.", "The_arguments_object_cannot_be_referenced_in_an_async_function_or_method_in_ES5_Consider_using_a_sta_2522": "No se puede hacer referencia al objeto “arguments” en una función o método asincrónico en ES5. Considere la posibilidad de usar un método o función estándar.", "The_body_of_an_if_statement_cannot_be_the_empty_statement_1313": "El cuerpo de una instrucción \"if\" no puede ser la instrucción vacía.", "The_call_would_have_succeeded_against_this_implementation_but_implementation_signatures_of_overloads_2793": "La llamada se llevaría a cabo sin problemas en esta implementación, pero las signaturas de implementación de las sobrecargas no están visibles externamente.", "The_character_set_of_the_input_files_6163": "Conjunto de caracteres de los archivos de entrada.", "The_containing_arrow_function_captures_the_global_value_of_this_7041": "La función de flecha contenedora captura el valor global de \"this\".", "The_containing_function_or_module_body_is_too_large_for_control_flow_analysis_2563": "El cuerpo de la función o del módulo contenedor es demasiado grande para realizar un análisis de flujo de control.", "The_current_file_is_a_CommonJS_module_and_cannot_use_await_at_the_top_level_1309": "El archivo actual es un módulo CommonJS y no puede usar \"await\" en el nivel superior.", "The_current_file_is_a_CommonJS_module_whose_imports_will_produce_require_calls_however_the_reference_1479": "El archivo actual es un módulo CommonJS cuyas importaciones generarán llamadas \"require\"; sin embargo, el archivo al que se hace referencia es un módulo ECMAScript y no se puede importar con \"require\". Considere la posibilidad de escribir una llamada dinámica \"import(\"{0}\")\" en su lugar.", "The_current_host_does_not_support_the_0_option_5001": "El host actual no admite la opción '{0}'.", "The_declaration_of_0_that_you_probably_intended_to_use_is_defined_here_18018": "La declaración de \"{0}\" que probablemente pretendía usar se define aquí", "The_declaration_was_marked_as_deprecated_here_2798": "La declaración se ha marcado aquí como en desuso.", "The_expected_type_comes_from_property_0_which_is_declared_here_on_type_1_6500": "El tipo esperado procede de la propiedad \"{0}\", que se declara aquí en el tipo \"{1}\"", "The_expected_type_comes_from_the_return_type_of_this_signature_6502": "El tipo esperado procede del tipo de valor devuelto de esta signatura.", "The_expected_type_comes_from_this_index_signature_6501": "El tipo esperado procede de esta signatura de índice.", "The_expression_of_an_export_assignment_must_be_an_identifier_or_qualified_name_in_an_ambient_context_2714": "La expresión de una asignación de exportación debe ser un identificador o un nombre completo en un contexto de ambiente.", "The_file_is_in_the_program_because_Colon_1430": "El archivo está en el programa porque:", "The_files_list_in_config_file_0_is_empty_18002": "La lista de archivos del archivo de configuración '{0}' está vacía.", "The_first_export_default_is_here_2752": "El primer elemento export default está aquí.", "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060": "El primer parámetro del método \"then\" de una promesa debe ser una devolución de llamada.", "The_global_type_JSX_0_may_not_have_more_than_one_property_2608": "El tipo \"JSX.{0}\" global no puede tener más de una propiedad.", "The_implementation_signature_is_declared_here_2750": "La signatura de implementación se declara aquí.", "The_import_meta_meta_property_is_not_allowed_in_files_which_will_build_into_CommonJS_output_1470": "No se permite la metapropiedad \"import.meta\" en archivos que se compilarán en la salida de CommonJS.", "The_import_meta_meta_property_is_only_allowed_when_the_module_option_is_es2020_es2022_esnext_system__1343": "La metapropiedad \"import.meta\" solo se permite cuando la opción \"--módulo\" es \"es2020\", \"es2022\", \"esnext\", \"system\", \"node16\", \"node18\" o \"nodenext\".", "The_inferred_type_of_0_cannot_be_named_without_a_reference_to_1_This_is_likely_not_portable_A_type_a_2742": "No se puede asignar un nombre al tipo inferido de \"{0}\" sin una referencia a \"{1}\". Es probable que no sea portable. Se requiere una anotación de tipo.", "The_inferred_type_of_0_references_a_type_with_a_cyclic_structure_which_cannot_be_trivially_serialize_5088": "El tipo deducido de \"{0}\" hace referencia a un tipo con una estructura cíclica que no se puede serializar trivialmente. Es necesaria una anotación de tipo.", "The_inferred_type_of_0_references_an_inaccessible_1_type_A_type_annotation_is_necessary_2527": "El tipo inferido de \"{0}\" hace referencia a un tipo \"{1}\" no accesible. Se requiere una anotación de tipo.", "The_inferred_type_of_this_node_exceeds_the_maximum_length_the_compiler_will_serialize_An_explicit_ty_7056": "El tipo inferido de este nodo supera la longitud máxima que el compilador podrá serializar. Se necesita una anotación de tipo explícito.", "The_initializer_of_a_using_declaration_must_be_either_an_object_with_a_Symbol_dispose_method_or_be_n_2850": "El inicializador de una declaración “using” debe ser un objeto con un método “[Symbol.dispose]()”, o ser “null” o “undefined”.", "The_initializer_of_an_await_using_declaration_must_be_either_an_object_with_a_Symbol_asyncDispose_or_2851": "El inicializador de una declaración “await using” debe ser un objeto con un método “[Symbol.asyncDispose]()” o “[Symbol.dispose]5D;()”, o ser “null” o “undefined”.", "The_intersection_0_was_reduced_to_never_because_property_1_exists_in_multiple_constituents_and_is_pr_18032": "La intersección \"{0}\" se redujo a \"never\" porque la propiedad \"{1}\" existe en varios constituyentes y es privada en algunos de ellos.", "The_intersection_0_was_reduced_to_never_because_property_1_has_conflicting_types_in_some_constituent_18031": "La intersección \"{0}\" se redujo a \"never\" porque la propiedad \"{1}\" tiene tipos en conflicto en algunos constituyentes.", "The_intrinsic_keyword_can_only_be_used_to_declare_compiler_provided_intrinsic_types_2795": "La palabra clave \"intrinsic\" solo se puede usar para declarar tipos intrínsecos proporcionados por el compilador.", "The_jsxFragmentFactory_compiler_option_must_be_provided_to_use_JSX_fragments_with_the_jsxFactory_com_17016": "Se debe proporcionar la opción del compilador \"jsxFragmentFactory\" para usar fragmentos de JSX con la opción del compilador \"jsxFactory\".", "The_last_overload_gave_the_following_error_2770": "La última sobrecarga dio el error siguiente.", "The_last_overload_is_declared_here_2771": "La última sobrecarga se declara aquí.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_destructuring_pattern_2491": "La parte izquierda de una instrucción \"for...in\" no puede ser un patrón de desestructuración.", "The_left_hand_side_of_a_for_in_statement_cannot_be_a_using_declaration_1493": "El lado izquierdo de un “for...in” no puede ser una declaración “using”.", "The_left_hand_side_of_a_for_in_statement_cannot_be_an_await_using_declaration_1494": "El lado izquierdo de una instrucción “for...in” no puede ser una declaración “await using”.", "The_left_hand_side_of_a_for_in_statement_cannot_use_a_type_annotation_2404": "La parte izquierda de una instrucción \"for...in\" no puede usar una anotación de tipo.", "The_left_hand_side_of_a_for_in_statement_may_not_be_an_optional_property_access_2780": "La parte izquierda de una instrucción \"for...in\" no puede ser un acceso de propiedad opcional.", "The_left_hand_side_of_a_for_in_statement_must_be_a_variable_or_a_property_access_2406": "La parte izquierda de una instrucción 'for...in' debe ser una variable o el acceso a una propiedad.", "The_left_hand_side_of_a_for_in_statement_must_be_of_type_string_or_any_2405": "La parte izquierda de una instrucción \"for...in\" debe ser de tipo \"string\" o \"any\".", "The_left_hand_side_of_a_for_of_statement_cannot_use_a_type_annotation_2483": "La parte izquierda de una instrucción \"for...of\" no puede usar una anotación de tipo.", "The_left_hand_side_of_a_for_of_statement_may_not_be_an_optional_property_access_2781": "La parte izquierda de una instrucción \"for...of\" no puede ser un acceso de propiedad opcional.", "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106": "El lado izquierdo de una instrucción de \"para... de\" puede no ser \"async\".", "The_left_hand_side_of_a_for_of_statement_must_be_a_variable_or_a_property_access_2487": "La parte izquierda de una instrucción 'for...of' debe ser una variable o el acceso a una propiedad.", "The_left_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2362": "La parte izquierda de una operación aritmética debe ser de tipo \"any\", \"number\", \"bigint\" o un tipo de enumeración.", "The_left_hand_side_of_an_assignment_expression_may_not_be_an_optional_property_access_2779": "La parte izquierda de una expresión de asignación no puede ser un acceso de propiedad opcional.", "The_left_hand_side_of_an_assignment_expression_must_be_a_variable_or_a_property_access_2364": "La parte izquierda de una expresión de asignación debe ser una variable o el acceso a una propiedad.", "The_left_hand_side_of_an_instanceof_expression_must_be_assignable_to_the_first_argument_of_the_right_2860": "El lado izquierdo de una expresión “instanceof” debe poder asignarse al primer argumento del método “[Symbol.hasInstance]” del lado derecho.", "The_left_hand_side_of_an_instanceof_expression_must_be_of_type_any_an_object_type_or_a_type_paramete_2358": "La parte izquierda de una expresión \"instanceof\" debe ser de tipo \"any\", un tipo de objeto o un parámetro de tipo.", "The_locale_used_when_displaying_messages_to_the_user_e_g_en_us_6156": "Configuración regional utilizada para mostrar los mensajes al usuario (por ejemplo, \"es-es\")", "The_maximum_dependency_depth_to_search_under_node_modules_and_load_JavaScript_files_6136": "La profundidad máxima de dependencia para buscar en node_modules y cargar los archivos de JavaScript.", "The_operand_of_a_delete_operator_cannot_be_a_private_identifier_18011": "El operando de un operador \"delete\" no puede ser un identificador privado.", "The_operand_of_a_delete_operator_cannot_be_a_read_only_property_2704": "El operando de un operador \"delete\" no puede ser una propiedad de solo lectura.", "The_operand_of_a_delete_operator_must_be_a_property_reference_2703": "El operando de un operador \"delete\" debe ser una referencia de propiedad.", "The_operand_of_a_delete_operator_must_be_optional_2790": "El operando de un operador \"delete\" debe ser opcional.", "The_operand_of_an_increment_or_decrement_operator_may_not_be_an_optional_property_access_2777": "El operando de un operador de incremento o decremento no puede ser un acceso de propiedad opcional.", "The_operand_of_an_increment_or_decrement_operator_must_be_a_variable_or_a_property_access_2357": "El operando de un operador de incremento o decremento debe ser una variable o el acceso a una propiedad.", "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007": "El analizador esperaba encontrar un elemento \"{1}\" que coincidiera con el token \"{0}\" aquí.", "The_project_root_is_ambiguous_but_is_required_to_resolve_export_map_entry_0_in_file_1_Supply_the_roo_2209": "La raíz del proyecto es ambigua, pero es necesaria para resolver la entrada de asignación de exportación \"{0}\" en el archivo \"{1}\". Proporcione la opción del compilador \"rootDir\" para eliminar la ambigüedad.", "The_project_root_is_ambiguous_but_is_required_to_resolve_import_map_entry_0_in_file_1_Supply_the_roo_2210": "La raíz del proyecto es ambigua, pero es necesaria para resolver la entrada de asignación de importación \"{0}\" en el archivo \"{1}\". Proporcione la opción del compilador \"rootDir\" para eliminar la ambigüedad.", "The_property_0_cannot_be_accessed_on_type_1_within_this_class_because_it_is_shadowed_by_another_priv_18014": "No se puede acceder a la propiedad \"{0}\" en el tipo \"{1}\" de esta clase porque se ha reemplazado por otro identificador privado con la misma ortografía.", "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237": "El tipo de valor devuelto de una función Decorator de parámetro debe ser \"void\" o \"any\".", "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236": "El tipo de valor devuelto de una función Decorator de propiedad debe ser \"void\" o \"any\".", "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058": "El tipo de valor devuelto de una función asincrónica debe ser una promesa válida o no debe contener un miembro \"then\" invocable.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_1065": "El tipo de valor devuelto de una función o un método asincrónicos debe ser el tipo Promise<T> global.", "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064": "El tipo de valor devuelto de una función o un método asincrónicos debe ser el tipo Promise<T> global. ¿Pretendía escribir \"Promise<{0}>\"?", "The_right_hand_side_of_a_for_in_statement_must_be_of_type_any_an_object_type_or_a_type_parameter_but_2407": "La parte derecha de una instrucción \"for...in\" debe ser de tipo \"any\", un tipo de objeto o un parámetro de tipo, pero aquí tiene el tipo \"{0}\".", "The_right_hand_side_of_an_arithmetic_operation_must_be_of_type_any_number_bigint_or_an_enum_type_2363": "La parte derecha de una operación aritmética debe ser de tipo \"any\", \"number\", \"bigint\" o un tipo de enumeración.", "The_right_hand_side_of_an_instanceof_expression_must_be_either_of_type_any_a_class_function_or_other_2359": "El lado derecho de una expresión “instanceof” debe ser de tipo “any”, una clase, función u otro tipo asignable al tipo de interfaz “Function”, o un tipo de objeto con un método “Symbol.hasInstance”.", "The_right_hand_side_of_an_instanceof_expression_must_not_be_an_instantiation_expression_2848": "El lado derecho de una expresión “instanceof” no debe ser una expresión de creación de instancias.", "The_root_value_of_a_0_file_must_be_an_object_5092": "El valor raíz de un archivo \"{0}\" debe ser un objeto.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_0_1278": "El runtime invocará el decorador con argumentos {1}, pero el decorador espera {0}.", "The_runtime_will_invoke_the_decorator_with_1_arguments_but_the_decorator_expects_at_least_0_1279": "El runtime invocará el decorador con argumentos {1}, pero el decorador espera al menos {0}.", "The_shadowing_declaration_of_0_is_defined_here_18017": "La declaración que ensombrece a \"{0}\" se define aquí.", "The_signature_0_of_1_is_deprecated_6387": "La signatura \"{0}\" de \"{1}\" está en desuso.", "The_specified_path_does_not_exist_Colon_0_5058": "La ruta de acceso especificada no existe: \"{0}\".", "The_tag_was_first_specified_here_8034": "La etiqueta se especificó aquí primero.", "The_target_of_an_object_rest_assignment_may_not_be_an_optional_property_access_2778": "El destino de una asignación rest de objeto no puede ser un acceso de propiedad opcional.", "The_target_of_an_object_rest_assignment_must_be_a_variable_or_a_property_access_2701": "El destino de una asignación de reposo de objetos debe ser una variable o un acceso a propiedad.", "The_this_context_of_type_0_is_not_assignable_to_method_s_this_of_type_1_2684": "El contexto 'this' de tipo '{0}' no se puede asignar al contexto 'this' de tipo '{1}' del método.", "The_this_types_of_each_signature_are_incompatible_2685": "Los tipos 'this' de cada signatura son incompatibles.", "The_type_0_is_readonly_and_cannot_be_assigned_to_the_mutable_type_1_4104": "El tipo \"{0}\" es \"readonly\" y no se puede asignar al tipo mutable \"{1}\".", "The_type_modifier_cannot_be_used_on_a_named_export_when_export_type_is_used_on_its_export_statement_2207": "El modificador 'tipo' no se puede usar en una exportación con nombre cuando se usa 'exportar tipo' en su instrucción exportar.", "The_type_modifier_cannot_be_used_on_a_named_import_when_import_type_is_used_on_its_import_statement_2206": "El modificador 'tipo' no se puede usar en una importación con nombre cuando se usa 'exportar tipo' en su instrucción importar.", "The_type_of_a_function_declaration_must_match_the_function_s_signature_8030": "El tipo de una declaración de función debe coincidir con la signatura de la función.", "The_type_of_this_node_cannot_be_serialized_because_its_property_0_cannot_be_serialized_4118": "No se ha podido serializar el tipo de este nodo porque su propiedad '{0}' no se puede serializar.", "The_type_returned_by_the_0_method_of_an_async_iterator_must_be_a_promise_for_a_type_with_a_value_pro_2547": "El tipo devuelto por el método \"{0}()\" de un iterador de asincronía debe ser una promesa para un tipo con una propiedad \"value\".", "The_type_returned_by_the_0_method_of_an_iterator_must_have_a_value_property_2490": "El tipo devuelto por el método \"{0}()\" de un iterador debe tener una propiedad \"value\".", "The_types_of_0_are_incompatible_between_these_types_2200": "Los tipos de \"{0}\" son incompatibles entre estos tipos.", "The_types_returned_by_0_are_incompatible_between_these_types_2201": "Los tipos que \"{0}\" devuelve son incompatibles entre estos tipos.", "The_value_0_cannot_be_used_here_18050": "El valor \"{0}\" no se puede usar aquí.", "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189": "La declaración de variable de una instrucción \"for...in\" no puede tener un inicializador.", "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190": "La declaración de variable de una instrucción \"for...of\" no puede tener un inicializador.", "The_with_statement_is_not_supported_All_symbols_in_a_with_block_will_have_type_any_2410": "No se admite la instrucción 'with'. Todos los símbolos de un bloque 'with' tendrán el tipo 'any'.", "There_are_types_at_0_but_this_result_could_not_be_resolved_under_your_current_moduleResolution_setti_6280": "Hay tipos en “{0}”, pero este resultado no se pudo resolver en la configuración actual de “moduleResolution”. Considere la posibilidad de actualizar a “node16”, “nodenext” o “bundler”.", "There_are_types_at_0_but_this_result_could_not_be_resolved_when_respecting_package_json_exports_The__6278": "Hay tipos en “{0}”, pero este resultado no se pudo resolver al respetar \"exports\" de package.json. Es posible que la biblioteca “{1}” necesite actualizar sus package.json o tipos.", "There_is_no_capturing_group_named_0_in_this_regular_expression_1532": "No hay ningún grupo de captura denominado “{0}” en esta expresión regular.", "There_is_nothing_available_for_repetition_1507": "No hay nada disponible para la repetición.", "This_JSX_tag_requires_0_to_be_in_scope_but_it_could_not_be_found_2874": "Esta etiqueta JSX requiere que \"{0}\" esté en el ámbito, pero no se encontró.", "This_JSX_tag_requires_the_module_path_0_to_exist_but_none_could_be_found_Make_sure_you_have_types_fo_2875": "Esta etiqueta JSX requiere que exista la ruta de acceso del módulo \"{0}\", pero no se encontró ninguna. Asegúrese de que tiene instalados los tipos para el paquete adecuado.", "This_JSX_tag_s_0_prop_expects_a_single_child_of_type_1_but_multiple_children_were_provided_2746": "La propiedad \"{0}\" de esta etiqueta de JSX espera un solo elemento secundario de tipo \"{1}\", pero se han proporcionado varios elementos secundarios.", "This_JSX_tag_s_0_prop_expects_type_1_which_requires_multiple_children_but_only_a_single_child_was_pr_2745": "La propiedad \"{0}\" de esta etiqueta de JSX espera el tipo \"{1}\", que requiere varios elementos secundarios, pero solo se ha proporcionado un elemento secundario.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_no_capturing_groups_in_this_regul_1534": "Esta referencia inversa hace referencia a un grupo que no existe. No hay grupos de captura en esta expresión regular.", "This_backreference_refers_to_a_group_that_does_not_exist_There_are_only_0_capturing_groups_in_this_r_1533": "Esta referencia inversa hace referencia a un grupo que no existe. Solo hay {0} grupos de captura en esta expresión regular.", "This_binary_expression_is_never_nullish_Are_you_missing_parentheses_2870": "Esta expresión binaria nunca acepta valores NULL. ¿Faltan parén<PERSON>?", "This_character_cannot_be_escaped_in_a_regular_expression_1535": "Este carácter no se puede escapar en una expresión regular.", "This_comparison_appears_to_be_unintentional_because_the_types_0_and_1_have_no_overlap_2367": "Esta comparación parece no intencionada porque los tipos \"{0}\" y \"{1}\" no tienen superposición.", "This_condition_will_always_return_0_2845": "Esta condición siempre devolverá \"{0}\".", "This_condition_will_always_return_0_since_JavaScript_compares_objects_by_reference_not_value_2839": "Esta condición siempre devolverá \"{0}\", ya que JavaScript compara objetos por referencia, no por valor.", "This_condition_will_always_return_true_since_this_0_is_always_defined_2801": "Esta condición devolverá siempre true porque siempre se define '{0}'.", "This_condition_will_always_return_true_since_this_function_is_always_defined_Did_you_mean_to_call_it_2774": "Esta condición siempre devolverá true, porque esta función se define siempre. ¿Pretendía llamarla en su lugar?", "This_constructor_function_may_be_converted_to_a_class_declaration_80002": "Esta función de constructor puede convertirse en una declaración de clase.", "This_expression_is_always_nullish_2871": "Esta expresión siempre acepta valores NULL.", "This_expression_is_not_callable_2349": "No se puede llamar a esta expresión.", "This_expression_is_not_callable_because_it_is_a_get_accessor_Did_you_mean_to_use_it_without_6234": "No se puede llamar a esta expresión porque es un descriptor de acceso \"get\". ¿Pretendía usarlo sin \"()\"?", "This_expression_is_not_constructable_2351": "No se puede construir esta expresión.", "This_file_already_has_a_default_export_95130": "Este archivo ya tiene una exportación predeterminada", "This_import_path_is_unsafe_to_rewrite_because_it_resolves_to_another_project_and_the_relative_path_b_2878": "Esta ruta de acceso de importación no es segura de reescribir porque se resuelve en otro proyecto y la ruta de acceso relativa entre los archivos de salida de los proyectos no es la misma que la ruta de acceso relativa entre sus archivos de entrada.", "This_import_uses_a_0_extension_to_resolve_to_an_input_TypeScript_file_but_will_not_be_rewritten_duri_2877": "Esta importación usa una extensión \"{0}\" para resolver en un archivo TypeScript de entrada, pero no se reescribe durante la emisión porque no es una ruta de acceso relativa.", "This_is_the_declaration_being_augmented_Consider_moving_the_augmenting_declaration_into_the_same_fil_6233": "Esta es la declaración que se está aumentando. Considere la posibilidad de mover la declaración en aumento al mismo archivo.", "This_kind_of_expression_is_always_falsy_2873": "Este tipo de expresión siempre es falso.", "This_kind_of_expression_is_always_truthy_2872": "Este tipo de expresión siempre es cierto.", "This_may_be_converted_to_an_async_function_80006": "Puede convertirse en una función asincrónica.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4122": "Este miembro no puede tener un comentario JSDoc con una etiqueta '@override' porque no se declara en la clase base '{0}'.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_it_is_not_declared_in_the_base__4123": "Este miembro no puede tener un comentario JSDoc con una etiqueta 'override' porque no se declara en la clase base '{0}'. ¿Quería decir '{1}'?", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_containing_class_0_does_not_4121": "Este miembro no puede tener un comentario JSDoc con una etiqueta '@override' porque su clase contenedora '{0}' no extiende otra clase.", "This_member_cannot_have_a_JSDoc_comment_with_an_override_tag_because_its_name_is_dynamic_4128": "Este miembro no puede tener un comentario JSDoc con una etiqueta '@override' porque su nombre es dinámico.", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_4113": "Este miembro no puede tener un modificador \"override\" porque no está declarado en la clase base \"{0}\".", "This_member_cannot_have_an_override_modifier_because_it_is_not_declared_in_the_base_class_0_Did_you__4117": "Este miembro no puede tener un modificador \"override\" porque no está declarado en la clase base \"{0}\". ¿Quizá quiso decir \"{1}\"?", "This_member_cannot_have_an_override_modifier_because_its_containing_class_0_does_not_extend_another__4112": "Este miembro no puede tener un modificador \"override\" porque su clase contenedora \"{0}\" no extiende otra clase.", "This_member_cannot_have_an_override_modifier_because_its_name_is_dynamic_4127": "Este miembro no puede tener un modificador 'override' porque su nombre es dinámico.", "This_member_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_in_the_base_4119": "Este miembro debe tener un comentario JSDoc con una etiqueta '@override' porque invalida un miembro de la clase base '{0}'.", "This_member_must_have_an_override_modifier_because_it_overrides_a_member_in_the_base_class_0_4114": "Este miembro debe tener un modificador \"override\" porque reemplaza a un miembro en la clase base \"{0}\".", "This_member_must_have_an_override_modifier_because_it_overrides_an_abstract_method_that_is_declared__4116": "Este miembro debe tener un modificador \"override\" porque reemplaza a un método abstracto que se declara en la clase base \"{0}\".", "This_module_can_only_be_referenced_with_ECMAScript_imports_Slashexports_by_turning_on_the_0_flag_and_2497": "Solo se puede hacer referencia a este módulo con las importaciones o exportaciones de ECMAScript mediante la activación de la marca \"{0}\" y la referencia a su exportación predeterminada.", "This_module_is_declared_with_export_and_can_only_be_used_with_a_default_import_when_using_the_0_flag_2594": "Este módulo se declara con \"export =\" y solo se puede usar con una importación predeterminada cuando se usa la marca \"{0}\".", "This_operation_can_be_simplified_This_shift_is_identical_to_0_1_2_6807": "Esta operación se puede simplificar. Este turno es idéntico a \"{0} {1} {2}\".", "This_overload_implicitly_returns_the_type_0_because_it_lacks_a_return_type_annotation_7012": "Esta sobrecarga devuelve implícitamente el tipo “{0}” porque carece de una anotación de tipo de valor devuelto.", "This_overload_signature_is_not_compatible_with_its_implementation_signature_2394": "Esta signatura de sobrecarga no es compatible con su signatura de implementación.", "This_parameter_is_not_allowed_with_use_strict_directive_1346": "Este parámetro no se permite con la directiva \"use strict\".", "This_parameter_property_must_have_a_JSDoc_comment_with_an_override_tag_because_it_overrides_a_member_4120": "Esta propiedad de parámetro debe tener un comentario JSDoc con una etiqueta '@override' porque invalida un miembro de la clase base '{0}'.", "This_parameter_property_must_have_an_override_modifier_because_it_overrides_a_member_in_base_class_0_4115": "Esta propiedad de parámetro debe tener un modificador \"override\" porque reemplaza a un miembro en la clase base \"{0}\".", "This_regular_expression_flag_cannot_be_toggled_within_a_subpattern_1509": "Esta marca de expresión regular no se puede alternar dentro de un subpatrón.", "This_regular_expression_flag_is_only_available_when_targeting_0_or_later_1501": "Esta marca de expresión regular solo está disponible cuando el destino es “{0}” o posterior.", "This_relative_import_path_is_unsafe_to_rewrite_because_it_looks_like_a_file_name_but_actually_resolv_2876": "Esta ruta de acceso de importación relativa no es segura de reescribir porque parece un nombre de archivo, pero realmente se resuelve en \"{0}\".", "This_spread_always_overwrites_this_property_2785": "Este elemento de propagación siempre sobrescribe esta propiedad.", "This_syntax_is_not_allowed_when_erasableSyntaxOnly_is_enabled_1294": "Esta sintaxis no se permite cuando \"erasableSyntaxOnly\" está habilitado.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Add_a_trailing_comma_or_explicit_cons_7060": "Esta sintaxis está reservada en archivos con la extensión .mts o .CTS. Agregue una coma o una restricción explícita al final.", "This_syntax_is_reserved_in_files_with_the_mts_or_cts_extension_Use_an_as_expression_instead_7059": "Esta sintaxis se reserva a archivos con la extensión .mts o .cts. En su lugar, use una expresión \"as\".", "This_syntax_requires_an_imported_helper_but_module_0_cannot_be_found_2354": "Esta sintaxis requiere un asistente importado, pero no se puede encontrar el módulo \"{0}\".", "This_syntax_requires_an_imported_helper_named_1_which_does_not_exist_in_0_Consider_upgrading_your_ve_2343": "Esta sintaxis requiere una aplicación auxiliar importada denominada \"{1}\", que no existe en \"{0}\". Considere la posibilidad de actualizar la versión de \"{0}\".", "This_syntax_requires_an_imported_helper_named_1_with_2_parameters_which_is_not_compatible_with_the_o_2807": "Esta sintaxis requiere un asistente importado denominado \"{1}\" con parámetros de {2}, que no es compatible con el de \"{0}\". Pruebe a actualizar la versión de \"{0}\".", "This_type_parameter_might_need_an_extends_0_constraint_2208": "Este parámetro de tipo podría necesitar una restricción \"extends {0}\".", "This_use_of_import_is_invalid_import_calls_can_be_written_but_they_must_have_parentheses_and_cannot__1326": "Este uso de \"import\" no es válido. Se pueden escribir llamadas \"import()\", pero deben tener paréntesis y no pueden tener argumentos de tipo.", "To_convert_this_file_to_an_ECMAScript_module_add_the_field_type_Colon_module_to_0_1482": "Para convertir este archivo en un módulo ECMAScript, agregue el campo `\"type\": \"module\"` a \"{0}\"'.", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_add_the_field_type_Co_1481": "Para convertir este archivo en un módulo ECMAScript, cambie su extensión de archivo a \"{0}\" o agregue el campo `\"type\": \"module\"` a \"{1}\".", "To_convert_this_file_to_an_ECMAScript_module_change_its_file_extension_to_0_or_create_a_local_packag_1480": "Para convertir este archivo en un módulo ECMAScript, cambie su extensión de archivo a \"{0}\" o cree un archivo package.json local con '{ \"type\": \"module\" }'.", "To_convert_this_file_to_an_ECMAScript_module_create_a_local_package_json_file_with_type_Colon_module_1483": "Para convertir este archivo en un módulo ECMAScript, cree un archivo package.json local con `{ \"type\": \"module\" }`.", "Top_level_await_expressions_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_n_1378": "Las expresiones \"await\" de nivel superior solo se permiten cuando la opción \"module\" está establecida en \"es2022\", \"esnext\", \"system\", \"node16\", \"node18\" \"nodenext\" o \"preserve\", y la opción \"target\" está establecida en \"es2017\" o superior.", "Top_level_await_using_statements_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_sys_2854": "Las declaraciones \"await using\" de nivel superior solo se permiten cuando la opción \"module\" está establecida en \"es2022\", \"esnext\", \"system\", \"node16\", \"node18\", \"nodenext\" o \"preserve\", y la opción \"target\" está establecida en \"es2017\" o superior.", "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046": "Las declaraciones de nivel superior de los archivos .d.ts deben comenzar con un modificador \"declare\" o \"export\".", "Top_level_for_await_loops_are_only_allowed_when_the_module_option_is_set_to_es2022_esnext_system_nod_1432": "Los bucles \"for await\" de nivel superior solo se permiten cuando la opción \"module\" está establecida en \"es2022\", \"esnext\", \"system\", \"node16\", \"node18\", \"nodenext\" o \"preserve\", y la opción \"target\" está establecida en \"es2017\" o superior.", "Trailing_comma_not_allowed_1009": "No se permite la coma final.", "Transpile_each_file_as_a_separate_module_similar_to_ts_transpileModule_6153": "Transpilar cada archivo como un módulo aparte (parecido a \"ts.transpileModule\").", "Try_npm_i_save_dev_types_Slash_1_if_it_exists_or_add_a_new_declaration_d_ts_file_containing_declare__7035": "<PERSON><PERSON><PERSON> \"npm i --save-dev @types/{1}\" si existe o agregue un nuevo archivo de declaración (.d.ts) que incluya \"declare module '{0}';\".", "Trying_other_entries_in_rootDirs_6110": "Se probarán otras entradas de \"rootDirs\".", "Trying_substitution_0_candidate_module_location_Colon_1_6093": "Probando la sustitución '{0}', ubicación candidata para el módulo: '{1}'.", "Tuple_type_0_of_length_1_has_no_element_at_index_2_2493": "El tipo de tupla \"{0}\" de longitud \"{1}\" no tiene ningún elemento en el índice \"{2}\".", "Tuple_type_arguments_circularly_reference_themselves_4110": "Los argumentos de tipo de tupla se hacen referencia a sí mismos de forma circular.", "Type_0_can_only_be_iterated_through_when_using_the_downlevelIteration_flag_or_with_a_target_of_es201_2802": "El tipo \"{0}\" solo puede iterarse cuando se usa la marca \"--downlevelIteration\" o con un valor \"--target\" de \"es2015\" o superior.", "Type_0_cannot_be_used_as_an_index_type_2538": "El tipo '{0}' no se puede usar como tipo de índice.", "Type_0_cannot_be_used_to_index_type_1_2536": "El tipo '{0}' no se puede usar para indexar el tipo '{1}'.", "Type_0_does_not_satisfy_the_constraint_1_2344": "El tipo '{0}' no cumple la restricción '{1}'.", "Type_0_does_not_satisfy_the_expected_type_1_1360": "El tipo \"{0}\" no satisface el tipo esperado \"{1}\".", "Type_0_has_no_call_signatures_2757": "El tipo \"{0}\" no tiene signaturas de llamada.", "Type_0_has_no_construct_signatures_2761": "El tipo \"{0}\" no tiene signaturas de construcción.", "Type_0_has_no_matching_index_signature_for_type_1_2537": "El tipo '{0}' no tiene una signatura de índice correspondiente al tipo '{1}'.", "Type_0_has_no_properties_in_common_with_type_1_2559": "El tipo \"{0}\" no tiene propiedades en común con el tipo \"{1}\".", "Type_0_has_no_signatures_for_which_the_type_argument_list_is_applicable_2635": "El tipo \"{0}\" no tiene firmas para las que sea aplicable la lista de argumentos de tipo.", "Type_0_is_generic_and_can_only_be_indexed_for_reading_2862": "El tipo “{0}” es genérico y solo se puede indizar para lectura.", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_2739": "Al tipo \"{0}\" le faltan las propiedades siguientes del tipo \"{1}\": {2}", "Type_0_is_missing_the_following_properties_from_type_1_Colon_2_and_3_more_2740": "Al tipo \"{0}\" le faltan las propiedades siguientes del tipo \"{1}\": {2} y {3} más.", "Type_0_is_not_a_constructor_function_type_2507": "El tipo '{0}' no es un tipo de función de constructor.", "Type_0_is_not_a_valid_async_function_return_type_in_ES5_because_it_does_not_refer_to_a_Promise_compa_1055": "El tipo “{0}” no es un tipo de valor devuelto válido para una función asincrónica en ES5, porque no hace referencia a un valor de constructor compatible con promesas.", "Type_0_is_not_an_array_type_2461": "'{0}' no es un tipo de matriz.", "Type_0_is_not_an_array_type_or_a_string_type_2495": "'{0}' no es un tipo de matriz o un tipo de cadena.", "Type_0_is_not_an_array_type_or_a_string_type_or_does_not_have_a_Symbol_iterator_method_that_returns__2549": "El tipo \"{0}\" no es un tipo de matriz o un tipo de cadena o no tiene un método \"[Symbol.iterator]()\" que devuelve un iterador.", "Type_0_is_not_an_array_type_or_does_not_have_a_Symbol_iterator_method_that_returns_an_iterator_2548": "El tipo \"{0}\" no es un tipo de matriz o no tiene un método \"[Symbol.iterator]()\" que devuelve un iterador.", "Type_0_is_not_assignable_to_type_1_2322": "El tipo '{0}' no se puede asignar al tipo '{1}'.", "Type_0_is_not_assignable_to_type_1_Did_you_mean_2_2820": "El tipo '{0}' no se puede asignar al tipo '{1}'. ¿Quería decir \"{2}\"?", "Type_0_is_not_assignable_to_type_1_Two_different_types_with_this_name_exist_but_they_are_unrelated_2719": "El tipo \"{0}\" no se puede asignar al tipo \"{1}\". Existen dos tipos distintos con este nombre, pero no están relacionados.", "Type_0_is_not_assignable_to_type_1_as_implied_by_variance_annotation_2636": "El tipo “{0}” no se puede asignar al tipo “{1}”, tal y como implica la anotación de desviación.", "Type_0_is_not_assignable_to_type_1_as_required_for_computed_enum_member_values_18033": "El tipo “{0}” no se puede asignar al tipo “{1}” según sea necesario para los valores de miembro de enumeración calculados.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2375": "El tipo '{0}' no se puede asignar al tipo '{1}' con 'exactOptionalPropertyTypes: true'. Considere la posibilidad de agregar \"undefined\" a los tipos de propiedades del destino.", "Type_0_is_not_assignable_to_type_1_with_exactOptionalPropertyTypes_Colon_true_Consider_adding_undefi_2412": "El tipo '{0}' no se puede asignar al tipo '{1}' con 'exactOptionalPropertyTypes: true'. Considere la posibilidad de agregar \"undefined\" al tipo del destino.", "Type_0_is_not_comparable_to_type_1_2678": "El tipo '{0}' no se puede comparar con el tipo '{1}'.", "Type_0_is_not_generic_2315": "El tipo '{0}' no es genérico.", "Type_0_may_represent_a_primitive_value_which_is_not_permitted_as_the_right_operand_of_the_in_operato_2638": "El tipo \"{0}\" puede representar un valor primitivo, que no se permite como operando derecho del operador \"in\".", "Type_0_must_have_a_Symbol_asyncIterator_method_that_returns_an_async_iterator_2504": "El tipo \"{0}\" debe tener un método \"[Symbol.asyncIterator]()\" que devuelve un iterador de asincronía.", "Type_0_must_have_a_Symbol_iterator_method_that_returns_an_iterator_2488": "El tipo \"{0}\" debe tener un método \"[Symbol.iterator]()\" que devuelve un iterador.", "Type_0_provides_no_match_for_the_signature_1_2658": "El tipo \"{0}\" no proporciona ninguna coincidencia para la signatura \"{1}\".", "Type_0_recursively_references_itself_as_a_base_type_2310": "El tipo '{0}' se hace referencia a sí mismo de forma recursiva como tipo base.", "Type_Checking_6248": "Comprobación de tipos", "Type_alias_0_circularly_references_itself_2456": "El alias de tipo '{0}' se hace referencia a sí mismo de forma circular.", "Type_alias_must_be_given_a_name_1439": "Se debe asignar un nombre al alias de tipo.", "Type_alias_name_cannot_be_0_2457": "El nombre del alias de tipo no puede ser \"{0}\".", "Type_aliases_can_only_be_used_in_TypeScript_files_8008": "<PERSON> alias de tipo solo se pueden usar en los archivos TypeScript.", "Type_annotation_cannot_appear_on_a_constructor_declaration_1093": "Una anotación de tipo no puede aparecer en una declaración de constructor.", "Type_annotations_can_only_be_used_in_TypeScript_files_8010": "Las anotaciones de tipo solo se pueden usar en los archivos TypeScript.", "Type_argument_expected_1140": "Se esperaba un argumento de tipo.", "Type_argument_list_cannot_be_empty_1099": "La lista de argumentos de tipo no puede estar vacía.", "Type_arguments_can_only_be_used_in_TypeScript_files_8011": "Los argumentos de tipo solo se pueden usar en los archivos TypeScript.", "Type_arguments_for_0_circularly_reference_themselves_4109": "Los argumentos de tipo de \"{0}\" se hacen referencia a sí mismos de forma circular.", "Type_assertion_expressions_can_only_be_used_in_TypeScript_files_8016": "Las expresiones de aserción de tipo solo se pueden usar en los archivos TypeScript.", "Type_at_position_0_in_source_is_not_compatible_with_type_at_position_1_in_target_2626": "El tipo en la posición {0} del origen no es compatible con el tipo en la posición {1} del destino.", "Type_at_positions_0_through_1_in_source_is_not_compatible_with_type_at_position_2_in_target_2627": "El tipo en las posiciones {0} a {1} del origen no es compatible con el tipo en la posición {2} del destino.", "Type_containing_private_name_0_can_t_be_used_with_isolatedDeclarations_9039": "El tipo que contiene el nombre privado “{0}” no se puede usar con --isolatedDeclarations.", "Type_declaration_files_to_be_included_in_compilation_6124": "Archivos de declaración de tipos que se incluirán en la compilación.", "Type_expected_1110": "Se esperaba un tipo.", "Type_import_assertions_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1456": "Las aserciones de importación de tipos deben tener exactamente una clave - \"resolution-mode\" - con el valor \"import\" o \"require\".", "Type_import_attributes_should_have_exactly_one_key_resolution_mode_with_value_import_or_require_1464": "Los atributos de importación de tipos deben tener exactamente una clave, “resolution-mode”, con el valor “import” o “require”.", "Type_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribute_1542": "La importación de tipos de un módulo ECMAScript desde un módulo CommonJS debe tener un atributo \"resolution-mode\".", "Type_instantiation_is_excessively_deep_and_possibly_infinite_2589": "La creación de una instancia de tipo es excesivamente profunda y posiblemente infinita.", "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062": "Se hace referencia al tipo directa o indirectamente en la devolución de llamada de entrega de su propio método \"then\".", "Type_library_referenced_via_0_from_file_1_1402": "Biblioteca de tipos a la que se hace referencia mediante \"{0}\" desde el archivo \"{1}\"", "Type_library_referenced_via_0_from_file_1_with_packageId_2_1403": "Biblioteca de tipos a la que se hace referencia mediante \"{0}\" desde el archivo \"{1}\" con el valor packageId \"{2}\"", "Type_of_await_operand_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member_1320": "El tipo de operando \"await\" debe ser una promesa válida o no debe contener un miembro \"then\" invocable.", "Type_of_computed_property_s_value_is_0_which_is_not_assignable_to_type_1_2418": "El tipo de valor de la propiedad calculada es \"{0}\", que no se puede asignar al tipo \"{1}\".", "Type_of_instance_member_variable_0_cannot_reference_identifier_1_declared_in_the_constructor_2844": "El tipo de variable miembro de instancia \"{0}\" no puede hacer referencia al identificador \"{1}\" declarado en el constructor.", "Type_of_iterated_elements_of_a_yield_Asterisk_operand_must_either_be_a_valid_promise_or_must_not_con_1322": "El tipo de elementos iterados de un operando \"yield*\" debe ser una promesa válida o no debe contener un miembro \"then\" invocable.", "Type_of_property_0_circularly_references_itself_in_mapped_type_1_2615": "El tipo de propiedad \"{0}\" hace referencia circular a sí misma en el tipo asignado \"{1}\".", "Type_of_yield_operand_in_an_async_generator_must_either_be_a_valid_promise_or_must_not_contain_a_cal_1321": "El tipo de operando \"yield\" en un generador asincrónico debe ser una promesa válida o no debe contener un miembro \"then\" invocable.", "Type_only_import_of_an_ECMAScript_module_from_a_CommonJS_module_must_have_a_resolution_mode_attribut_1541": "La importación de solo tipo de un módulo ECMAScript desde un módulo CommonJS debe tener un atributo \"resolution-mode\".", "Type_originates_at_this_import_A_namespace_style_import_cannot_be_called_or_constructed_and_will_cau_7038": "El tipo se origina en esta importación. No se puede construir ni llamar a una importación de estilo de espacio de nombres y provocará un error en tiempo de ejecución. Considere la posibilidad de usar una importación predeterminada o require aquí en su lugar.", "Type_parameter_0_has_a_circular_constraint_2313": "El parámetro de tipo '{0}' tiene una restricción circular.", "Type_parameter_0_has_a_circular_default_2716": "El parámetro de tipo \"{0}\" tiene un valor circular predeterminado.", "Type_parameter_0_of_call_signature_from_exported_interface_has_or_is_using_private_name_1_4008": "El parámetro de tipo '{0}' de la signatura de llamada de la interfaz exportada tiene o usa el nombre privado '{1}'.", "Type_parameter_0_of_constructor_signature_from_exported_interface_has_or_is_using_private_name_1_4006": "El parámetro de tipo '{0}' de la signatura de constructor de la interfaz exportada tiene o usa el nombre privado '{1}'.", "Type_parameter_0_of_exported_class_has_or_is_using_private_name_1_4002": "El parámetro de tipo '{0}' de la clase exportada tiene o usa el nombre privado '{1}'.", "Type_parameter_0_of_exported_function_has_or_is_using_private_name_1_4016": "El parámetro de tipo '{0}' de la función exportada tiene o usa el nombre privado '{1}'.", "Type_parameter_0_of_exported_interface_has_or_is_using_private_name_1_4004": "El parámetro de tipo '{0}' de la interfaz exportada tiene o usa el nombre privado '{1}'.", "Type_parameter_0_of_exported_mapped_object_type_is_using_private_name_1_4103": "El parámetro de tipo \"{0}\" del tipo de objeto asignado exportado usa un nombre privado \"{1}\".", "Type_parameter_0_of_exported_type_alias_has_or_is_using_private_name_1_4083": "El parámetro de tipo '{0}' del alias del tipo exportado tiene o usa un nombre privado '{1}'.", "Type_parameter_0_of_method_from_exported_interface_has_or_is_using_private_name_1_4014": "El parámetro de tipo '{0}' del método de la interfaz exportada tiene o usa el nombre privado '{1}'.", "Type_parameter_0_of_public_method_from_exported_class_has_or_is_using_private_name_1_4012": "El parámetro de tipo '{0}' del método público de la clase exportada tiene o usa el nombre privado '{1}'.", "Type_parameter_0_of_public_static_method_from_exported_class_has_or_is_using_private_name_1_4010": "El parámetro de tipo '{0}' del método estático público de la clase exportada tiene o usa el nombre privado '{1}'.", "Type_parameter_declaration_expected_1139": "Se esperaba una declaración de parámetros de tipo.", "Type_parameter_declarations_can_only_be_used_in_TypeScript_files_8004": "Las declaraciones de parámetros de tipo solo se pueden usar en los archivos TypeScript.", "Type_parameter_defaults_can_only_reference_previously_declared_type_parameters_2744": "Los valores predeterminados del parámetro de tipo solo pueden hacer referencia a parámetros de tipo declarados previamente.", "Type_parameter_list_cannot_be_empty_1098": "La lista de parámetros de tipo no puede estar vacía.", "Type_parameter_name_cannot_be_0_2368": "El nombre del parámetro de tipo no puede ser \"{0}\".", "Type_parameters_cannot_appear_on_a_constructor_declaration_1092": "Los parámetros de tipo no pueden aparecer en una declaración de constructor.", "Type_predicate_0_is_not_assignable_to_1_1226": "El predicado de tipo '{0}' no se puede asignar a '{1}'.", "Type_produces_a_tuple_type_that_is_too_large_to_represent_2799": "El tipo genera un tipo de tupla demasiado grande para representarlo.", "Type_reference_directive_0_was_not_resolved_6120": "======== No se resolvió la directiva de referencia de tipo '{0}'. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_primary_Colon_2_6119": "======== La directiva de referencia de tipo '{0}' se resolvió correctamente como '{1}', principal: {2}. ========", "Type_reference_directive_0_was_successfully_resolved_to_1_with_Package_ID_2_primary_Colon_3_6219": "======== La directiva de referencia de tipo \"{0}\" se resolvió correctamente como \"{1}\" con el identificador de paquete \"{2}\", principal: {3}. ========", "Type_satisfaction_expressions_can_only_be_used_in_TypeScript_files_8037": "Las expresiones de satisfacción de tipo solo se pueden usar en archivos TypeScript.", "Types_cannot_appear_in_export_declarations_in_JavaScript_files_18043": "Los tipos no pueden aparecer en declaraciones de exportación en archivos JavaScript.", "Types_have_separate_declarations_of_a_private_property_0_2442": "Los tipos tienen declaraciones independientes de una propiedad '{0}' privada.", "Types_of_construct_signatures_are_incompatible_2419": "Los tipos de signaturas de construcción son incompatibles.", "Types_of_parameters_0_and_1_are_incompatible_2328": "Los tipos de parámetros '{0}' y '{1}' no son compatibles.", "Types_of_property_0_are_incompatible_2326": "Los tipos de propiedad '{0}' no son compatibles.", "Unable_to_open_file_0_6050": "No se puede abrir el archivo '{0}'.", "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238": "No se puede resolver la signatura de elemento Decorator de una clase cuando se llama como expresión.", "Unable_to_resolve_signature_of_method_decorator_when_called_as_an_expression_1241": "No se puede resolver la signatura de elemento Decorator de un método cuando se llama como expresión.", "Unable_to_resolve_signature_of_parameter_decorator_when_called_as_an_expression_1239": "No se puede resolver la signatura de elemento Decorator de un parámetro cuando se llama como expresión.", "Unable_to_resolve_signature_of_property_decorator_when_called_as_an_expression_1240": "No se puede resolver la signatura de elemento Decorator de una propiedad cuando se llama como expresión.", "Undetermined_character_escape_1513": "Escape de caracteres no determinado.", "Unexpected_0_Did_you_mean_to_escape_it_with_backslash_1508": "“{0}” inesperado. ¿Desea escaparlo con barra diagonal inversa?", "Unexpected_end_of_text_1126": "Final de texto inesperado.", "Unexpected_keyword_or_identifier_1434": "Identificador o palabra clave inesperados.", "Unexpected_token_1012": "Token inesperado.", "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068": "Token inesperado. Se esperaba un constructor, un método, un descriptor de acceso o una propiedad.", "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069": "Token inesperado. Se esperaba un nombre de parámetro de tipo sin llaves.", "Unexpected_token_Did_you_mean_or_gt_1382": "Token inesperado. ¿Pretendía usar \"{'>'}\" o \"&gt;\"?", "Unexpected_token_Did_you_mean_or_rbrace_1381": "Token inesperado. ¿Pretendía usar \"{'}'}\" o \"&rbrace;\"?", "Unexpected_token_expected_1179": "Token inesperado. Se esperaba \"{\".", "Unicode_escape_sequence_cannot_appear_here_17021": "La secuencia de escape Unicode no puede aparecer aquí.", "Unicode_escape_sequences_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v_flag_is_se_1538": "Las secuencias de escape Unicode solo están disponibles cuando se establecen las marcas Unicode (u) o Unicode Sets (v).", "Unicode_property_value_expressions_are_only_available_when_the_Unicode_u_flag_or_the_Unicode_Sets_v__1530": "Las expresiones de valor de propiedad Unicode solo están disponibles cuando se establecen las marcas Unicode (u) o Unicode Sets (v).", "Unknown_Unicode_property_name_1524": "Nombre de propiedad Unicode desconocido.", "Unknown_Unicode_property_name_or_value_1529": "Nombre o valor de propiedad Unicode desconocido.", "Unknown_Unicode_property_value_1526": "Valor de propiedad Unicode desconocido.", "Unknown_build_option_0_5072": "Opción de compilación \"{0}\" desconocida.", "Unknown_build_option_0_Did_you_mean_1_5077": "Opción de compilación \"{0}\" desconocida. ¿Pretendía usar \"{1}\"?", "Unknown_compiler_option_0_5023": "Opción '{0}' del compilador desconocida.", "Unknown_compiler_option_0_Did_you_mean_1_5025": "Opción del compilador \"{0}\" desconocida. ¿Pretendía usar \"{1}\"?", "Unknown_keyword_or_identifier_Did_you_mean_0_1435": "Identificador o palabra clave desconocidos. ¿Quiso decir \"{0}\"?", "Unknown_option_excludes_Did_you_mean_exclude_6114": "Opción 'excludes' desconocida. ¿Quería decir 'exclude'?", "Unknown_regular_expression_flag_1499": "Marca de expresión regular desconocida.", "Unknown_type_acquisition_option_0_17010": "Opción '{0}' de adquisición de tipos desconocida.", "Unknown_type_acquisition_option_0_Did_you_mean_1_17018": "Opción de adquisición de tipos \"{0}\" desconocida. ¿Pretendía usar \"{1}\"?", "Unknown_watch_option_0_5078": "Opción de inspección \"{0}\" desconocida.", "Unknown_watch_option_0_Did_you_mean_1_5079": "Opción de inspección \"{0}\" desconocida. ¿Pretendía usar \"{1}\"?", "Unreachable_code_detected_7027": "Se ha detectado código inaccesible.", "Unterminated_Unicode_escape_sequence_1199": "Secuencia de escape Unicode sin terminar.", "Unterminated_quoted_string_in_response_file_0_6045": "Cadena entrecomillada sin terminar en el archivo de respuesta '{0}'.", "Unterminated_regular_expression_literal_1161": "Literal de expresión regular sin terminar.", "Unterminated_string_literal_1002": "Literal de cadena sin terminar.", "Unterminated_template_literal_1160": "Literal de plantilla sin terminar.", "Untyped_function_calls_may_not_accept_type_arguments_2347": "Las llamadas a función sin tipo no pueden aceptar argumentos de tipo.", "Unused_label_7028": "Etiqueta no usada.", "Unused_ts_expect_error_directive_2578": "Directiva \"@ts-expect-error\" no usada.", "Update_import_from_0_90058": "Actualizar importación desde “{0}”", "Update_modifiers_of_0_90061": "Actualizar modificadores de “{0}”", "Updating_output_timestamps_of_project_0_6359": "Actualizando las marcas de hora de salida del proyecto \"{0}\"...", "Updating_unchanged_output_timestamps_of_project_0_6371": "Actualizando las marcas de hora de salida no modificadas del proyecto \"{0}\"...", "Use_0_95174": "Usar `{0}`.", "Use_0_instead_5106": "Use “{0}” en su lugar.", "Use_Number_isNaN_in_all_conditions_95175": "Use \"Number.isNaN\" en todas las condiciones.", "Use_element_access_for_0_95145": "Usar acceso de elemento para \"{0}\"", "Use_element_access_for_all_undeclared_properties_95146": "Use el acceso de elemento para todas las propiedades no declaradas.", "Use_import_type_95180": "Usar “import type”", "Use_synthetic_default_member_95016": "Use el miembro sintético \"default\".", "Use_the_package_json_exports_field_when_resolving_package_imports_6408": "Use el campo “exports” de package.json al resolver las importaciones de paquetes.", "Use_the_package_json_imports_field_when_resolving_imports_6409": "Use el campo “imports” de package.json al resolver importaciones.", "Use_type_0_95181": "Usar “type {0}”", "Using_0_subpath_1_with_target_2_6404": "<PERSON>and<PERSON> '{0}' subruta '{1}' con destino '{2}'.", "Using_JSX_fragments_requires_fragment_factory_0_to_be_in_scope_but_it_could_not_be_found_2879": "El uso de fragmentos JSX requiere que el generador de fragmentos \"{0}\" esté en el ámbito, pero no se encontró.", "Using_a_string_in_a_for_of_statement_is_only_supported_in_ECMAScript_5_and_higher_2494": "El uso de una cadena en una instrucción \"for...of\" solo se admite en ECMAScript 5 y versiones posteriores.", "Using_build_b_will_make_tsc_behave_more_like_a_build_orchestrator_than_a_compiler_This_is_used_to_tr_6915": "Con --build, -b hará que tsc se comporte más como un orquestador de compilación que como un compilador. Se usa para desencadenar la compilación de proyectos compuestos, sobre los que puede obtener más información en {0}", "Using_compiler_options_of_project_reference_redirect_0_6215": "Uso de las opciones del compilador de redireccionamiento de la referencia del proyecto \"{0}\".", "VERSION_6036": "VERSIÓN", "Value_of_type_0_has_no_properties_in_common_with_type_1_Did_you_mean_to_call_it_2560": "El valor de tipo \"{0}\" no tiene propiedades en común con el tipo \"{1}\". ¿Realmente quiere llamarlo?", "Value_of_type_0_is_not_callable_Did_you_mean_to_include_new_2348": "No se puede llamar a un valor de tipo '{0}'. ¿Pretendía incluir \"new\"?", "Variable_0_implicitly_has_an_1_type_7005": "La variable '{0}' tiene un tipo '{1}' implícitamente.", "Variable_0_implicitly_has_an_1_type_but_a_better_type_may_be_inferred_from_usage_7043": "La variable \"{0}\" tiene un tipo \"{1}\" de forma implícita, pero se puede inferir un tipo más adecuado a partir del uso.", "Variable_0_implicitly_has_type_1_in_some_locations_but_a_better_type_may_be_inferred_from_usage_7046": "La variable \"{0}\" tiene un tipo \"{1}\" de forma implícita en algunas ubicaciones, pero se puede inferir un tipo más adecuado a partir del uso.", "Variable_0_implicitly_has_type_1_in_some_locations_where_its_type_cannot_be_determined_7034": "La variable '{0}' tiene implícitamente el tipo '{1}' en algunos sitios donde no se puede determinar su tipo.", "Variable_0_is_used_before_being_assigned_2454": "La variable '{0}' se usa antes de asignarla.", "Variable_declaration_expected_1134": "Se esperaba una declaración de variable.", "Variable_declaration_list_cannot_be_empty_1123": "La lista de declaraciones de variable no puede estar vacía.", "Variable_declaration_not_allowed_at_this_location_1440": "No se permite una declaración de variable en esta ubicación.", "Variable_must_have_an_explicit_type_annotation_with_isolatedDeclarations_9010": "La variable debe tener una anotación de tipo explícita con --isolatedDeclarations.", "Variables_with_multiple_declarations_cannot_be_inlined_95186": "Las variables con varias declaraciones no se pueden insertar.", "Variadic_element_at_position_0_in_source_does_not_match_element_at_position_1_in_target_2625": "El elemento variádico en la posición {0} del origen no coincide con el elemento en la posición {1} del destino.", "Variance_annotations_are_only_supported_in_type_aliases_for_object_function_constructor_and_mapped_t_2637": "Las anotaciones de varianza solo se admiten en alias de tipo para los tipos objeto, función, constructor y asignado.", "Version_0_6029": "Versión {0}", "Visit_https_Colon_Slash_Slashaka_ms_Slashtsconfig_to_read_more_about_this_file_95110": "Visite https://aka.ms/tsconfig para obtener más información acerca de este archivo", "WATCH_OPTIONS_6918": "OPCIONES DE INSPECCIÓN", "Watch_and_Build_Modes_6250": "Modos de compilación e inspección", "Watch_input_files_6005": "Inspeccionar archivos de entrada.", "Watch_option_0_requires_a_value_of_type_1_5080": "La opción \"{0}\" de inspección requiere un valor de tipo {1}.", "We_can_only_write_a_type_for_0_by_adding_a_type_for_the_entire_parameter_here_2843": "Solo se puede escribir un tipo para '{0}' agregando aquí un tipo para todo el parámetro.", "When_assigning_functions_check_to_ensure_parameters_and_the_return_values_are_subtype_compatible_6698": "Al asignar funciones, compruebe que efectivamente los parámetros y los valores devueltos son compatibles con el subtipo.", "When_type_checking_take_into_account_null_and_undefined_6699": "Al comprobar tipos, tenga en cuenta \"null\" y \"undefined\".", "Whether_to_keep_outdated_console_output_in_watch_mode_instead_of_clearing_the_screen_6191": "Si se debe mantener la salida de la consola no actualizada en el modo de inspección en lugar de borrar la pantalla.", "Wrap_all_invalid_characters_in_an_expression_container_95109": "Encapsular todos los caracteres no válidos en un contenedor de expresiones", "Wrap_all_invalid_decorator_expressions_in_parentheses_95195": "Incluir todas las expresiones de decorador no válidas entre paréntesis", "Wrap_all_object_literal_with_parentheses_95116": "Encapsular todos los literales de objeto entre paréntesis", "Wrap_all_unparented_JSX_in_JSX_fragment_95121": "Encapsular todos los JSX no primarios en el fragmento de JSX", "Wrap_in_JSX_fragment_95120": "Ajustar en fragmento de JSX", "Wrap_in_parentheses_95194": "Incluir entre paréntesis", "Wrap_invalid_character_in_an_expression_container_95108": "Encapsular el carácter no válido en un contenedor de expresiones", "Wrap_the_following_body_with_parentheses_which_should_be_an_object_literal_95113": "Encapsular el cuerpo siguiente entre paréntesis, lo cual debe ser un literal de objeto", "You_can_learn_about_all_of_the_compiler_options_at_0_6913": "Puede obtener información sobre todas las opciones del compilador en {0}", "You_cannot_rename_a_module_via_a_global_import_8031": "No se puede cambiar el nombre de un módulo mediante una importación global.", "You_cannot_rename_elements_that_are_defined_in_a_node_modules_folder_8035": "No se puede cambiar el nombre de los elementos definidos en una carpeta 'node_modules'.", "You_cannot_rename_elements_that_are_defined_in_another_node_modules_folder_8036": "No se puede cambiar el nombre de los elementos definidos en otra carpeta 'node_modules'.", "You_cannot_rename_elements_that_are_defined_in_the_standard_TypeScript_library_8001": "No se puede cambiar el nombre de elementos definidos en la biblioteca TypeScript estándar.", "You_cannot_rename_this_element_8000": "No se puede cambiar el nombre a este elemento.", "_0_accepts_too_few_arguments_to_be_used_as_a_decorator_here_Did_you_mean_to_call_it_first_and_write__1329": "\"{0}\" no acepta suficientes argumentos para utilizarse como decorador aquí. ¿Pretendía llamar primero y escribir \"@{0}()\"?", "_0_and_1_index_signatures_are_incompatible_2330": "Las signaturas de índice \"{0}\" y \"{1}\" no son compatibles.", "_0_and_1_operations_cannot_be_mixed_without_parentheses_5076": "Las operaciones \"{0}\" y \"{1}\" no se pueden mezclar sin paréntesis.", "_0_are_specified_twice_The_attribute_named_0_will_be_overwritten_2710": "\"{0}\" se especifica dos veces. El atributo denominado \"{0}\" se sobrescribirá.", "_0_at_the_end_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17019": "“{0}” al final de un tipo no es una sintaxis de TypeScript válida. ¿Pretendía escribir “{1}”?", "_0_at_the_start_of_a_type_is_not_valid_TypeScript_syntax_Did_you_mean_to_write_1_17020": "“{0}” al principio de un tipo no es una sintaxis de TypeScript válida. ¿Pretendía escribir “{1}”?", "_0_can_only_be_imported_by_turning_on_the_esModuleInterop_flag_and_using_a_default_import_2596": "\"{0}\" solo se puede importar si se activa la marca \"esModuleInterop\" y se usa una importación predeterminada.", "_0_can_only_be_imported_by_using_a_default_import_2595": "\"{0}\" solo se puede importar si se usa una importación predeterminada.", "_0_can_only_be_imported_by_using_a_require_call_or_by_turning_on_the_esModuleInterop_flag_and_using__2598": "\"{0}\" solo se puede importar si se usa una llamada a \"require\" o bien se activa la marca \"esModuleInterop\" y se usa una importación predeterminada.", "_0_can_only_be_imported_by_using_a_require_call_or_by_using_a_default_import_2597": "\"{0}\" solo se puede importar si se usa una llamada a \"require\" o una importación predeterminada.", "_0_can_only_be_imported_by_using_import_1_require_2_or_a_default_import_2616": "\"{0}\" solo se puede importar si se usa \"import {1} = require({2})\" o una importación predeterminada.", "_0_can_only_be_imported_by_using_import_1_require_2_or_by_turning_on_the_esModuleInterop_flag_and_us_2617": "\"{0}\" solo se puede importar si se usa \"import {1} = require({2})\" o bien se activa la marca \"esModuleInterop\" y se usa una importación predeterminada.", "_0_cannot_be_used_as_a_JSX_component_2786": "No se puede usar \"{0}\" como componente JSX.", "_0_cannot_be_used_as_a_value_because_it_was_exported_using_export_type_1362": "No se puede usar \"{0}\" como valor porque se exportó mediante \"export type\".", "_0_cannot_be_used_as_a_value_because_it_was_imported_using_import_type_1361": "No se puede usar \"{0}\" como valor porque se importó mediante \"import type\".", "_0_components_don_t_accept_text_as_child_elements_Text_in_JSX_has_the_type_string_but_the_expected_t_2747": "Los componentes \"{0}\" no aceptan el texto como elemento secundario. El texto de JSX tiene el tipo \"string\", pero el tipo que se esperaba de \"{1}\" es \"{2}\".", "_0_could_be_instantiated_with_an_arbitrary_type_which_could_be_unrelated_to_1_5082": "<PERSON><PERSON><PERSON> crearse una instancia de \"{0}\" con un tipo arbitrario que podría no estar relacionado con \"{1}\".", "_0_declarations_can_only_be_declared_inside_a_block_1156": "Las declaraciones “{0}” solo se pueden declarar dentro de un bloque.", "_0_declarations_can_only_be_used_in_TypeScript_files_8006": "Las declaraciones \"{0}\" solo se pueden usar en los archivos TypeScript.", "_0_declarations_may_not_have_binding_patterns_1492": "Las declaraciones “{0}” no pueden tener patrones de enlace.", "_0_declarations_must_be_initialized_1155": "Las declaraciones “{0}” deben inicializarse.", "_0_expected_1005": "Se esperaba '{0}'.", "_0_has_a_string_type_but_must_have_syntactically_recognizable_string_syntax_when_isolatedModules_is__18055": "“{0}” tiene un tipo de cadena, pero debe tener sintaxis de cadena reconocible sintácticamente cuando “isolatedModules” está habilitado.", "_0_has_no_exported_member_named_1_Did_you_mean_2_2724": "\"{0}\" no tiene ningún miembro exportado con el nombre \"{1}\". ¿Pretendía usar \"{2}\"?", "_0_implicitly_has_an_1_return_type_but_a_better_type_may_be_inferred_from_usage_7050": "\"{0}\" tiene un tipo de valor devuelto \"{1}\" de forma implícita, pero se puede inferir un tipo más adecuado a partir del uso.", "_0_implicitly_has_return_type_any_because_it_does_not_have_a_return_type_annotation_and_is_reference_7023": "'{0}' tiene el tipo de valor devuelto \"any\" implícitamente porque no tiene una anotación de tipo de valor devuelto y se hace referencia a este directa o indirectamente en una de sus expresiones return.", "_0_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_and_is_referenced_directly_or__7022": "'{0}' tiene el tipo de valor devuelto \"any\" implícitamente porque no tiene una anotación de tipo y se hace referencia a este directa o indirectamente en su propio inicializador.", "_0_index_signatures_are_incompatible_2634": "Las signaturas de índice \"{0}\" no son compatibles.", "_0_index_type_1_is_not_assignable_to_2_index_type_3_2413": "El tipo de índice \"{0}\"' \"{1}\" no se puede asignar al tipo de índice \"{2}\" \"{3}\".", "_0_is_a_primitive_but_1_is_a_wrapper_object_Prefer_using_0_when_possible_2692": "'{0}' es un elemento primitivo, pero '{1}' es un objeto contenedor. Use '{0}' preferentemente cuando sea posible.", "_0_is_a_type_and_cannot_be_imported_in_JavaScript_files_Use_1_in_a_JSDoc_type_annotation_18042": "'{0}' es un tipo y no se puede importar en archivos JavaScript. Use '{1}' en una anotación de tipo JSDoc.", "_0_is_a_type_and_must_be_imported_using_a_type_only_import_when_verbatimModuleSyntax_is_enabled_1484": "“{0}” es un tipo y debe importarse mediante una importación de solo tipo cuando “verbatimModuleSyntax” está habilitado.", "_0_is_an_unused_renaming_of_1_Did_you_intend_to_use_it_as_a_type_annotation_2842": "'{0}' es un cambio de nombre de '{1}' sin usar. ¿Quería usarlo como una anotación de tipo?", "_0_is_assignable_to_the_constraint_of_type_1_but_1_could_be_instantiated_with_a_different_subtype_of_5075": "\"{0}\" puede asignarse a la restricción de tipo \"{1}\", pero no se pudo crear una instancia de \"{1}\" con un subtipo distinto de la restricción \"{2}\".", "_0_is_automatically_exported_here_18044": "'{0}' se exporta automáticamente aquí.", "_0_is_declared_but_its_value_is_never_read_6133": "Se declara \"{0}\", pero su valor no se lee nunca.", "_0_is_declared_but_never_used_6196": "\"{0}\" se declara pero nunca se utiliza.", "_0_is_declared_here_2728": "\"{0}\" se declara aquí.", "_0_is_defined_as_a_property_in_class_1_but_is_overridden_here_in_2_as_an_accessor_2611": "\"{0}\" se define como propiedad en la clase \"{1}\", pero se reemplaza aquí en \"{2}\" como descriptor de acceso.", "_0_is_defined_as_an_accessor_in_class_1_but_is_overridden_here_in_2_as_an_instance_property_2610": "\"{0}\" se define como descriptor de acceso en la clase \"{1}\", pero se reemplaza aquí en \"{2}\" como propiedad de instancia.", "_0_is_deprecated_6385": "\"{0}\" está en desuso.", "_0_is_not_a_valid_meta_property_for_keyword_1_Did_you_mean_2_17012": "\"{0}\" no es una propiedad Meta válida para la palabra clave \"{1}\". ¿Pretendía usar \"{2}\"?", "_0_is_not_allowed_as_a_parameter_name_1390": "No se permite “{0}” como nombre de parámetro.", "_0_is_not_allowed_as_a_variable_declaration_name_1389": "No se permite \"{0}\" como nombre de declaración de variable.", "_0_is_of_type_unknown_18046": "\"{0}\" es de tipo \"unknown\".", "_0_is_possibly_null_18047": "\"{0}\" es posiblemente \"null\".", "_0_is_possibly_null_or_undefined_18049": "\"{0}\" es posiblemente \"null\" o \"undefined\".", "_0_is_possibly_undefined_18048": "\"{0}\" es posiblemente \"undefined\".", "_0_is_referenced_directly_or_indirectly_in_its_own_base_expression_2506": "Se hace referencia a '{0}' directa o indirectamente en su propia expresión base.", "_0_is_referenced_directly_or_indirectly_in_its_own_type_annotation_2502": "Se hace referencia a '{0}' directa o indirectamente en su propia anotación de tipo.", "_0_is_specified_more_than_once_so_this_usage_will_be_overwritten_2783": "\"{0}\" se ha especificado más de una vez, por lo que se sobrescribirá este uso.", "_0_list_cannot_be_empty_1097": "La lista '{0}' no puede estar vacía.", "_0_modifier_already_seen_1030": "El modificador '{0}' ya se ha visto.", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_class_interface_or_type_alias_1274": "El modificador “{0}” solo puede aparecer en un parámetro de tipo de una clase, interfaz o alias de tipo", "_0_modifier_can_only_appear_on_a_type_parameter_of_a_function_method_or_class_1277": "El modificador “{0}” solo puede aparecer en un parámetro de tipo de una función, método o clase", "_0_modifier_cannot_appear_on_a_constructor_declaration_1089": "El modificador '{0}' no puede aparecer en una declaración de constructor.", "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044": "El modificador '{0}' no puede aparecer en un módulo o un elemento de espacio de nombres.", "_0_modifier_cannot_appear_on_a_parameter_1090": "El modificador '{0}' no puede aparecer en un parámetro.", "_0_modifier_cannot_appear_on_a_type_member_1070": "El modificador '{0}' no puede aparecer en un miembro de tipo.", "_0_modifier_cannot_appear_on_a_type_parameter_1273": "El modificador “{0}” no puede aparecer en un parámetro de tipo", "_0_modifier_cannot_appear_on_a_using_declaration_1491": "El modificador “{0}” no puede aparecer en una declaración “using”.", "_0_modifier_cannot_appear_on_an_await_using_declaration_1495": "El modificador “{0}” no puede aparecer en una declaración “await using”.", "_0_modifier_cannot_appear_on_an_index_signature_1071": "El modificador '{0}' no puede aparecer en una signatura de índice.", "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031": "El modificador \"{0}\" no puede aparecer en elementos de clase de este tipo.", "_0_modifier_cannot_be_used_here_1042": "El modificador '{0}' no se puede usar aquí.", "_0_modifier_cannot_be_used_in_an_ambient_context_1040": "El modificador '{0}' no se puede usar en un contexto de ambiente.", "_0_modifier_cannot_be_used_with_1_modifier_1243": "El modificador '{0}' no se puede usar con el modificador '{1}'.", "_0_modifier_cannot_be_used_with_a_private_identifier_18019": "El modificador \"{0}\" no se puede usar con un identificador privado.", "_0_modifier_must_precede_1_modifier_1029": "El modificador \"{0}\" debe preceder al modificador \"{1}\".", "_0_must_be_followed_by_a_Unicode_property_value_expression_enclosed_in_braces_1531": "“\\{0}” debe ir seguido de una expresión de valor de propiedad Unicode entre llaves.", "_0_needs_an_explicit_type_annotation_2782": "\"{0}\" necesita una anotación de tipo explícito.", "_0_only_refers_to_a_type_but_is_being_used_as_a_namespace_here_2702": "'{0}' solo hace referencia a un tipo, pero aquí se usa como espacio de nombres.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_2693": "'{0}' solo hace referencia a un tipo, pero aquí se usa como valor.", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Did_you_mean_to_use_1_in_0_2690": "\"{0}\" solo hace referencia a un tipo, pero aquí se usa como valor. ¿Pretendía usar \"{1} en {0}\"?", "_0_only_refers_to_a_type_but_is_being_used_as_a_value_here_Do_you_need_to_change_your_target_library_2585": "\"{0}\" solo hace referencia a un tipo, pero aquí se usa como valor. ¿Necesita cambiar la biblioteca de destino? Pruebe a cambiar la opción del compilador \"lib\" a es2015 o posterior.", "_0_refers_to_a_UMD_global_but_the_current_file_is_a_module_Consider_adding_an_import_instead_2686": "'{0}' hace referencia a un elemento UMD global, pero el archivo actual es un módulo. Puede agregar una importación en su lugar.", "_0_refers_to_a_value_but_is_being_used_as_a_type_here_Did_you_mean_typeof_0_2749": "\"{0}\" hace referencia a un valor, pero aquí se usa como tipo. ¿Quiso decir \"typeof {0}\"?", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1291": "“{0}” se resuelve en un tipo y debe marcarse como de solo tipo en este archivo antes de volver a exportar cuando “{1}” está habilitado. Considere la posibilidad de usar “import type” donde se importa “{0}”.", "_0_resolves_to_a_type_and_must_be_marked_type_only_in_this_file_before_re_exporting_when_1_is_enable_1292": "“{0}” se resuelve en un tipo y debe marcarse como de solo tipo en este archivo antes de volver a exportar cuando “{1}” está habilitado. Considere la posibilidad de usar “export type { {0} as default }”.", "_0_resolves_to_a_type_only_declaration_and_must_be_imported_using_a_type_only_import_when_verbatimMo_1485": "“{0}” se resuelve en una declaración de solo tipo y debe importarse mediante una importación de solo tipo cuando “verbatimModuleSyntax” está habilitado.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1289": "“{0}” se resuelve en una declaración de solo tipo y debe marcarse como de solo tipo en este archivo antes de volver a exportar cuando “{1}” está habilitado. Considere la posibilidad de usar “import type” donde se importa “{0}”.", "_0_resolves_to_a_type_only_declaration_and_must_be_marked_type_only_in_this_file_before_re_exporting_1290": "“{0}” se resuelve en una declaración de solo tipo y debe marcarse como de solo tipo en este archivo antes de volver a exportar cuando “{1}” está habilitado. Considere la posibilidad de usar “export type { {0} as default }”.", "_0_resolves_to_a_type_only_declaration_and_must_be_re_exported_using_a_type_only_re_export_when_1_is_1448": "“{0}” se resuelve como una declaración de solo tipo y debe volverse a exportar con un tipo de reexportación solo cuando esté habilitada la opción “{1}”.", "_0_should_be_set_inside_the_compilerOptions_object_of_the_config_json_file_6258": "\"{0}\" debe establecerse dentro del objeto \"compilerOptions\" del archivo .json de configuración", "_0_tag_already_specified_1223": "La etiqueta '{0}' ya se ha especificado.", "_0_was_also_declared_here_6203": "\"{0}\" tambi<PERSON> se ha declarado aquí.", "_0_was_exported_here_1377": "\"{0}\" se ha exportado aquí.", "_0_was_imported_here_1376": "\"{0}\" se ha importado aquí.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_return_type_7010": "'{0}' carece de una anotación de tipo de valor devuelto, pero tiene un tipo de valor devuelto '{1}' implícitamente.", "_0_which_lacks_return_type_annotation_implicitly_has_an_1_yield_type_7055": "\"{0}\" carece de una anotación de tipo de valor devuelto, pero tiene un tipo yield \"{1}\" de forma implícita.", "abstract_modifier_can_only_appear_on_a_class_method_or_property_declaration_1242": "El modificador 'abstract' solo puede aparecer en una declaración de propiedad, clase o método.", "accessor_modifier_can_only_appear_on_a_property_declaration_1275": "El modificador 'accessor' solo puede aparecer en una declaración de propiedad.", "and_here_6204": "y aquí.", "arguments_cannot_be_referenced_in_property_initializers_2815": "no se puede hacer referencia a «arguments» en los inicializadores de propiedad.", "auto_Colon_Treat_files_with_imports_exports_import_meta_jsx_with_jsx_Colon_react_jsx_or_esm_format_w_1476": "\"auto\": trate los archivos con importaciones, exportaciones, import.meta, jsx (con jsx: react-jsx) o formato esm (con el módulo: node16+) como módulos.", "await_expression_cannot_be_used_inside_a_class_static_block_18037": "La expresión “await” no se puede usar dentro de un bloque estático de clase.", "await_expressions_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_fi_1375": "Las expresiones \"await\" solo se permiten en el nivel superior de un archivo cuando el archivo es un módulo, pero este archivo no tiene importaciones ni exportaciones. Considere la posibilidad de agregar un elemento \"export {}\" vacío para convertir este archivo en módulo.", "await_expressions_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1308": "Las expresiones \"await\" solo se permiten en las funciones asincrónicas y en los niveles superiores de los módulos.", "await_expressions_cannot_be_used_in_a_parameter_initializer_2524": "Las expresiones \"await\" no se pueden usar en un inicializador de parámetros.", "await_has_no_effect_on_the_type_of_this_expression_80007": "\"await\" no tiene efecto en el tipo de esta expresión.", "await_using_statements_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_th_2853": "Las declaraciones “await using” solo se permiten en el nivel superior de un archivo cuando el archivo es un módulo, pero este archivo no tiene importaciones ni exportaciones. Considere la posibilidad de agregar un elemento \"export {}\" vacío para convertir este archivo en módulo.", "await_using_statements_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_2852": "Las declaraciones “await using” solo se permiten en las funciones asincrónicas y en los niveles superiores de los módulos.", "await_using_statements_cannot_be_used_inside_a_class_static_block_18054": "Las instrucciones “await using” no se pueden usar dentro de un bloque estático de clase.", "baseUrl_option_is_set_to_0_using_this_value_to_resolve_non_relative_module_name_1_6106": "La opción \"baseUrl\" está establecida en \"{0}\", se usará este valor para resolver el nombre de módulo no relativo \"{1}\".", "c_must_be_followed_by_an_ASCII_letter_1512": "“\\c” debe ir seguido de una letra ASCII.", "can_only_be_used_at_the_start_of_a_file_18026": "\"#!\" solo se puede usar al principio de un archivo.", "case_or_default_expected_1130": "Se esperaba \"case\" o \"default\".", "catch_or_finally_expected_1472": "se esperaba \"catch\" o \"finally\".", "const_enum_member_initializer_was_evaluated_to_a_non_finite_value_2477": "El inicializador de miembros de enumeración \"const\" se evaluó con un valor no finito.", "const_enum_member_initializer_was_evaluated_to_disallowed_value_NaN_2478": "El inicializador de miembros de enumeración \"const\" se evaluó con un valor \"NaN\" no permitido.", "const_enum_member_initializers_must_be_constant_expressions_2474": "Los inicializadores de miembro de enumeración const deben ser expresiones constantes.", "const_enums_can_only_be_used_in_property_or_index_access_expressions_or_the_right_hand_side_of_an_im_2475": "Las enumeraciones \"const\" solo se pueden usar en expresiones de acceso de propiedad o índice, o en la parte derecha de una declaración de importación, una asignación de exportación o una consulta de tipo.", "constructor_cannot_be_used_as_a_parameter_property_name_2398": "El elemento \"constructor\" no se puede usar como nombre de propiedad de parámetro.", "constructor_is_a_reserved_word_18012": "\"#constructor\" es una palabra reservada.", "default_Colon_6903": "predeterminadas:", "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102": "No se puede llamar a \"delete\" en un identificador en modo strict.", "export_Asterisk_does_not_re_export_a_default_1195": "\"export *\" no vuelve a exportar una exportación predeterminada.", "export_can_only_be_used_in_TypeScript_files_8003": "\"export =\" solo se puede usar en los archivos TypeScript.", "export_modifier_cannot_be_applied_to_ambient_modules_and_module_augmentations_since_they_are_always__2668": "El modificador 'export' no se puede aplicar a módulos de ambiente ni aumentos de módulos, porque siempre están visibles.", "extends_clause_already_seen_1172": "La cláusula \"extends\" ya se ha visto.", "extends_clause_must_precede_implements_clause_1173": "La cláusula \"extends\" debe preceder a la cláusula \"implements\".", "extends_clause_of_exported_class_0_has_or_is_using_private_name_1_4020": "La cláusula \"extends\" de la clase \"{0}\" exportada tiene o usa el nombre privado \"{1}\".", "extends_clause_of_exported_class_has_or_is_using_private_name_0_4021": "La cláusula \"extends\" de la clase exportada tiene o usa el nombre privado \"{0}\".", "extends_clause_of_exported_interface_0_has_or_is_using_private_name_1_4022": "La cláusula \"extends\" de la interfaz \"{0}\" exportada tiene o usa el nombre privado \"{1}\".", "false_unless_composite_is_set_6906": "\"false\", a menos que se establezca \"composite\"", "false_unless_strict_is_set_6905": "\"false\", a menos que se establezca como \"strict\"", "file_6025": "archivo", "for_await_loops_are_only_allowed_at_the_top_level_of_a_file_when_that_file_is_a_module_but_this_file_1431": "Los bucles \"for await\" solo se permiten en el nivel superior de un archivo cuando el archivo es un módulo, pero este archivo no tiene importaciones ni exportaciones. Considere la posibilidad de agregar un elemento \"export {}\" vacío para convertir este archivo en módulo.", "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103": "Los bucles \"for await\" solo se permiten en las funciones asincrónicas y en los niveles superiores de los módulos.", "for_await_loops_cannot_be_used_inside_a_class_static_block_18038": "Los bucles “for await” no se pueden usar dentro de un bloque estático de clase.", "get_and_set_accessors_cannot_declare_this_parameters_2784": "Los descriptores de acceso \"get\" y \"set\" no pueden declarar parámetros \"this\".", "if_files_is_specified_otherwise_Asterisk_Asterisk_Slash_Asterisk_6908": "\"[]\", si se especifica \"archivos\"; de lo contrario, \"[\"**/*\"]5D;\"", "implements_clause_already_seen_1175": "La cláusula \"implements\" ya se ha visto.", "implements_clauses_can_only_be_used_in_TypeScript_files_8005": "Las cláusulas \"implements\" solo se pueden usar en los archivos TypeScript.", "import_can_only_be_used_in_TypeScript_files_8002": "\"import ... =\" solo se puede usar en los archivos TypeScript.", "infer_declarations_are_only_permitted_in_the_extends_clause_of_a_conditional_type_1338": "Las declaraciones \"infer\" solo se permiten en la cláusula \"extends\" de un tipo condicional.", "k_must_be_followed_by_a_capturing_group_name_enclosed_in_angle_brackets_1510": "“\\k” debe ir seguido de un nombre de grupo de captura entre corchetes angulares.", "let_is_not_allowed_to_be_used_as_a_name_in_let_or_const_declarations_2480": "No se permite usar \"let\" como nombre en las declaraciones \"let\" o \"const\".", "module_AMD_or_UMD_or_System_or_ES6_then_Classic_Otherwise_Node_69010": "módulo === \"AMD\" o \"UMD\" o \"System\" o \"ES6\", después, \"Classic\", de lo contrario \"Node\"", "module_system_or_esModuleInterop_6904": "módulo === \"system\" o esModuleInterop", "new_expression_whose_target_lacks_a_construct_signature_implicitly_has_an_any_type_7009": "La expresión \"new\", a cuyo destino le falta una signatura de construcción, tiene implícitamente un tipo \"any\".", "node_modules_bower_components_jspm_packages_plus_the_value_of_outDir_if_one_is_specified_6907": "\"[\"node_modules\", \"bower_components\", \"jspm_packages\"]\", más el valor de \"outDir\", si se especifica uno.", "one_of_Colon_6900": "uno de:", "one_or_more_Colon_6901": "uno o más:", "options_6024": "Opciones", "or_JSX_element_expected_1145": "Se esperaba \"{\" o un elemento JSX.", "or_expected_1144": "Se esperaba \"{\" o \";\".", "package_json_does_not_have_a_0_field_6100": "\"package.json\" no tiene un campo \"{0}\".", "package_json_does_not_have_a_typesVersions_entry_that_matches_version_0_6207": "El archivo \"package.json\" no tiene ninguna entrada \"typesVersions\" que coincida con la versión \"{0}\".", "package_json_had_a_falsy_0_field_6220": "El archivo \"package.json\" tenía un campo \"{0}\" false.", "package_json_has_0_field_1_that_references_2_6101": "'package.json' tiene el campo '{1}' de '{0}' que hace referencia a '{2}'.", "package_json_has_a_peerDependencies_field_6281": "“package.json” tiene un campo “peerDependencies”.", "package_json_has_a_typesVersions_entry_0_that_is_not_a_valid_semver_range_6209": "El archivo \"package.json\" tiene una entrada \"typesVersions\" \"{0}\" que no es un intervalo de SemVer válido.", "package_json_has_a_typesVersions_entry_0_that_matches_compiler_version_1_looking_for_a_pattern_to_ma_6208": "El archivo \"package.json\" tiene una entrada \"typesVersions\" \"{0}\" que coincide con la versión del compilador \"{1}\"; se busca un patrón que coincida con el nombre de módulo \"{2}\".", "package_json_has_a_typesVersions_field_with_version_specific_path_mappings_6206": "El archivo \"package.json\" tiene un campo \"typesVersions\" con asignaciones de ruta de acceso específicas de la versión.", "package_json_scope_0_explicitly_maps_specifier_1_to_null_6274": "El ámbito de búsqueda package.json scope \"{0}\" asigna explícitamente el especificador \"{1}\" a NULL.", "package_json_scope_0_has_invalid_type_for_target_of_specifier_1_6275": "El ámbito package.json \"{0}\" tiene un tipo no válido para el destino del especificador \"{1}\"", "package_json_scope_0_has_no_imports_defined_6273": "El ámbito de package.json \"{0}\" no tiene importaciones definidas.", "paths_option_is_specified_looking_for_a_pattern_to_match_module_name_0_6091": "Se ha especificado la opción 'paths'. Se buscará un patrón que coincida con el nombre de módulo '{0}'.", "q_is_only_available_inside_character_class_1511": "“\\q” solo está disponible dentro de la clase de caracteres.", "q_must_be_followed_by_string_alternatives_enclosed_in_braces_1521": "“\\q” debe ir seguido de alternativas de cadena entre llaves.", "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024": "El modificador 'readonly' solo puede aparecer en una declaración de propiedad o una signatura de índice.", "readonly_type_modifier_is_only_permitted_on_array_and_tuple_literal_types_1354": "El modificador de tipo \"readonly\" solo se permite en los tipos literales de matriz y de tupla.", "require_call_may_be_converted_to_an_import_80005": "La llamada a \"require\" puede convertirse en una importación.", "resolution_mode_can_only_be_set_for_type_only_imports_1454": "\"resolution-mode\" solo se puede establecer para importaciones solamente de tipo.", "resolution_mode_is_the_only_valid_key_for_type_import_assertions_1455": "\"resolution-mode\" es la única clave válida para las aserciones de importación de tipos.", "resolution_mode_is_the_only_valid_key_for_type_import_attributes_1463": "“resolution-mode” es la única clave válida para los atributos de importación de tipos.", "resolution_mode_should_be_either_require_or_import_1453": "\"modo de resolución\" debe ser \"requerir\" o \"importar\".", "rootDirs_option_is_set_using_it_to_resolve_relative_module_name_0_6107": "Se ha establecido la opción \"rootDirs\". Se usará para resolver el nombre de módulo relativo \"{0}\".", "super_can_only_be_referenced_in_a_derived_class_2335": "Solo se puede hacer referencia a \"super\" en una clase derivada.", "super_can_only_be_referenced_in_members_of_derived_classes_or_object_literal_expressions_2660": "Solo se puede hacer referencia a 'super' en miembros de clases derivadas o expresiones de literal de objeto.", "super_cannot_be_referenced_in_a_computed_property_name_2466": "No se puede hacer referencia a \"super\" en un nombre de propiedad calculada.", "super_cannot_be_referenced_in_constructor_arguments_2336": "No se puede hacer referencia a \"super\" en argumentos de constructor.", "super_is_only_allowed_in_members_of_object_literal_expressions_when_option_target_is_ES2015_or_highe_2659": "'super' se permite únicamente en miembros de expresiones de literal de objeto cuando la opción 'target' es 'ES2015' o superior.", "super_may_not_use_type_arguments_2754": "\"super\" no puede usar argumentos de tipo.", "super_must_be_called_before_accessing_a_property_of_super_in_the_constructor_of_a_derived_class_17011": "<PERSON><PERSON> llamarse a \"super\" antes de acceder a una propiedad de \"super\" en el constructor de una clase derivada.", "super_must_be_called_before_accessing_this_in_the_constructor_of_a_derived_class_17009": "<PERSON><PERSON> llamarse a 'super' antes de acceder a 'this' en el constructor de una clase derivada.", "super_must_be_followed_by_an_argument_list_or_member_access_1034": "\"super\" debe estar seguido de una lista de argumentos o un acceso a miembros.", "super_property_access_is_permitted_only_in_a_constructor_member_function_or_member_accessor_of_a_der_2338": "El acceso a la propiedad \"super\" se permite únicamente en un constructor, una función miembro o un descriptor de acceso de miembro de una clase derivada.", "this_cannot_be_referenced_in_a_computed_property_name_2465": "No se puede hacer referencia a \"this\" en un nombre de propiedad calculada.", "this_cannot_be_referenced_in_a_module_or_namespace_body_2331": "No se puede hace referencia a \"this\" en el cuerpo de un módulo o de un espacio de nombres.", "this_cannot_be_referenced_in_a_static_property_initializer_2334": "No se puede hacer referencia a \"this\" en un inicializador de propiedad estática.", "this_cannot_be_referenced_in_current_location_2332": "No se puede hacer referencia a \"this\" en la ubicación actual.", "this_implicitly_has_type_any_because_it_does_not_have_a_type_annotation_2683": "'this' tiene el tipo implícito 'any' porque no tiene una anotación de tipo.", "true_for_ES2022_and_above_including_ESNext_6930": "\"true\" para ES2022 y versiones posteriores, incluido ESNext.", "true_if_composite_false_otherwise_6909": "\"true\", si \"composite\"; \"false\", en caso contrario", "true_when_moduleResolution_is_node16_nodenext_or_bundler_otherwise_false_6411": "“true” cuando “moduleResolution” es “node16”, “nodenext” o “bundler”; en caso contrario, “false”.", "tsc_Colon_The_TypeScript_Compiler_6922": "tsc: el compilador de TypeScript", "type_Colon_6902": "tipo:", "unique_symbol_types_are_not_allowed_here_1335": "Aquí no se permiten tipos \"unique symbol\".", "unique_symbol_types_are_only_allowed_on_variables_in_a_variable_statement_1334": "Los tipos \"unique symbol\" se permiten solo en variables en una instrucción de variable.", "unique_symbol_types_may_not_be_used_on_a_variable_declaration_with_a_binding_name_1333": "Los tipos \"unique symbol\" no se pueden utilizar en una declaración de variable con un nombre de enlace.", "use_strict_directive_cannot_be_used_with_non_simple_parameter_list_1347": "La directiva \"use strict\" no se puede usar con una lista de parámetros no simples.", "use_strict_directive_used_here_1349": "La directiva \"use strict\" se ha usado aquí.", "with_statements_are_not_allowed_in_an_async_function_block_1300": "No se permiten instrucciones \"with\" en un bloque de funciones asincrónicas.", "with_statements_are_not_allowed_in_strict_mode_1101": "No se permiten instrucciones \"with\" en modo strict.", "yield_expression_implicitly_results_in_an_any_type_because_its_containing_generator_lacks_a_return_t_7057": "La expresión \"yield\" da como resultado un tipo \"any\" de forma implícita porque el generador que la contiene no tiene una anotación de tipo de valor devuelto.", "yield_expressions_cannot_be_used_in_a_parameter_initializer_2523": "Las expresiones \"yield\" no se pueden usar en un inicializador de parámetros."}