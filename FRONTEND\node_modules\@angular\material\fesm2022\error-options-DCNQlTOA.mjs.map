{"version": 3, "file": "error-options-DCNQlTOA.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/error/error-options.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable} from '@angular/core';\nimport {FormGroupDirective, NgForm, AbstractControl} from '@angular/forms';\n\n/** Error state matcher that matches when a control is invalid and dirty. */\n@Injectable()\nexport class ShowOnDirtyErrorStateMatcher implements ErrorStateMatcher {\n  isErrorState(control: AbstractControl | null, form: FormGroupDirective | NgForm | null): boolean {\n    return !!(control && control.invalid && (control.dirty || (form && form.submitted)));\n  }\n}\n\n/** Provider that defines how form controls behave with regards to displaying error messages. */\n@Injectable({providedIn: 'root'})\nexport class ErrorStateMatcher {\n  isErrorState(control: AbstractControl | null, form: FormGroupDirective | NgForm | null): boolean {\n    return !!(control && control.invalid && (control.touched || (form && form.submitted)));\n  }\n}\n"], "names": [], "mappings": ";;;AAWA;MAEa,4BAA4B,CAAA;IACvC,YAAY,CAAC,OAA+B,EAAE,IAAwC,EAAA;QACpF,OAAO,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;uGAF3E,4BAA4B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAA5B,4BAA4B,EAAA,CAAA;;2FAA5B,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBADxC;;AAOD;MAEa,iBAAiB,CAAA;IAC5B,YAAY,CAAC,OAA+B,EAAE,IAAwC,EAAA;QACpF,OAAO,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,KAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;uGAF7E,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,iBAAiB,cADL,MAAM,EAAA,CAAA;;2FAClB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAD7B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;;;"}