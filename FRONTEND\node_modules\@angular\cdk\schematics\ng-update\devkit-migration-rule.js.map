{"version": 3, "file": "devkit-migration-rule.js", "sourceRoot": "", "sources": ["devkit-migration-rule.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;;;;;;;;;;AA2DH,oEAmHC;AAGD,8CAIC;AAlLD,4DAAwE;AAGxE,gDAA6C;AAI7C,4EAAoG;AAEpG,6DAAsD;AACtD,yDAAuF;AACvF,yDAAuD;AACvD,0EAA6E;AAC7E,sEAAyE;AACzE,0DAA6D;AAC7D,8EAAiF;AACjF,8DAAiE;AACjE,wDAA2D;AAC3D,sEAAyE;AACzE,0DAA6D;AAC7D,8EAAgF;AAChF,8DAAiE;AACjE,4DAA+D;AAC/D,gEAAmE;AACnE,gEAAmE;AAGnE,uDAAuD;AAC1C,QAAA,aAAa,GAAiC;IACzD,iDAA2B;IAC3B,6CAAyB;IACzB,iCAAmB;IACnB,qDAA6B;IAC7B,qCAAqB;IACrB,+BAAkB;IAClB,6CAAyB;IACzB,iCAAmB;IACnB,oDAA4B;IAC5B,qCAAqB;IACrB,mCAAoB;IACpB,uCAAsB;IACtB,uCAAsB;CACvB,CAAC;AAUF;;;GAGG;AACH,SAAgB,4BAA4B,CAC1C,aAA4B,EAC5B,eAA0C,EAC1C,WAAwB,EACxB,qBAAuC;IAEvC,OAAO,CAAO,IAAU,EAAE,OAAyB,EAAE,EAAE;QACrD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC9B,MAAM,SAAS,GAAG,MAAM,IAAA,qDAA4B,EAAC,IAAI,CAAC,CAAC;QAE3D,IAAI,SAAS,KAAK,IAAI,EAAE,CAAC;YACvB,MAAM,CAAC,KAAK,CAAC,8CAA8C,CAAC,CAAC;YAC7D,OAAO;QACT,CAAC;QAED,mFAAmF;QACnF,sFAAsF;QACtF,2FAA2F;QAC3F,MAAM,aAAa,GAAG,IAAI,GAAG,EAAiB,CAAC;QAC/C,MAAM,UAAU,GAAG,IAAI,qCAAgB,CAAC,IAAI,CAAC,CAAC;QAC9C,MAAM,YAAY,GAAG,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC/C,MAAM,UAAU,GAAG,CAAC,GAAG,qBAAa,EAAE,GAAG,eAAe,CAA8B,CAAC;QACvF,IAAI,WAAW,GAAG,KAAK,CAAC;QAExB,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;YACvC,MAAM,OAAO,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,CAAE,CAAC;YACrD,MAAM,iBAAiB,GAAG,IAAA,8CAAqB,EAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAClE,MAAM,gBAAgB,GAAG,IAAA,8CAAqB,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAEhE,IAAI,CAAC,iBAAiB,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC5C,MAAM,CAAC,IAAI,CACT,kCAAkC,WAAW,iEAAiE,CAC/G,CAAC;gBACF,SAAS;YACX,CAAC;YAED,8EAA8E;YAC9E,kFAAkF;YAClF,6CAA6C;YAC7C,6EAA6E;YAC7E,4DAA4D;YAC5D,MAAM,yBAAyB,GAAG,IAAA,sCAAmB,EAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YAE1E,IAAI,iBAAiB,KAAK,IAAI,EAAE,CAAC;gBAC/B,aAAa,CAAC,OAAO,EAAE,WAAW,EAAE,iBAAiB,EAAE,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAC3F,CAAC;YACD,IAAI,gBAAgB,KAAK,IAAI,EAAE,CAAC;gBAC9B,aAAa,CAAC,OAAO,EAAE,WAAW,EAAE,gBAAgB,EAAE,yBAAyB,EAAE,IAAI,CAAC,CAAC;YACzF,CAAC;QACH,CAAC;QAED,IAAI,iBAAiB,GAAG,KAAK,CAAC;QAC9B,uDAAuD;QACvD,gCAAgC;QAChC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YACrB,MAAM,YAAY,GAChB,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,mBAAmB,KAAK,SAAS;gBACzD,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,EAAE,aAAa,EAAE,OAAO,CAAC;gBACrD,CAAC,CAAC,IAAI,CAAC;YACX,IAAI,YAAY,EAAE,CAAC;gBACjB,iBAAiB,GAAG,iBAAiB,IAAI,YAAY,CAAC,iBAAiB,CAAC;YAC1E,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,uEAAuE;QACvE,oEAAoE;QACpE,qEAAqE;QACrE,uBAAuB;QACvB,IAAI,iBAAiB,EAAE,CAAC;YACtB,OAAO,CAAC,OAAO,CAAC,IAAI,8BAAsB,CAAC,EAAC,KAAK,EAAE,KAAK,EAAC,CAAC,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,qBAAqB,EAAE,CAAC;YAC1B,qBAAqB,CAAC,OAAO,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;QAC7D,CAAC;QAED,+DAA+D;QAC/D,SAAS,aAAa,CACpB,OAA0B,EAC1B,WAAmB,EACnB,YAA2B,EAC3B,yBAAmC,EACnC,YAAqB;YAErB,MAAM,OAAO,GAAG,2BAAa,CAAC,yBAAyB,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;YAClF,MAAM,aAAa,GAAkB;gBACnC,YAAY;gBACZ,WAAW;gBACX,OAAO;gBACP,IAAI;aACL,CAAC;YAEF,MAAM,aAAa,GAAG,IAAI,2BAAa,CACrC,aAAa,EACb,OAAO,EACP,UAAU,EACV,aAAa,EACb,OAAO,CAAC,MAAM,CACf,CAAC;YAEF,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,CAClC,UAAU,EACV,aAAa,EACb,WAAW,EACX,yBAAyB,CAC1B,CAAC;YAEF,iFAAiF;YACjF,8EAA8E;YAC9E,iFAAiF;YACjF,UAAU,CAAC,WAAW,EAAE,CAAC;YAEzB,WAAW,GAAG,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC;QAClD,CAAC;IACH,CAAC,CAAA,CAAC;AACJ,CAAC;AAED,oEAAoE;AACpE,SAAgB,iBAAiB,CAC/B,KAA8B;IAE9B,OAAO,kCAAe,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC9C,CAAC"}