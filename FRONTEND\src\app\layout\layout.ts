import { Component } from '@angular/core';
import { Sidebar } from './sidebar/sidebar';
import { Header } from './header/header';
import { RoomManagement } from '../pages/roommanagement/roommanagement';
import { InvoiceComponent } from '../pages/contractsList copy/invoice.component';

@Component({
  selector: 'app-layout',
  imports: [Sidebar, Header, RoomManagement, InvoiceComponent],
  templateUrl: './layout.html',
  styleUrls: ['./layout.css'],
})
export class Layout {
  isCollapsed = false;

  toggleSidebar() {
    this.isCollapsed = !this.isCollapsed;
  }
}
