{"version": 3, "file": "card.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/card/card.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/card/card.html", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/card/card-title-group.html", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/card/card-header.html", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/card/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  Directive,\n  InjectionToken,\n  Input,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\n\nexport type MatCardAppearance = 'outlined' | 'raised' | 'filled';\n\n/** Object that can be used to configure the default options for the card module. */\nexport interface MatCardConfig {\n  /** Default appearance for cards. */\n  appearance?: MatCardAppearance;\n}\n\n/** Injection token that can be used to provide the default options the card module. */\nexport const MAT_CARD_CONFIG = new InjectionToken<MatCardConfig>('MAT_CARD_CONFIG');\n\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\n@Component({\n  selector: 'mat-card',\n  templateUrl: 'card.html',\n  styleUrl: 'card.css',\n  host: {\n    'class': 'mat-mdc-card mdc-card',\n    '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n    '[class.mdc-card--outlined]': 'appearance === \"outlined\"',\n    '[class.mat-mdc-card-filled]': 'appearance === \"filled\"',\n    '[class.mdc-card--filled]': 'appearance === \"filled\"',\n  },\n  exportAs: 'matCard',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatCard {\n  @Input() appearance: MatCardAppearance;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const config = inject<MatCardConfig>(MAT_CARD_CONFIG, {optional: true});\n    this.appearance = config?.appearance || 'raised';\n  }\n}\n\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n  host: {'class': 'mat-mdc-card-title'},\n})\nexport class MatCardTitle {}\n\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\n@Component({\n  selector: 'mat-card-title-group',\n  templateUrl: 'card-title-group.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {'class': 'mat-mdc-card-title-group'},\n})\nexport class MatCardTitleGroup {}\n\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: 'mat-card-content',\n  host: {'class': 'mat-mdc-card-content'},\n})\nexport class MatCardContent {}\n\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n  host: {'class': 'mat-mdc-card-subtitle'},\n})\nexport class MatCardSubtitle {}\n\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: 'mat-card-actions',\n  exportAs: 'matCardActions',\n  host: {\n    'class': 'mat-mdc-card-actions mdc-card__actions',\n    '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"',\n  },\n})\nexport class MatCardActions {\n  // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n  // as to not conflict with the native `align` attribute.\n\n  /** Position of the actions inside the card. */\n  @Input() align: 'start' | 'end' = 'start';\n\n  // TODO(jelbourn): support `.mdc-card__actions--full-bleed`.\n\n  // TODO(jelbourn): support  `.mdc-card__action-buttons` and `.mdc-card__action-icons`.\n\n  // TODO(jelbourn): figure out how to use `.mdc-card__action`, `.mdc-card__action--button`, and\n  // `mdc-card__action--icon`. They're used primarily for positioning, which we might be able to\n  // do implicitly.\n}\n\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\n@Component({\n  selector: 'mat-card-header',\n  templateUrl: 'card-header.html',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {'class': 'mat-mdc-card-header'},\n})\nexport class MatCardHeader {}\n\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: 'mat-card-footer',\n  host: {'class': 'mat-mdc-card-footer'},\n})\nexport class MatCardFooter {}\n\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n\n// TODO(jelbourn): support `.mdc-card__media-content`.\n\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: '[mat-card-image], [matCardImage]',\n  host: {'class': 'mat-mdc-card-image mdc-card__media'},\n})\nexport class MatCardImage {\n  // TODO(jelbourn): support `.mdc-card__media--square` and `.mdc-card__media--16-9`.\n}\n\n/** Same as `MatCardImage`, but small. */\n@Directive({\n  selector: '[mat-card-sm-image], [matCardImageSmall]',\n  host: {'class': 'mat-mdc-card-sm-image mdc-card__media'},\n})\nexport class MatCardSmImage {}\n\n/** Same as `MatCardImage`, but medium. */\n@Directive({\n  selector: '[mat-card-md-image], [matCardImageMedium]',\n  host: {'class': 'mat-mdc-card-md-image mdc-card__media'},\n})\nexport class MatCardMdImage {}\n\n/** Same as `MatCardImage`, but large. */\n@Directive({\n  selector: '[mat-card-lg-image], [matCardImageLarge]',\n  host: {'class': 'mat-mdc-card-lg-image mdc-card__media'},\n})\nexport class MatCardLgImage {}\n\n/** Same as `MatCardImage`, but extra-large. */\n@Directive({\n  selector: '[mat-card-xl-image], [matCardImageXLarge]',\n  host: {'class': 'mat-mdc-card-xl-image mdc-card__media'},\n})\nexport class MatCardXlImage {}\n\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\n@Directive({\n  selector: '[mat-card-avatar], [matCardAvatar]',\n  host: {'class': 'mat-mdc-card-avatar'},\n})\nexport class MatCardAvatar {}\n", "<ng-content></ng-content>\n", "<div>\n  <ng-content\n      select=\"mat-card-title, mat-card-subtitle,\n      [mat-card-title], [mat-card-subtitle],\n      [matCardTitle], [matCardSubtitle]\"></ng-content>\n</div>\n<ng-content select=\"[mat-card-image], [matCardImage],\n                    [mat-card-sm-image], [matCardImageSmall],\n                    [mat-card-md-image], [matCardImageMedium],\n                    [mat-card-lg-image], [matCardImageLarge],\n                    [mat-card-xl-image], [matCardImageXLarge]\"></ng-content>\n<ng-content></ng-content>\n", "<ng-content select=\"[mat-card-avatar], [matCardAvatar]\"></ng-content>\n<div class=\"mat-mdc-card-header-text\">\n  <ng-content\n      select=\"mat-card-title, mat-card-subtitle,\n      [mat-card-title], [mat-card-subtitle],\n      [matCardTitle], [matCardSubtitle]\"></ng-content>\n</div>\n<ng-content></ng-content>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {\n  Mat<PERSON>ard,\n  MatCardActions,\n  MatCardAvatar,\n  MatCardContent,\n  MatCardFooter,\n  MatCardHeader,\n  MatCardImage,\n  MatCardLgImage,\n  MatCardMdImage,\n  MatCardSmImage,\n  MatCardSubtitle,\n  MatCardTitle,\n  MatCardTitleGroup,\n  MatCardXlImage,\n} from './card';\n\nconst CARD_DIRECTIVES = [\n  MatCard,\n  MatCardActions,\n  MatCardAvatar,\n  MatCardContent,\n  MatCardFooter,\n  MatCardHeader,\n  MatCardImage,\n  MatCardLgImage,\n  MatCardMdImage,\n  MatCardSmImage,\n  MatCardSubtitle,\n  MatCardTitle,\n  MatCardTitleGroup,\n  MatCardXlImage,\n];\n\n@NgModule({\n  imports: [MatCommonModule, ...CARD_DIRECTIVES],\n  exports: [CARD_DIRECTIVES, MatCommonModule],\n})\nexport class MatCardModule {}\n"], "names": [], "mappings": ";;;;;;AA0BA;MACa,eAAe,GAAG,IAAI,cAAc,CAAgB,iBAAiB;AAElF;;;;;AAKG;MAgBU,OAAO,CAAA;AACT,IAAA,UAAU;AAInB,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,MAAM,GAAG,MAAM,CAAgB,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QACvE,IAAI,CAAC,UAAU,GAAG,MAAM,EAAE,UAAU,IAAI,QAAQ;;uGAPvC,OAAO,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,OAAO,8aClDpB,6BACA,EAAA,MAAA,EAAA,CAAA,uwIAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDiDa,OAAO,EAAA,UAAA,EAAA,CAAA;kBAfnB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,UAAU,EAGd,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,uBAAuB;AAChC,wBAAA,+BAA+B,EAAE,2BAA2B;AAC5D,wBAAA,4BAA4B,EAAE,2BAA2B;AACzD,wBAAA,6BAA6B,EAAE,yBAAyB;AACxD,wBAAA,0BAA0B,EAAE,yBAAyB;qBACtD,EACS,QAAA,EAAA,SAAS,iBACJ,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,6BAAA,EAAA,MAAA,EAAA,CAAA,uwIAAA,CAAA,EAAA;wDAGtC,UAAU,EAAA,CAAA;sBAAlB;;AAUH;AACA;AAEA;;;;;AAKG;MAKU,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kDAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,oBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAAkD,gDAAA,CAAA;AAC5D,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,oBAAoB,EAAC;AACtC,iBAAA;;AAGD;;;;AAIG;MAQU,iBAAiB,CAAA;uGAAjB,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAjB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,iBAAiB,sIExF9B,0hBAYA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FF4Ea,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAP7B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,sBAAsB,EAEjB,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA,EAAC,OAAO,EAAE,0BAA0B,EAAC,EAAA,QAAA,EAAA,0hBAAA,EAAA;;AAI7C;;;;;;AAMG;MAKU,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,sBAAsB,EAAC;AACxC,iBAAA;;AAGD;;;;;;AAMG;MAKU,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAf,eAAe,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2DAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAA2D,yDAAA,CAAA;AACrE,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,uBAAuB,EAAC;AACzC,iBAAA;;AAGD;;;;;;AAMG;MASU,cAAc,CAAA;;;;IAKhB,KAAK,GAAoB,OAAO;uGAL9B,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,sCAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAR1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kBAAkB;AAC5B,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,wCAAwC;AACjD,wBAAA,wCAAwC,EAAE,iBAAiB;AAC5D,qBAAA;AACF,iBAAA;8BAMU,KAAK,EAAA,CAAA;sBAAb;;AAWH;;;;;;;AAOG;MAQU,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,4HGlK1B,iUAQA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FH0Ja,aAAa,EAAA,UAAA,EAAA,CAAA;kBAPzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,iBAAiB,EAEZ,aAAA,EAAA,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA,EAAC,OAAO,EAAE,qBAAqB,EAAC,EAAA,QAAA,EAAA,iUAAA,EAAA;;AAIxC;;;;;;AAMG;MAKU,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,iBAAiB;AAC3B,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,qBAAqB,EAAC;AACvC,iBAAA;;AAGD;AAEA;AAEA;;;;;;;;AAQG;MAKU,YAAY,CAAA;uGAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,kCAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,oCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBAJxB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,kCAAkC;AAC5C,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,oCAAoC,EAAC;AACtD,iBAAA;;AAKD;MAKa,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,0CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,0CAA0C;AACpD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,uCAAuC,EAAC;AACzD,iBAAA;;AAGD;MAKa,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2CAA2C;AACrD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,uCAAuC,EAAC;AACzD,iBAAA;;AAGD;MAKa,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,0CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,0CAA0C;AACpD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,uCAAuC,EAAC;AACzD,iBAAA;;AAGD;MAKa,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,2CAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,uCAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAJ1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,2CAA2C;AACrD,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,uCAAuC,EAAC;AACzD,iBAAA;;AAGD;;;;;;;;AAQG;MAKU,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,oCAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,oCAAoC;AAC9C,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,qBAAqB,EAAC;AACvC,iBAAA;;;AInND,MAAM,eAAe,GAAG;IACtB,OAAO;IACP,cAAc;IACd,aAAa;IACb,cAAc;IACd,aAAa;IACb,aAAa;IACb,YAAY;IACZ,cAAc;IACd,cAAc;IACd,cAAc;IACd,eAAe;IACf,YAAY;IACZ,iBAAiB;IACjB,cAAc;CACf;MAMY,aAAa,CAAA;uGAAb,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAb,aAAa,EAAA,OAAA,EAAA,CAHd,eAAe,EAjBzB,OAAO;YACP,cAAc;YACd,aAAa;YACb,cAAc;YACd,aAAa;YACb,aAAa;YACb,YAAY;YACZ,cAAc;YACd,cAAc;YACd,cAAc;YACd,eAAe;YACf,YAAY;YACZ,iBAAiB;AACjB,YAAA,cAAc,aAbd,OAAO;YACP,cAAc;YACd,aAAa;YACb,cAAc;YACd,aAAa;YACb,aAAa;YACb,YAAY;YACZ,cAAc;YACd,cAAc;YACd,cAAc;YACd,eAAe;YACf,YAAY;YACZ,iBAAiB;AACjB,YAAA,cAAc,EAKa,eAAe,CAAA,EAAA,CAAA;wGAE/B,aAAa,EAAA,OAAA,EAAA,CAHd,eAAe,EACE,eAAe,CAAA,EAAA,CAAA;;2FAE/B,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,GAAG,eAAe,CAAC;AAC9C,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,CAAC;AAC5C,iBAAA;;;;;"}