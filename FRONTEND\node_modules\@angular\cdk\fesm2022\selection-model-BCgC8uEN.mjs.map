{"version": 3, "file": "selection-model-BCgC8uEN.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/collections/selection-model.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Subject} from 'rxjs';\n\n/**\n * Class to be used to power selecting one or more options from a list.\n */\nexport class SelectionModel<T> {\n  /** Currently-selected values. */\n  private _selection = new Set<T>();\n\n  /** Keeps track of the deselected options that haven't been emitted by the change event. */\n  private _deselectedToEmit: T[] = [];\n\n  /** Keeps track of the selected options that haven't been emitted by the change event. */\n  private _selectedToEmit: T[] = [];\n\n  /** Cache for the array value of the selected items. */\n  private _selected: T[] | null;\n\n  /** Selected values. */\n  get selected(): T[] {\n    if (!this._selected) {\n      this._selected = Array.from(this._selection.values());\n    }\n\n    return this._selected;\n  }\n\n  /** Event emitted when the value has changed. */\n  readonly changed = new Subject<SelectionChange<T>>();\n\n  constructor(\n    private _multiple = false,\n    initiallySelectedValues?: T[],\n    private _emitChanges = true,\n    public compareWith?: (o1: T, o2: T) => boolean,\n  ) {\n    if (initiallySelectedValues && initiallySelectedValues.length) {\n      if (_multiple) {\n        initiallySelectedValues.forEach(value => this._markSelected(value));\n      } else {\n        this._markSelected(initiallySelectedValues[0]);\n      }\n\n      // Clear the array in order to avoid firing the change event for preselected values.\n      this._selectedToEmit.length = 0;\n    }\n  }\n\n  /**\n   * Selects a value or an array of values.\n   * @param values The values to select\n   * @return Whether the selection changed as a result of this call\n   */\n  select(...values: T[]): boolean {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._markSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n\n  /**\n   * Deselects a value or an array of values.\n   * @param values The values to deselect\n   * @return Whether the selection changed as a result of this call\n   */\n  deselect(...values: T[]): boolean {\n    this._verifyValueAssignment(values);\n    values.forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n\n  /**\n   * Sets the selected values\n   * @param values The new selected values\n   * @return Whether the selection changed as a result of this call\n   */\n  setSelection(...values: T[]): boolean {\n    this._verifyValueAssignment(values);\n    const oldValues = this.selected;\n    const newSelectedSet = new Set(values.map(value => this._getConcreteValue(value)));\n    values.forEach(value => this._markSelected(value));\n    oldValues\n      .filter(value => !newSelectedSet.has(this._getConcreteValue(value, newSelectedSet)))\n      .forEach(value => this._unmarkSelected(value));\n    const changed = this._hasQueuedChanges();\n    this._emitChangeEvent();\n    return changed;\n  }\n\n  /**\n   * Toggles a value between selected and deselected.\n   * @param value The value to toggle\n   * @return Whether the selection changed as a result of this call\n   */\n  toggle(value: T): boolean {\n    return this.isSelected(value) ? this.deselect(value) : this.select(value);\n  }\n\n  /**\n   * Clears all of the selected values.\n   * @param flushEvent Whether to flush the changes in an event.\n   *   If false, the changes to the selection will be flushed along with the next event.\n   * @return Whether the selection changed as a result of this call\n   */\n  clear(flushEvent = true): boolean {\n    this._unmarkAll();\n    const changed = this._hasQueuedChanges();\n    if (flushEvent) {\n      this._emitChangeEvent();\n    }\n    return changed;\n  }\n\n  /**\n   * Determines whether a value is selected.\n   */\n  isSelected(value: T): boolean {\n    return this._selection.has(this._getConcreteValue(value));\n  }\n\n  /**\n   * Determines whether the model does not have a value.\n   */\n  isEmpty(): boolean {\n    return this._selection.size === 0;\n  }\n\n  /**\n   * Determines whether the model has a value.\n   */\n  hasValue(): boolean {\n    return !this.isEmpty();\n  }\n\n  /**\n   * Sorts the selected values based on a predicate function.\n   */\n  sort(predicate?: (a: T, b: T) => number): void {\n    if (this._multiple && this.selected) {\n      this._selected!.sort(predicate);\n    }\n  }\n\n  /**\n   * Gets whether multiple values can be selected.\n   */\n  isMultipleSelection() {\n    return this._multiple;\n  }\n\n  /** Emits a change event and clears the records of selected and deselected values. */\n  private _emitChangeEvent() {\n    // Clear the selected values so they can be re-cached.\n    this._selected = null;\n\n    if (this._selectedToEmit.length || this._deselectedToEmit.length) {\n      this.changed.next({\n        source: this,\n        added: this._selectedToEmit,\n        removed: this._deselectedToEmit,\n      });\n\n      this._deselectedToEmit = [];\n      this._selectedToEmit = [];\n    }\n  }\n\n  /** Selects a value. */\n  private _markSelected(value: T) {\n    value = this._getConcreteValue(value);\n    if (!this.isSelected(value)) {\n      if (!this._multiple) {\n        this._unmarkAll();\n      }\n\n      if (!this.isSelected(value)) {\n        this._selection.add(value);\n      }\n\n      if (this._emitChanges) {\n        this._selectedToEmit.push(value);\n      }\n    }\n  }\n\n  /** Deselects a value. */\n  private _unmarkSelected(value: T) {\n    value = this._getConcreteValue(value);\n    if (this.isSelected(value)) {\n      this._selection.delete(value);\n\n      if (this._emitChanges) {\n        this._deselectedToEmit.push(value);\n      }\n    }\n  }\n\n  /** Clears out the selected values. */\n  private _unmarkAll() {\n    if (!this.isEmpty()) {\n      this._selection.forEach(value => this._unmarkSelected(value));\n    }\n  }\n\n  /**\n   * Verifies the value assignment and throws an error if the specified value array is\n   * including multiple values while the selection model is not supporting multiple values.\n   */\n  private _verifyValueAssignment(values: T[]) {\n    if (values.length > 1 && !this._multiple && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw getMultipleValuesInSingleSelectionError();\n    }\n  }\n\n  /** Whether there are queued up change to be emitted. */\n  private _hasQueuedChanges() {\n    return !!(this._deselectedToEmit.length || this._selectedToEmit.length);\n  }\n\n  /** Returns a value that is comparable to inputValue by applying compareWith function, returns the same inputValue otherwise. */\n  private _getConcreteValue(inputValue: T, selection?: Set<T>): T {\n    if (!this.compareWith) {\n      return inputValue;\n    } else {\n      selection = selection ?? this._selection;\n      for (let selectedValue of selection) {\n        if (this.compareWith!(inputValue, selectedValue)) {\n          return selectedValue;\n        }\n      }\n      return inputValue;\n    }\n  }\n}\n\n/**\n * Event emitted when the value of a MatSelectionModel has changed.\n * @docs-private\n */\nexport interface SelectionChange<T> {\n  /** Model that dispatched the event. */\n  source: SelectionModel<T>;\n  /** Options that were added to the model. */\n  added: T[];\n  /** Options that were removed from the model. */\n  removed: T[];\n}\n\n/**\n * Returns an error that reports that multiple values are passed into a selection model\n * with a single value.\n * @docs-private\n */\nexport function getMultipleValuesInSingleSelectionError() {\n  return Error('Cannot pass multiple values into SelectionModel with single-value mode.');\n}\n"], "names": [], "mappings": ";;AAUA;;AAEG;MACU,cAAc,CAAA;AA0Bf,IAAA,SAAA;AAEA,IAAA,YAAA;AACD,IAAA,WAAA;;AA3BD,IAAA,UAAU,GAAG,IAAI,GAAG,EAAK;;IAGzB,iBAAiB,GAAQ,EAAE;;IAG3B,eAAe,GAAQ,EAAE;;AAGzB,IAAA,SAAS;;AAGjB,IAAA,IAAI,QAAQ,GAAA;AACV,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;;QAGvD,OAAO,IAAI,CAAC,SAAS;;;AAId,IAAA,OAAO,GAAG,IAAI,OAAO,EAAsB;IAEpD,WACU,CAAA,SAAA,GAAY,KAAK,EACzB,uBAA6B,EACrB,YAAe,GAAA,IAAI,EACpB,WAAuC,EAAA;QAHtC,IAAS,CAAA,SAAA,GAAT,SAAS;QAET,IAAY,CAAA,YAAA,GAAZ,YAAY;QACb,IAAW,CAAA,WAAA,GAAX,WAAW;AAElB,QAAA,IAAI,uBAAuB,IAAI,uBAAuB,CAAC,MAAM,EAAE;YAC7D,IAAI,SAAS,EAAE;AACb,gBAAA,uBAAuB,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;;iBAC9D;gBACL,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;;;AAIhD,YAAA,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC;;;AAInC;;;;AAIG;IACH,MAAM,CAAC,GAAG,MAAW,EAAA;AACnB,QAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;AACnC,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAClD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE;QACxC,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,OAAO,OAAO;;AAGhB;;;;AAIG;IACH,QAAQ,CAAC,GAAG,MAAW,EAAA;AACrB,QAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;AACnC,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AACpD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE;QACxC,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,OAAO,OAAO;;AAGhB;;;;AAIG;IACH,YAAY,CAAC,GAAG,MAAW,EAAA;AACzB,QAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC;AACnC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ;QAC/B,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC;AAClF,QAAA,MAAM,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAClD;AACG,aAAA,MAAM,CAAC,KAAK,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;AAClF,aAAA,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;AAChD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE;QACxC,IAAI,CAAC,gBAAgB,EAAE;AACvB,QAAA,OAAO,OAAO;;AAGhB;;;;AAIG;AACH,IAAA,MAAM,CAAC,KAAQ,EAAA;QACb,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;;AAG3E;;;;;AAKG;IACH,KAAK,CAAC,UAAU,GAAG,IAAI,EAAA;QACrB,IAAI,CAAC,UAAU,EAAE;AACjB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,EAAE;QACxC,IAAI,UAAU,EAAE;YACd,IAAI,CAAC,gBAAgB,EAAE;;AAEzB,QAAA,OAAO,OAAO;;AAGhB;;AAEG;AACH,IAAA,UAAU,CAAC,KAAQ,EAAA;AACjB,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;;AAG3D;;AAEG;IACH,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC;;AAGnC;;AAEG;IACH,QAAQ,GAAA;AACN,QAAA,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE;;AAGxB;;AAEG;AACH,IAAA,IAAI,CAAC,SAAkC,EAAA;QACrC,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnC,YAAA,IAAI,CAAC,SAAU,CAAC,IAAI,CAAC,SAAS,CAAC;;;AAInC;;AAEG;IACH,mBAAmB,GAAA;QACjB,OAAO,IAAI,CAAC,SAAS;;;IAIf,gBAAgB,GAAA;;AAEtB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AAErB,QAAA,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;AAChE,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;AAChB,gBAAA,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,IAAI,CAAC,eAAe;gBAC3B,OAAO,EAAE,IAAI,CAAC,iBAAiB;AAChC,aAAA,CAAC;AAEF,YAAA,IAAI,CAAC,iBAAiB,GAAG,EAAE;AAC3B,YAAA,IAAI,CAAC,eAAe,GAAG,EAAE;;;;AAKrB,IAAA,aAAa,CAAC,KAAQ,EAAA;AAC5B,QAAA,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAC3B,YAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,CAAC,UAAU,EAAE;;YAGnB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAC3B,gBAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC;;AAG5B,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC;;;;;AAM9B,IAAA,eAAe,CAAC,KAAQ,EAAA;AAC9B,QAAA,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;AACrC,QAAA,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;AAC1B,YAAA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC;AAE7B,YAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,gBAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC;;;;;IAMhC,UAAU,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;;;AAIjE;;;AAGG;AACK,IAAA,sBAAsB,CAAC,MAAW,EAAA;QACxC,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YAC3F,MAAM,uCAAuC,EAAE;;;;IAK3C,iBAAiB,GAAA;AACvB,QAAA,OAAO,CAAC,EAAE,IAAI,CAAC,iBAAiB,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;;;IAIjE,iBAAiB,CAAC,UAAa,EAAE,SAAkB,EAAA;AACzD,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;AACrB,YAAA,OAAO,UAAU;;aACZ;AACL,YAAA,SAAS,GAAG,SAAS,IAAI,IAAI,CAAC,UAAU;AACxC,YAAA,KAAK,IAAI,aAAa,IAAI,SAAS,EAAE;gBACnC,IAAI,IAAI,CAAC,WAAY,CAAC,UAAU,EAAE,aAAa,CAAC,EAAE;AAChD,oBAAA,OAAO,aAAa;;;AAGxB,YAAA,OAAO,UAAU;;;AAGtB;AAeD;;;;AAIG;SACa,uCAAuC,GAAA;AACrD,IAAA,OAAO,KAAK,CAAC,yEAAyE,CAAC;AACzF;;;;"}