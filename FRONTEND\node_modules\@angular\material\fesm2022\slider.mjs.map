{"version": 3, "file": "slider.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/slider/slider-interface.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/slider/slider-thumb.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/slider/slider-thumb.html", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/slider/slider.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/slider/slider.html", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/slider/slider-input.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/slider/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {InjectionToken, ChangeDetectorRef, WritableSignal} from '@angular/core';\nimport {MatRipple, RippleGlobalOptions} from '../core';\n\n/**\n * Thumb types: range slider has two thumbs (START, END) whereas single point\n * slider only has one thumb (END).\n */\nexport enum _MatThumb {\n  START = 1,\n  END = 2,\n}\n\n/** Tick mark enum, for discrete sliders. */\nexport enum _MatTickMark {\n  ACTIVE = 0,\n  INACTIVE = 1,\n}\n\n/**\n * Injection token that can be used for a `MatSlider` to provide itself as a\n * parent to the `MatSliderThumb` and `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nexport const MAT_SLIDER = new InjectionToken<{}>('_MatSlider');\n\n/**\n * Injection token that can be used to query for a `MatSliderThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nexport const MAT_SLIDER_THUMB = new InjectionToken<{}>('_MatSliderThumb');\n\n/**\n * Injection token that can be used to query for a `MatSliderRangeThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nexport const MAT_SLIDER_RANGE_THUMB = new InjectionToken<{}>('_MatSliderRangeThumb');\n\n/**\n * Injection token that can be used to query for a `MatSliderVisualThumb`.\n * Used primarily to avoid circular imports.\n * @docs-private\n */\nexport const MAT_SLIDER_VISUAL_THUMB = new InjectionToken<{}>('_MatSliderVisualThumb');\n\n/** Represents a drag event emitted by the MatSlider component. */\nexport interface MatSliderDragEvent {\n  /** The MatSliderThumb that was interacted with. */\n  source: _MatSliderThumb;\n\n  /** The MatSlider that was interacted with. */\n  parent: _MatSlider;\n\n  /** The current value of the slider. */\n  value: number;\n}\n\n/**\n * A simple change event emitted by the MatSlider component.\n * @deprecated Use event bindings directly on the MatSliderThumbs for `change` and `input` events. See https://v17.material.angular.dev/guide/mdc-migration for information about migrating.\n * @breaking-change 17.0.0\n */\nexport class MatSliderChange {\n  /** The MatSliderThumb that was interacted with. */\n  source: _MatSliderThumb;\n\n  /** The MatSlider that was interacted with. */\n  parent: _MatSlider;\n\n  /** The new value of the source slider. */\n  value: number;\n}\n\nexport interface _MatSlider {\n  /** Whether the given pointer event occurred within the bounds of the slider pointer's DOM Rect. */\n  _isCursorOnSliderThumb(event: PointerEvent, rect: DOMRect): boolean;\n\n  /** Gets the slider thumb input of the given thumb position. */\n  _getInput(thumbPosition: _MatThumb): _MatSliderThumb | _MatSliderRangeThumb | undefined;\n\n  /** Gets the slider thumb HTML input element of the given thumb position. */\n  _getThumb(thumbPosition: _MatThumb): _MatSliderVisualThumb;\n\n  /** The minimum value that the slider can have. */\n  min: number;\n\n  /** The maximum value that the slider can have. */\n  max: number;\n\n  /** The amount that slider values can increment or decrement by. */\n  step: number;\n\n  /** Whether the slider is disabled. */\n  disabled: boolean;\n\n  /** Whether the slider is a range slider. */\n  _isRange: boolean;\n\n  /** Whether the slider is rtl. */\n  _isRtl: boolean;\n\n  /** The stored width of the host element's bounding client rect. */\n  _cachedWidth: number;\n\n  /** The stored width of the host element's bounding client rect. */\n  _cachedLeft: number;\n\n  /**\n   * The padding of the native slider input. This is added in order to make the region where the\n   * thumb ripple extends past the end of the slider track clickable.\n   */\n  _inputPadding: number;\n\n  /** The radius of the visual slider's ripple. */\n  _rippleRadius: number;\n\n  /** The global configuration for `matRipple` instances. */\n  readonly _globalRippleOptions: RippleGlobalOptions | null;\n\n  /** Whether animations have been disabled. */\n  _noopAnimations: boolean;\n\n  /** Whether or not the slider should use animations. */\n  _hasAnimation: boolean;\n\n  /** Triggers UI updates that are needed after a slider input value has changed. */\n  _onValueChange: (source: _MatSliderThumb) => void;\n\n  /** Triggers UI updates that are needed after the slider thumb position has changed. */\n  _onTranslateXChange: (source: _MatSliderThumb) => void;\n\n  /** Updates the stored slider dimensions using the current bounding client rect. */\n  _updateDimensions: () => void;\n\n  /** Updates the scale on the active portion of the track. */\n  _updateTrackUI: (source: _MatSliderThumb) => void;\n\n  /** Used to set the transition duration for thumb and track animations. */\n  _setTransition: (withAnimation: boolean) => void;\n\n  _cdr: ChangeDetectorRef;\n}\n\nexport interface _MatSliderThumb {\n  /** The minimum value that the slider can have. */\n  min: number;\n\n  /** The maximum value that the slider can have. */\n  max: number;\n\n  /** The amount that slider values can increment or decrement by. */\n  step: number;\n\n  /** The current value of this slider input. */\n  value: number;\n\n  /** The current translateX in px of the slider visual thumb. */\n  translateX: number;\n\n  /** Indicates whether this thumb is the start or end thumb. */\n  thumbPosition: _MatThumb;\n\n  /** Similar to percentage but calcualted using translateX relative to the total track width. */\n  fillPercentage: number;\n\n  /** Whether the slider is disabled. */\n  disabled: boolean;\n\n  /** The host native HTML input element. */\n  _hostElement: HTMLInputElement;\n\n  /** Whether the input is currently focused (either by tab or after clicking). */\n  _isFocused: boolean;\n\n  /** The aria-valuetext string representation of the input's value. */\n  _valuetext: WritableSignal<string>;\n\n  /**\n   * Indicates whether UI updates should be skipped.\n   *\n   * This flag is used to avoid flickering\n   * when correcting values on pointer up/down.\n   */\n  _skipUIUpdate: boolean;\n\n  /** Handles the initialization of properties for the slider input. */\n  initProps: () => void;\n\n  /** Handles UI initialization controlled by this slider input. */\n  initUI: () => void;\n\n  /** Calculates the visual thumb's translateX based on the slider input's current value. */\n  _calcTranslateXByValue: () => number;\n\n  /** Updates the visual thumb based on the slider input's current value. */\n  _updateThumbUIByValue: () => void;\n\n  /**\n   * Sets the slider input to disproportionate dimensions to allow for touch\n   * events to be captured on touch devices.\n   */\n  _updateWidthInactive: () => void;\n\n  /**\n   * Used to set the slider width to the correct\n   * dimensions while the user is dragging.\n   */\n  _updateWidthActive: () => void;\n}\n\nexport interface _MatSliderRangeThumb extends _MatSliderThumb {\n  /** Whether this slider corresponds to the input on the left hand side. */\n  _isLeftThumb: boolean;\n\n  /**\n   * Gets the sibling MatSliderRangeThumb.\n   * Returns undefined if it is too early in Angular's life cycle.\n   */\n  getSibling: () => _MatSliderRangeThumb | undefined;\n\n  /** Used to cache whether this slider input corresponds to the visual left thumb. */\n  _setIsLeftThumb: () => void;\n\n  /** Updates the input styles to control whether it is pinned to the start or end of the mat-slider. */\n  _updateStaticStyles: () => void;\n\n  /** Updates the min and max properties of this slider input according to it's sibling. */\n  _updateMinMax: () => void;\n}\n\nexport interface _MatSliderVisualThumb {\n  /** The MatRipple for this slider thumb. */\n  _ripple: MatRipple;\n\n  /** Whether the slider thumb is currently being pressed. */\n  _isActive: boolean;\n\n  /** The host native HTML input element. */\n  _hostElement: HTMLElement;\n\n  /** Shows the value indicator ui. */\n  _showValueIndicator: () => void;\n\n  /** Hides the value indicator ui. */\n  _hideValueIndicator: () => void;\n\n  /** Whether the slider visual thumb is currently showing any ripple. */\n  _isShowingAnyRipple: () => boolean;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ElementRef,\n  Input,\n  NgZone,\n  OnDestroy,\n  Renderer2,\n  ViewChild,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\nimport {MatRipple, RippleAnimationConfig, RippleRef, RippleState} from '../core';\nimport {\n  _MatThumb,\n  _MatSlider,\n  _MatSliderThumb,\n  _MatSliderVisualThumb,\n  MAT_SLIDER,\n  MAT_SLIDER_VISUAL_THUMB,\n} from './slider-interface';\nimport {Platform} from '@angular/cdk/platform';\n\n/**\n * The visual slider thumb.\n *\n * Handles the slider thumb ripple states (hover, focus, and active),\n * and displaying the value tooltip on discrete sliders.\n * @docs-private\n */\n@Component({\n  selector: 'mat-slider-visual-thumb',\n  templateUrl: './slider-thumb.html',\n  styleUrl: 'slider-thumb.css',\n  host: {\n    'class': 'mdc-slider__thumb mat-mdc-slider-visual-thumb',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  providers: [{provide: MAT_SLIDER_VISUAL_THUMB, useExisting: MatSliderVisualThumb}],\n  imports: [MatRipple],\n})\nexport class MatSliderVisualThumb implements _MatSliderVisualThumb, AfterViewInit, OnDestroy {\n  readonly _cdr = inject(ChangeDetectorRef);\n  private readonly _ngZone = inject(NgZone);\n  private _slider = inject<_MatSlider>(MAT_SLIDER);\n  private _renderer = inject(Renderer2);\n  private _listenerCleanups: (() => void)[] | undefined;\n\n  /** Whether the slider displays a numeric value label upon pressing the thumb. */\n  @Input() discrete: boolean;\n\n  /** Indicates which slider thumb this input corresponds to. */\n  @Input() thumbPosition: _MatThumb;\n\n  /** The display value of the slider thumb. */\n  @Input() valueIndicatorText: string;\n\n  /** The MatRipple for this slider thumb. */\n  @ViewChild(MatRipple) readonly _ripple: MatRipple;\n\n  /** The slider thumb knob. */\n  @ViewChild('knob') _knob: ElementRef<HTMLElement>;\n\n  /** The slider thumb value indicator container. */\n  @ViewChild('valueIndicatorContainer')\n  _valueIndicatorContainer: ElementRef<HTMLElement>;\n\n  /** The slider input corresponding to this slider thumb. */\n  private _sliderInput: _MatSliderThumb;\n\n  /** The native html element of the slider input corresponding to this thumb. */\n  private _sliderInputEl: HTMLInputElement | undefined;\n\n  /** The RippleRef for the slider thumbs hover state. */\n  private _hoverRippleRef: RippleRef | undefined;\n\n  /** The RippleRef for the slider thumbs focus state. */\n  private _focusRippleRef: RippleRef | undefined;\n\n  /** The RippleRef for the slider thumbs active state. */\n  private _activeRippleRef: RippleRef | undefined;\n\n  /** Whether the slider thumb is currently being hovered. */\n  private _isHovered: boolean = false;\n\n  /** Whether the slider thumb is currently being pressed. */\n  _isActive = false;\n\n  /** Whether the value indicator tooltip is visible. */\n  _isValueIndicatorVisible: boolean = false;\n\n  /** The host native HTML input element. */\n  _hostElement = inject<ElementRef<HTMLElement>>(ElementRef).nativeElement;\n\n  private _platform = inject(Platform);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngAfterViewInit() {\n    const sliderInput = this._slider._getInput(this.thumbPosition);\n\n    // No-op if the slider isn't configured properly. `MatSlider` will\n    // throw an error instructing the user how to set up the slider.\n    if (!sliderInput) {\n      return;\n    }\n\n    this._ripple.radius = 24;\n    this._sliderInput = sliderInput;\n    this._sliderInputEl = this._sliderInput._hostElement;\n\n    // These listeners don't update any data bindings so we bind them outside\n    // of the NgZone to prevent Angular from needlessly running change detection.\n    this._ngZone.runOutsideAngular(() => {\n      const input = this._sliderInputEl!;\n      const renderer = this._renderer;\n      this._listenerCleanups = [\n        renderer.listen(input, 'pointermove', this._onPointerMove),\n        renderer.listen(input, 'pointerdown', this._onDragStart),\n        renderer.listen(input, 'pointerup', this._onDragEnd),\n        renderer.listen(input, 'pointerleave', this._onMouseLeave),\n        renderer.listen(input, 'focus', this._onFocus),\n        renderer.listen(input, 'blur', this._onBlur),\n      ];\n    });\n  }\n\n  ngOnDestroy() {\n    this._listenerCleanups?.forEach(cleanup => cleanup());\n  }\n\n  private _onPointerMove = (event: PointerEvent): void => {\n    if (this._sliderInput._isFocused) {\n      return;\n    }\n\n    const rect = this._hostElement.getBoundingClientRect();\n    const isHovered = this._slider._isCursorOnSliderThumb(event, rect);\n    this._isHovered = isHovered;\n\n    if (isHovered) {\n      this._showHoverRipple();\n    } else {\n      this._hideRipple(this._hoverRippleRef);\n    }\n  };\n\n  private _onMouseLeave = (): void => {\n    this._isHovered = false;\n    this._hideRipple(this._hoverRippleRef);\n  };\n\n  private _onFocus = (): void => {\n    // We don't want to show the hover ripple on top of the focus ripple.\n    // Happen when the users cursor is over a thumb and then the user tabs to it.\n    this._hideRipple(this._hoverRippleRef);\n    this._showFocusRipple();\n    this._hostElement.classList.add('mdc-slider__thumb--focused');\n  };\n\n  private _onBlur = (): void => {\n    // Happens when the user tabs away while still dragging a thumb.\n    if (!this._isActive) {\n      this._hideRipple(this._focusRippleRef);\n    }\n    // Happens when the user tabs away from a thumb but their cursor is still over it.\n    if (this._isHovered) {\n      this._showHoverRipple();\n    }\n    this._hostElement.classList.remove('mdc-slider__thumb--focused');\n  };\n\n  private _onDragStart = (event: PointerEvent): void => {\n    if (event.button !== 0) {\n      return;\n    }\n    this._isActive = true;\n    this._showActiveRipple();\n  };\n\n  private _onDragEnd = (): void => {\n    this._isActive = false;\n    this._hideRipple(this._activeRippleRef);\n    // Happens when the user starts dragging a thumb, tabs away, and then stops dragging.\n    if (!this._sliderInput._isFocused) {\n      this._hideRipple(this._focusRippleRef);\n    }\n\n    // On Safari we need to immediately re-show the hover ripple because\n    // sliders do not retain focus from pointer events on that platform.\n    if (this._platform.SAFARI) {\n      this._showHoverRipple();\n    }\n  };\n\n  /** Handles displaying the hover ripple. */\n  private _showHoverRipple(): void {\n    if (!this._isShowingRipple(this._hoverRippleRef)) {\n      this._hoverRippleRef = this._showRipple({enterDuration: 0, exitDuration: 0});\n      this._hoverRippleRef?.element.classList.add('mat-mdc-slider-hover-ripple');\n    }\n  }\n\n  /** Handles displaying the focus ripple. */\n  private _showFocusRipple(): void {\n    // Show the focus ripple event if noop animations are enabled.\n    if (!this._isShowingRipple(this._focusRippleRef)) {\n      this._focusRippleRef = this._showRipple({enterDuration: 0, exitDuration: 0}, true);\n      this._focusRippleRef?.element.classList.add('mat-mdc-slider-focus-ripple');\n    }\n  }\n\n  /** Handles displaying the active ripple. */\n  private _showActiveRipple(): void {\n    if (!this._isShowingRipple(this._activeRippleRef)) {\n      this._activeRippleRef = this._showRipple({enterDuration: 225, exitDuration: 400});\n      this._activeRippleRef?.element.classList.add('mat-mdc-slider-active-ripple');\n    }\n  }\n\n  /** Whether the given rippleRef is currently fading in or visible. */\n  private _isShowingRipple(rippleRef?: RippleRef): boolean {\n    return rippleRef?.state === RippleState.FADING_IN || rippleRef?.state === RippleState.VISIBLE;\n  }\n\n  /** Manually launches the slider thumb ripple using the specified ripple animation config. */\n  private _showRipple(\n    animation: RippleAnimationConfig,\n    ignoreGlobalRippleConfig?: boolean,\n  ): RippleRef | undefined {\n    if (this._slider.disabled) {\n      return;\n    }\n    this._showValueIndicator();\n    if (this._slider._isRange) {\n      const sibling = this._slider._getThumb(\n        this.thumbPosition === _MatThumb.START ? _MatThumb.END : _MatThumb.START,\n      );\n      sibling._showValueIndicator();\n    }\n    if (this._slider._globalRippleOptions?.disabled && !ignoreGlobalRippleConfig) {\n      return;\n    }\n    return this._ripple.launch({\n      animation: this._slider._noopAnimations ? {enterDuration: 0, exitDuration: 0} : animation,\n      centered: true,\n      persistent: true,\n    });\n  }\n\n  /**\n   * Fades out the given ripple.\n   * Also hides the value indicator if no ripple is showing.\n   */\n  private _hideRipple(rippleRef?: RippleRef): void {\n    rippleRef?.fadeOut();\n\n    if (this._isShowingAnyRipple()) {\n      return;\n    }\n\n    if (!this._slider._isRange) {\n      this._hideValueIndicator();\n    }\n\n    const sibling = this._getSibling();\n    if (!sibling._isShowingAnyRipple()) {\n      this._hideValueIndicator();\n      sibling._hideValueIndicator();\n    }\n  }\n\n  /** Shows the value indicator ui. */\n  _showValueIndicator(): void {\n    this._hostElement.classList.add('mdc-slider__thumb--with-indicator');\n  }\n\n  /** Hides the value indicator ui. */\n  _hideValueIndicator(): void {\n    this._hostElement.classList.remove('mdc-slider__thumb--with-indicator');\n  }\n\n  _getSibling(): _MatSliderVisualThumb {\n    return this._slider._getThumb(\n      this.thumbPosition === _MatThumb.START ? _MatThumb.END : _MatThumb.START,\n    );\n  }\n\n  /** Gets the value indicator container's native HTML element. */\n  _getValueIndicatorContainer(): HTMLElement | undefined {\n    return this._valueIndicatorContainer?.nativeElement;\n  }\n\n  /** Gets the native HTML element of the slider thumb knob. */\n  _getKnob(): HTMLElement {\n    return this._knob.nativeElement;\n  }\n\n  _isShowingAnyRipple(): boolean {\n    return (\n      this._isShowingRipple(this._hoverRippleRef) ||\n      this._isShowingRipple(this._focusRippleRef) ||\n      this._isShowingRipple(this._activeRippleRef)\n    );\n  }\n}\n", "@if (discrete) {\n  <div class=\"mdc-slider__value-indicator-container\" #valueIndicatorContainer>\n    <div class=\"mdc-slider__value-indicator\">\n      <span class=\"mdc-slider__value-indicator-text\">{{valueIndicatorText}}</span>\n    </div>\n  </div>\n}\n<div class=\"mdc-slider__thumb-knob\" #knob></div>\n<div matRipple class=\"mat-focus-indicator\" [matRippleDisabled]=\"true\"></div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Directionality} from '@angular/cdk/bidi';\nimport {Platform} from '@angular/cdk/platform';\nimport {\n  AfterViewInit,\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  ContentChild,\n  ContentChildren,\n  ElementRef,\n  inject,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnDestroy,\n  QueryList,\n  ViewChild,\n  ViewChildren,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {\n  _animationsDisabled,\n  _StructuralStylesLoader,\n  MAT_RIPPLE_GLOBAL_OPTIONS,\n  RippleGlobalOptions,\n  ThemePalette,\n} from '../core';\nimport {Subscription} from 'rxjs';\nimport {\n  _MatThumb,\n  _MatTickMark,\n  _MatSlider,\n  _MatSliderRangeThumb,\n  _MatSliderThumb,\n  _MatSliderVisualThumb,\n  MAT_SLIDER_RANGE_THUMB,\n  MAT_SLIDER_THUMB,\n  MAT_SLIDER,\n  MAT_SLIDER_VISUAL_THUMB,\n} from './slider-interface';\nimport {MatSliderVisualThumb} from './slider-thumb';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\n\n// TODO(wagnermaciel): maybe handle the following edge case:\n// 1. start dragging discrete slider\n// 2. tab to disable checkbox\n// 3. without ending drag, disable the slider\n\n/**\n * Allows users to select from a range of values by moving the slider thumb. It is similar in\n * behavior to the native `<input type=\"range\">` element.\n */\n@Component({\n  selector: 'mat-slider',\n  templateUrl: 'slider.html',\n  styleUrl: 'slider.css',\n  host: {\n    'class': 'mat-mdc-slider mdc-slider',\n    '[class]': '\"mat-\" + (color || \"primary\")',\n    '[class.mdc-slider--range]': '_isRange',\n    '[class.mdc-slider--disabled]': 'disabled',\n    '[class.mdc-slider--discrete]': 'discrete',\n    '[class.mdc-slider--tick-marks]': 'showTickMarks',\n    '[class._mat-animation-noopable]': '_noopAnimations',\n  },\n  exportAs: 'matSlider',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  providers: [{provide: MAT_SLIDER, useExisting: MatSlider}],\n  imports: [MatSliderVisualThumb],\n})\nexport class MatSlider implements AfterViewInit, OnDestroy, _MatSlider {\n  readonly _ngZone = inject(NgZone);\n  readonly _cdr = inject(ChangeDetectorRef);\n  readonly _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  readonly _dir = inject(Directionality, {optional: true});\n  readonly _globalRippleOptions = inject<RippleGlobalOptions>(MAT_RIPPLE_GLOBAL_OPTIONS, {\n    optional: true,\n  });\n\n  /** The active portion of the slider track. */\n  @ViewChild('trackActive') _trackActive: ElementRef<HTMLElement>;\n\n  /** The slider thumb(s). */\n  @ViewChildren(MAT_SLIDER_VISUAL_THUMB) _thumbs: QueryList<_MatSliderVisualThumb>;\n\n  /** The sliders hidden range input(s). */\n  @ContentChild(MAT_SLIDER_THUMB) _input: _MatSliderThumb;\n\n  /** The sliders hidden range input(s). */\n  @ContentChildren(MAT_SLIDER_RANGE_THUMB, {descendants: false})\n  _inputs: QueryList<_MatSliderRangeThumb>;\n\n  /** Whether the slider is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(v: boolean) {\n    this._disabled = v;\n    const endInput = this._getInput(_MatThumb.END);\n    const startInput = this._getInput(_MatThumb.START);\n\n    if (endInput) {\n      endInput.disabled = this._disabled;\n    }\n    if (startInput) {\n      startInput.disabled = this._disabled;\n    }\n  }\n  private _disabled: boolean = false;\n\n  /** Whether the slider displays a numeric value label upon pressing the thumb. */\n  @Input({transform: booleanAttribute})\n  get discrete(): boolean {\n    return this._discrete;\n  }\n  set discrete(v: boolean) {\n    this._discrete = v;\n    this._updateValueIndicatorUIs();\n  }\n  private _discrete: boolean = false;\n\n  /** Whether the slider displays tick marks along the slider track. */\n  @Input({transform: booleanAttribute})\n  showTickMarks: boolean = false;\n\n  /** The minimum value that the slider can have. */\n  @Input({transform: numberAttribute})\n  get min(): number {\n    return this._min;\n  }\n  set min(v: number) {\n    const min = v === undefined || v === null || isNaN(v) ? this._min : v;\n    if (this._min !== min) {\n      this._updateMin(min);\n    }\n  }\n  private _min: number = 0;\n\n  /**\n   * Theme color of the slider. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/slider/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input()\n  color: ThemePalette;\n\n  /** Whether ripples are disabled in the slider. */\n  @Input({transform: booleanAttribute})\n  disableRipple: boolean = false;\n\n  private _updateMin(min: number): void {\n    const prevMin = this._min;\n    this._min = min;\n    this._isRange ? this._updateMinRange({old: prevMin, new: min}) : this._updateMinNonRange(min);\n    this._onMinMaxOrStepChange();\n  }\n\n  private _updateMinRange(min: {old: number; new: number}): void {\n    const endInput = this._getInput(_MatThumb.END) as _MatSliderRangeThumb;\n    const startInput = this._getInput(_MatThumb.START) as _MatSliderRangeThumb;\n\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n\n    startInput.min = min.new;\n    endInput.min = Math.max(min.new, startInput.value);\n    startInput.max = Math.min(endInput.max, endInput.value);\n\n    startInput._updateWidthInactive();\n    endInput._updateWidthInactive();\n\n    min.new < min.old\n      ? this._onTranslateXChangeBySideEffect(endInput, startInput)\n      : this._onTranslateXChangeBySideEffect(startInput, endInput);\n\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n\n  private _updateMinNonRange(min: number): void {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n\n      input.min = min;\n      input._updateThumbUIByValue();\n      this._updateTrackUI(input);\n\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n\n  /** The maximum value that the slider can have. */\n  @Input({transform: numberAttribute})\n  get max(): number {\n    return this._max;\n  }\n  set max(v: number) {\n    const max = v === undefined || v === null || isNaN(v) ? this._max : v;\n    if (this._max !== max) {\n      this._updateMax(max);\n    }\n  }\n  private _max: number = 100;\n\n  private _updateMax(max: number): void {\n    const prevMax = this._max;\n    this._max = max;\n    this._isRange ? this._updateMaxRange({old: prevMax, new: max}) : this._updateMaxNonRange(max);\n    this._onMinMaxOrStepChange();\n  }\n\n  private _updateMaxRange(max: {old: number; new: number}): void {\n    const endInput = this._getInput(_MatThumb.END) as _MatSliderRangeThumb;\n    const startInput = this._getInput(_MatThumb.START) as _MatSliderRangeThumb;\n\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n\n    endInput.max = max.new;\n    startInput.max = Math.min(max.new, endInput.value);\n    endInput.min = startInput.value;\n\n    endInput._updateWidthInactive();\n    startInput._updateWidthInactive();\n\n    max.new > max.old\n      ? this._onTranslateXChangeBySideEffect(startInput, endInput)\n      : this._onTranslateXChangeBySideEffect(endInput, startInput);\n\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n\n  private _updateMaxNonRange(max: number): void {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n\n      input.max = max;\n      input._updateThumbUIByValue();\n      this._updateTrackUI(input);\n\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n\n  /** The values at which the thumb will snap. */\n  @Input({transform: numberAttribute})\n  get step(): number {\n    return this._step;\n  }\n  set step(v: number) {\n    const step = isNaN(v) ? this._step : v;\n    if (this._step !== step) {\n      this._updateStep(step);\n    }\n  }\n  private _step: number = 1;\n\n  private _updateStep(step: number): void {\n    this._step = step;\n    this._isRange ? this._updateStepRange() : this._updateStepNonRange();\n    this._onMinMaxOrStepChange();\n  }\n\n  private _updateStepRange(): void {\n    const endInput = this._getInput(_MatThumb.END) as _MatSliderRangeThumb;\n    const startInput = this._getInput(_MatThumb.START) as _MatSliderRangeThumb;\n\n    const oldEndValue = endInput.value;\n    const oldStartValue = startInput.value;\n\n    const prevStartValue = startInput.value;\n\n    endInput.min = this._min;\n    startInput.max = this._max;\n\n    endInput.step = this._step;\n    startInput.step = this._step;\n\n    if (this._platform.SAFARI) {\n      endInput.value = endInput.value;\n      startInput.value = startInput.value;\n    }\n\n    endInput.min = Math.max(this._min, startInput.value);\n    startInput.max = Math.min(this._max, endInput.value);\n\n    startInput._updateWidthInactive();\n    endInput._updateWidthInactive();\n\n    endInput.value < prevStartValue\n      ? this._onTranslateXChangeBySideEffect(startInput, endInput)\n      : this._onTranslateXChangeBySideEffect(endInput, startInput);\n\n    if (oldEndValue !== endInput.value) {\n      this._onValueChange(endInput);\n    }\n\n    if (oldStartValue !== startInput.value) {\n      this._onValueChange(startInput);\n    }\n  }\n\n  private _updateStepNonRange(): void {\n    const input = this._getInput(_MatThumb.END);\n    if (input) {\n      const oldValue = input.value;\n\n      input.step = this._step;\n      if (this._platform.SAFARI) {\n        input.value = input.value;\n      }\n\n      input._updateThumbUIByValue();\n\n      if (oldValue !== input.value) {\n        this._onValueChange(input);\n      }\n    }\n  }\n\n  /**\n   * Function that will be used to format the value before it is displayed\n   * in the thumb label. Can be used to format very large number in order\n   * for them to fit into the slider thumb.\n   */\n  @Input() displayWith: (value: number) => string = (value: number) => `${value}`;\n\n  /** Used to keep track of & render the active & inactive tick marks on the slider track. */\n  _tickMarks: _MatTickMark[];\n\n  /** Whether animations have been disabled. */\n  _noopAnimations = _animationsDisabled();\n\n  /** Subscription to changes to the directionality (LTR / RTL) context for the application. */\n  private _dirChangeSubscription: Subscription;\n\n  /** Observer used to monitor size changes in the slider. */\n  private _resizeObserver: ResizeObserver | null;\n\n  // Stored dimensions to avoid calling getBoundingClientRect redundantly.\n\n  _cachedWidth: number;\n  _cachedLeft: number;\n\n  _rippleRadius: number = 24;\n\n  // The value indicator tooltip text for the visual slider thumb(s).\n\n  /** @docs-private */\n  protected startValueIndicatorText: string = '';\n\n  /** @docs-private */\n  protected endValueIndicatorText: string = '';\n\n  // Used to control the translateX of the visual slider thumb(s).\n\n  _endThumbTransform: string;\n  _startThumbTransform: string;\n\n  _isRange: boolean = false;\n\n  /** Whether the slider is rtl. */\n  _isRtl: boolean = false;\n\n  private _hasViewInitialized: boolean = false;\n\n  /**\n   * The width of the tick mark track.\n   * The tick mark track width is different from full track width\n   */\n  _tickMarkTrackWidth: number = 0;\n\n  _hasAnimation: boolean = false;\n\n  private _resizeTimer: null | ReturnType<typeof setTimeout> = null;\n\n  private _platform = inject(Platform);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n\n    if (this._dir) {\n      this._dirChangeSubscription = this._dir.change.subscribe(() => this._onDirChange());\n      this._isRtl = this._dir.value === 'rtl';\n    }\n  }\n\n  /** The radius of the native slider's knob. AFAIK there is no way to avoid hardcoding this. */\n  _knobRadius: number = 8;\n\n  _inputPadding: number;\n\n  ngAfterViewInit(): void {\n    if (this._platform.isBrowser) {\n      this._updateDimensions();\n    }\n\n    const eInput = this._getInput(_MatThumb.END);\n    const sInput = this._getInput(_MatThumb.START);\n    this._isRange = !!eInput && !!sInput;\n    this._cdr.detectChanges();\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      _validateInputs(\n        this._isRange,\n        this._getInput(_MatThumb.END),\n        this._getInput(_MatThumb.START),\n      );\n    }\n\n    const thumb = this._getThumb(_MatThumb.END);\n    this._rippleRadius = thumb._ripple.radius;\n    this._inputPadding = this._rippleRadius - this._knobRadius;\n\n    this._isRange\n      ? this._initUIRange(eInput as _MatSliderRangeThumb, sInput as _MatSliderRangeThumb)\n      : this._initUINonRange(eInput!);\n\n    this._updateTrackUI(eInput!);\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n\n    this._observeHostResize();\n    this._cdr.detectChanges();\n  }\n\n  private _initUINonRange(eInput: _MatSliderThumb): void {\n    eInput.initProps();\n    eInput.initUI();\n\n    this._updateValueIndicatorUI(eInput);\n\n    this._hasViewInitialized = true;\n    eInput._updateThumbUIByValue();\n  }\n\n  private _initUIRange(eInput: _MatSliderRangeThumb, sInput: _MatSliderRangeThumb): void {\n    eInput.initProps();\n    eInput.initUI();\n\n    sInput.initProps();\n    sInput.initUI();\n\n    eInput._updateMinMax();\n    sInput._updateMinMax();\n\n    eInput._updateStaticStyles();\n    sInput._updateStaticStyles();\n\n    this._updateValueIndicatorUIs();\n\n    this._hasViewInitialized = true;\n\n    eInput._updateThumbUIByValue();\n    sInput._updateThumbUIByValue();\n  }\n\n  ngOnDestroy(): void {\n    this._dirChangeSubscription.unsubscribe();\n    this._resizeObserver?.disconnect();\n    this._resizeObserver = null;\n  }\n\n  /** Handles updating the slider ui after a dir change. */\n  private _onDirChange(): void {\n    this._isRtl = this._dir?.value === 'rtl';\n    this._isRange ? this._onDirChangeRange() : this._onDirChangeNonRange();\n    this._updateTickMarkUI();\n  }\n\n  private _onDirChangeRange(): void {\n    const endInput = this._getInput(_MatThumb.END) as _MatSliderRangeThumb;\n    const startInput = this._getInput(_MatThumb.START) as _MatSliderRangeThumb;\n\n    endInput._setIsLeftThumb();\n    startInput._setIsLeftThumb();\n\n    endInput.translateX = endInput._calcTranslateXByValue();\n    startInput.translateX = startInput._calcTranslateXByValue();\n\n    endInput._updateStaticStyles();\n    startInput._updateStaticStyles();\n\n    endInput._updateWidthInactive();\n    startInput._updateWidthInactive();\n\n    endInput._updateThumbUIByValue();\n    startInput._updateThumbUIByValue();\n  }\n\n  private _onDirChangeNonRange(): void {\n    const input = this._getInput(_MatThumb.END)!;\n    input._updateThumbUIByValue();\n  }\n\n  /** Starts observing and updating the slider if the host changes its size. */\n  private _observeHostResize() {\n    if (typeof ResizeObserver === 'undefined' || !ResizeObserver) {\n      return;\n    }\n\n    this._ngZone.runOutsideAngular(() => {\n      this._resizeObserver = new ResizeObserver(() => {\n        if (this._isActive()) {\n          return;\n        }\n        if (this._resizeTimer) {\n          clearTimeout(this._resizeTimer);\n        }\n        this._onResize();\n      });\n      this._resizeObserver.observe(this._elementRef.nativeElement);\n    });\n  }\n\n  /** Whether any of the thumbs are currently active. */\n  private _isActive(): boolean {\n    return this._getThumb(_MatThumb.START)._isActive || this._getThumb(_MatThumb.END)._isActive;\n  }\n\n  private _getValue(thumbPosition: _MatThumb = _MatThumb.END): number {\n    const input = this._getInput(thumbPosition);\n    if (!input) {\n      return this.min;\n    }\n    return input.value;\n  }\n\n  private _skipUpdate(): boolean {\n    return !!(\n      this._getInput(_MatThumb.START)?._skipUIUpdate || this._getInput(_MatThumb.END)?._skipUIUpdate\n    );\n  }\n\n  /** Stores the slider dimensions. */\n  _updateDimensions(): void {\n    this._cachedWidth = this._elementRef.nativeElement.offsetWidth;\n    this._cachedLeft = this._elementRef.nativeElement.getBoundingClientRect().left;\n  }\n\n  /** Sets the styles for the active portion of the track. */\n  _setTrackActiveStyles(styles: {\n    left: string;\n    right: string;\n    transform: string;\n    transformOrigin: string;\n  }): void {\n    const trackStyle = this._trackActive.nativeElement.style;\n\n    trackStyle.left = styles.left;\n    trackStyle.right = styles.right;\n    trackStyle.transformOrigin = styles.transformOrigin;\n    trackStyle.transform = styles.transform;\n  }\n\n  /** Returns the translateX positioning for a tick mark based on it's index. */\n  _calcTickMarkTransform(index: number): string {\n    // TODO(wagnermaciel): See if we can avoid doing this and just using flex to position these.\n    const offset = index * (this._tickMarkTrackWidth / (this._tickMarks.length - 1));\n    const translateX = this._isRtl ? this._cachedWidth - 6 - offset : offset;\n    return `translateX(${translateX}px`;\n  }\n\n  // Handlers for updating the slider ui.\n\n  _onTranslateXChange(source: _MatSliderThumb): void {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n\n    this._updateThumbUI(source);\n    this._updateTrackUI(source);\n    this._updateOverlappingThumbUI(source as _MatSliderRangeThumb);\n  }\n\n  _onTranslateXChangeBySideEffect(\n    input1: _MatSliderRangeThumb,\n    input2: _MatSliderRangeThumb,\n  ): void {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n\n    input1._updateThumbUIByValue();\n    input2._updateThumbUIByValue();\n  }\n\n  _onValueChange(source: _MatSliderThumb): void {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n\n    this._updateValueIndicatorUI(source);\n    this._updateTickMarkUI();\n    this._cdr.detectChanges();\n  }\n\n  _onMinMaxOrStepChange(): void {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._cdr.markForCheck();\n  }\n\n  _onResize(): void {\n    if (!this._hasViewInitialized) {\n      return;\n    }\n\n    this._updateDimensions();\n    if (this._isRange) {\n      const eInput = this._getInput(_MatThumb.END) as _MatSliderRangeThumb;\n      const sInput = this._getInput(_MatThumb.START) as _MatSliderRangeThumb;\n\n      eInput._updateThumbUIByValue();\n      sInput._updateThumbUIByValue();\n\n      eInput._updateStaticStyles();\n      sInput._updateStaticStyles();\n\n      eInput._updateMinMax();\n      sInput._updateMinMax();\n\n      eInput._updateWidthInactive();\n      sInput._updateWidthInactive();\n    } else {\n      const eInput = this._getInput(_MatThumb.END);\n      if (eInput) {\n        eInput._updateThumbUIByValue();\n      }\n    }\n\n    this._updateTickMarkUI();\n    this._updateTickMarkTrackUI();\n    this._cdr.detectChanges();\n  }\n\n  /** Whether or not the slider thumbs overlap. */\n  private _thumbsOverlap: boolean = false;\n\n  /** Returns true if the slider knobs are overlapping one another. */\n  private _areThumbsOverlapping(): boolean {\n    const startInput = this._getInput(_MatThumb.START);\n    const endInput = this._getInput(_MatThumb.END);\n    if (!startInput || !endInput) {\n      return false;\n    }\n    return endInput.translateX - startInput.translateX < 20;\n  }\n\n  /**\n   * Updates the class names of overlapping slider thumbs so\n   * that the current active thumb is styled to be on \"top\".\n   */\n  private _updateOverlappingThumbClassNames(source: _MatSliderRangeThumb): void {\n    const sibling = source.getSibling()!;\n    const sourceThumb = this._getThumb(source.thumbPosition);\n    const siblingThumb = this._getThumb(sibling.thumbPosition);\n    siblingThumb._hostElement.classList.remove('mdc-slider__thumb--top');\n    sourceThumb._hostElement.classList.toggle('mdc-slider__thumb--top', this._thumbsOverlap);\n  }\n\n  /** Updates the UI of slider thumbs when they begin or stop overlapping. */\n  private _updateOverlappingThumbUI(source: _MatSliderRangeThumb): void {\n    if (!this._isRange || this._skipUpdate()) {\n      return;\n    }\n    if (this._thumbsOverlap !== this._areThumbsOverlapping()) {\n      this._thumbsOverlap = !this._thumbsOverlap;\n      this._updateOverlappingThumbClassNames(source);\n    }\n  }\n\n  // _MatThumb styles update conditions\n  //\n  // 1. TranslateX, resize, or dir change\n  //    - Reason: The thumb styles need to be updated according to the new translateX.\n  // 2. Min, max, or step\n  //    - Reason: The value may have silently changed.\n\n  /** Updates the translateX of the given thumb. */\n  _updateThumbUI(source: _MatSliderThumb) {\n    if (this._skipUpdate()) {\n      return;\n    }\n    const thumb = this._getThumb(\n      source.thumbPosition === _MatThumb.END ? _MatThumb.END : _MatThumb.START,\n    )!;\n    thumb._hostElement.style.transform = `translateX(${source.translateX}px)`;\n  }\n\n  // Value indicator text update conditions\n  //\n  // 1. Value\n  //    - Reason: The value displayed needs to be updated.\n  // 2. Min, max, or step\n  //    - Reason: The value may have silently changed.\n\n  /** Updates the value indicator tooltip ui for the given thumb. */\n  _updateValueIndicatorUI(source: _MatSliderThumb): void {\n    if (this._skipUpdate()) {\n      return;\n    }\n\n    const valuetext = this.displayWith(source.value);\n\n    this._hasViewInitialized\n      ? source._valuetext.set(valuetext)\n      : source._hostElement.setAttribute('aria-valuetext', valuetext);\n\n    if (this.discrete) {\n      source.thumbPosition === _MatThumb.START\n        ? (this.startValueIndicatorText = valuetext)\n        : (this.endValueIndicatorText = valuetext);\n\n      const visualThumb = this._getThumb(source.thumbPosition);\n      valuetext.length < 3\n        ? visualThumb._hostElement.classList.add('mdc-slider__thumb--short-value')\n        : visualThumb._hostElement.classList.remove('mdc-slider__thumb--short-value');\n    }\n  }\n\n  /** Updates all value indicator UIs in the slider. */\n  private _updateValueIndicatorUIs(): void {\n    const eInput = this._getInput(_MatThumb.END);\n    const sInput = this._getInput(_MatThumb.START);\n\n    if (eInput) {\n      this._updateValueIndicatorUI(eInput);\n    }\n    if (sInput) {\n      this._updateValueIndicatorUI(sInput);\n    }\n  }\n\n  // Update Tick Mark Track Width\n  //\n  // 1. Min, max, or step\n  //    - Reason: The maximum reachable value may have changed.\n  //    - Side note: The maximum reachable value is different from the maximum value set by the\n  //      user. For example, a slider with [min: 5, max: 100, step: 10] would have a maximum\n  //      reachable value of 95.\n  // 2. Resize\n  //    - Reason: The position for the maximum reachable value needs to be recalculated.\n\n  /** Updates the width of the tick mark track. */\n  private _updateTickMarkTrackUI(): void {\n    if (!this.showTickMarks || this._skipUpdate()) {\n      return;\n    }\n\n    const step = this._step && this._step > 0 ? this._step : 1;\n    const maxValue = Math.floor(this.max / step) * step;\n    const percentage = (maxValue - this.min) / (this.max - this.min);\n    this._tickMarkTrackWidth = (this._cachedWidth - 6) * percentage;\n  }\n\n  // Track active update conditions\n  //\n  // 1. TranslateX\n  //    - Reason: The track active should line up with the new thumb position.\n  // 2. Min or max\n  //    - Reason #1: The 'active' percentage needs to be recalculated.\n  //    - Reason #2: The value may have silently changed.\n  // 3. Step\n  //    - Reason: The value may have silently changed causing the thumb(s) to shift.\n  // 4. Dir change\n  //    - Reason: The track active will need to be updated according to the new thumb position(s).\n  // 5. Resize\n  //    - Reason: The total width the 'active' tracks translateX is based on has changed.\n\n  /** Updates the scale on the active portion of the track. */\n  _updateTrackUI(source: _MatSliderThumb): void {\n    if (this._skipUpdate()) {\n      return;\n    }\n\n    this._isRange\n      ? this._updateTrackUIRange(source as _MatSliderRangeThumb)\n      : this._updateTrackUINonRange(source as _MatSliderThumb);\n  }\n\n  private _updateTrackUIRange(source: _MatSliderRangeThumb): void {\n    const sibling = source.getSibling();\n    if (!sibling || !this._cachedWidth) {\n      return;\n    }\n\n    const activePercentage = Math.abs(sibling.translateX - source.translateX) / this._cachedWidth;\n\n    if (source._isLeftThumb && this._cachedWidth) {\n      this._setTrackActiveStyles({\n        left: 'auto',\n        right: `${this._cachedWidth - sibling.translateX}px`,\n        transformOrigin: 'right',\n        transform: `scaleX(${activePercentage})`,\n      });\n    } else {\n      this._setTrackActiveStyles({\n        left: `${sibling.translateX}px`,\n        right: 'auto',\n        transformOrigin: 'left',\n        transform: `scaleX(${activePercentage})`,\n      });\n    }\n  }\n\n  private _updateTrackUINonRange(source: _MatSliderThumb): void {\n    this._isRtl\n      ? this._setTrackActiveStyles({\n          left: 'auto',\n          right: '0px',\n          transformOrigin: 'right',\n          transform: `scaleX(${1 - source.fillPercentage})`,\n        })\n      : this._setTrackActiveStyles({\n          left: '0px',\n          right: 'auto',\n          transformOrigin: 'left',\n          transform: `scaleX(${source.fillPercentage})`,\n        });\n  }\n\n  // Tick mark update conditions\n  //\n  // 1. Value\n  //    - Reason: a tick mark which was once active might now be inactive or vice versa.\n  // 2. Min, max, or step\n  //    - Reason #1: the number of tick marks may have changed.\n  //    - Reason #2: The value may have silently changed.\n\n  /** Updates the dots along the slider track. */\n  _updateTickMarkUI(): void {\n    if (\n      !this.showTickMarks ||\n      this.step === undefined ||\n      this.min === undefined ||\n      this.max === undefined\n    ) {\n      return;\n    }\n    const step = this.step > 0 ? this.step : 1;\n    this._isRange ? this._updateTickMarkUIRange(step) : this._updateTickMarkUINonRange(step);\n  }\n\n  private _updateTickMarkUINonRange(step: number): void {\n    const value = this._getValue();\n    let numActive = Math.max(Math.round((value - this.min) / step), 0) + 1;\n    let numInactive = Math.max(Math.round((this.max - value) / step), 0) - 1;\n    this._isRtl ? numActive++ : numInactive++;\n\n    this._tickMarks = Array(numActive)\n      .fill(_MatTickMark.ACTIVE)\n      .concat(Array(numInactive).fill(_MatTickMark.INACTIVE));\n  }\n\n  private _updateTickMarkUIRange(step: number): void {\n    const endValue = this._getValue();\n    const startValue = this._getValue(_MatThumb.START);\n\n    const numInactiveBeforeStartThumb = Math.max(Math.round((startValue - this.min) / step), 0);\n    const numActive = Math.max(Math.round((endValue - startValue) / step) + 1, 0);\n    const numInactiveAfterEndThumb = Math.max(Math.round((this.max - endValue) / step), 0);\n    this._tickMarks = Array(numInactiveBeforeStartThumb)\n      .fill(_MatTickMark.INACTIVE)\n      .concat(\n        Array(numActive).fill(_MatTickMark.ACTIVE),\n        Array(numInactiveAfterEndThumb).fill(_MatTickMark.INACTIVE),\n      );\n  }\n\n  /** Gets the slider thumb input of the given thumb position. */\n  _getInput(thumbPosition: _MatThumb): _MatSliderThumb | _MatSliderRangeThumb | undefined {\n    if (thumbPosition === _MatThumb.END && this._input) {\n      return this._input;\n    }\n    if (this._inputs?.length) {\n      return thumbPosition === _MatThumb.START ? this._inputs.first : this._inputs.last;\n    }\n    return;\n  }\n\n  /** Gets the slider thumb HTML input element of the given thumb position. */\n  _getThumb(thumbPosition: _MatThumb): _MatSliderVisualThumb {\n    return thumbPosition === _MatThumb.END ? this._thumbs?.last! : this._thumbs?.first!;\n  }\n\n  _setTransition(withAnimation: boolean): void {\n    this._hasAnimation = !this._platform.IOS && withAnimation && !this._noopAnimations;\n    this._elementRef.nativeElement.classList.toggle(\n      'mat-mdc-slider-with-animation',\n      this._hasAnimation,\n    );\n  }\n\n  /** Whether the given pointer event occurred within the bounds of the slider pointer's DOM Rect. */\n  _isCursorOnSliderThumb(event: PointerEvent, rect: DOMRect) {\n    const radius = rect.width / 2;\n    const centerX = rect.x + radius;\n    const centerY = rect.y + radius;\n    const dx = event.clientX - centerX;\n    const dy = event.clientY - centerY;\n    return Math.pow(dx, 2) + Math.pow(dy, 2) < Math.pow(radius, 2);\n  }\n}\n\n/** Ensures that there is not an invalid configuration for the slider thumb inputs. */\nfunction _validateInputs(\n  isRange: boolean,\n  endInputElement: _MatSliderThumb | _MatSliderRangeThumb | undefined,\n  startInputElement: _MatSliderThumb | undefined,\n): void {\n  const startValid =\n    !isRange || startInputElement?._hostElement.hasAttribute('matSliderStartThumb');\n  const endValid = endInputElement?._hostElement.hasAttribute(\n    isRange ? 'matSliderEndThumb' : 'matSliderThumb',\n  );\n\n  if (!startValid || !endValid) {\n    _throwInvalidInputConfigurationError();\n  }\n}\n\nfunction _throwInvalidInputConfigurationError(): void {\n  throw Error(`Invalid slider thumb input configuration!\n\n   Valid configurations are as follows:\n\n     <mat-slider>\n       <input matSliderThumb>\n     </mat-slider>\n\n     or\n\n     <mat-slider>\n       <input matSliderStartThumb>\n       <input matSliderEndThumb>\n     </mat-slider>\n   `);\n}\n", "<!-- Inputs -->\n<ng-content></ng-content>\n\n<!-- Track -->\n<div class=\"mdc-slider__track\">\n  <div class=\"mdc-slider__track--inactive\"></div>\n  <div class=\"mdc-slider__track--active\">\n    <div #trackActive class=\"mdc-slider__track--active_fill\"></div>\n  </div>\n  @if (showTickMarks) {\n    <div class=\"mdc-slider__tick-marks\" #tickMarkContainer>\n      @if (_cachedWidth) {\n        @for (tickMark of _tickMarks; track i; let i = $index) {\n          <div\n            [class]=\"tickMark === 0 ? 'mdc-slider__tick-mark--active' : 'mdc-slider__tick-mark--inactive'\"\n            [style.transform]=\"_calcTickMarkTransform(i)\"></div>\n        }\n      }\n    </div>\n  }\n</div>\n\n<!-- Thumbs -->\n@if (_isRange) {\n  <mat-slider-visual-thumb\n    [discrete]=\"discrete\"\n    [thumbPosition]=\"1\"\n    [valueIndicatorText]=\"startValueIndicatorText\">\n  </mat-slider-visual-thumb>\n}\n\n<mat-slider-visual-thumb\n  [discrete]=\"discrete\"\n  [thumbPosition]=\"2\"\n  [valueIndicatorText]=\"endValueIndicatorText\">\n</mat-slider-visual-thumb>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  booleanAttribute,\n  ChangeDetectorRef,\n  Directive,\n  ElementRef,\n  EventEmitter,\n  forwardRef,\n  inject,\n  Input,\n  NgZone,\n  numberA<PERSON>ribute,\n  OnD<PERSON>roy,\n  Output,\n  Renderer2,\n  signal,\n} from '@angular/core';\nimport {ControlValueAccessor, FormControl, NG_VALUE_ACCESSOR} from '@angular/forms';\nimport {Subject} from 'rxjs';\nimport {\n  _MatThumb,\n  MatSliderDragEvent,\n  _MatSlider,\n  _MatSliderRangeThumb,\n  _MatSliderThumb,\n  MAT_SLIDER_RANGE_THUMB,\n  MAT_SLIDER_THUMB,\n  MAT_SLIDER,\n} from './slider-interface';\nimport {Platform} from '@angular/cdk/platform';\n\n/**\n * Provider that allows the slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nexport const MAT_SLIDER_THUMB_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSliderThumb),\n  multi: true,\n};\n\n/**\n * Provider that allows the range slider thumb to register as a ControlValueAccessor.\n * @docs-private\n */\nexport const MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR: any = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSliderRangeThumb),\n  multi: true,\n};\n\n/**\n * Directive that adds slider-specific behaviors to an input element inside `<mat-slider>`.\n * Up to two may be placed inside of a `<mat-slider>`.\n *\n * If one is used, the selector `matSliderThumb` must be used, and the outcome will be a normal\n * slider. If two are used, the selectors `matSliderStartThumb` and `matSliderEndThumb` must be\n * used, and the outcome will be a range slider with two slider thumbs.\n */\n@Directive({\n  selector: 'input[matSliderThumb]',\n  exportAs: 'matSliderThumb',\n  host: {\n    'class': 'mdc-slider__input',\n    'type': 'range',\n    '[attr.aria-valuetext]': '_valuetext()',\n    '(change)': '_onChange()',\n    '(input)': '_onInput()',\n    // TODO(wagnermaciel): Consider using a global event listener instead.\n    // Reason: I have found a semi-consistent way to mouse up without triggering this event.\n    '(blur)': '_onBlur()',\n    '(focus)': '_onFocus()',\n  },\n  providers: [\n    MAT_SLIDER_THUMB_VALUE_ACCESSOR,\n    {provide: MAT_SLIDER_THUMB, useExisting: MatSliderThumb},\n  ],\n})\nexport class MatSliderThumb implements _MatSliderThumb, OnDestroy, ControlValueAccessor {\n  readonly _ngZone = inject(NgZone);\n  readonly _elementRef = inject<ElementRef<HTMLInputElement>>(ElementRef);\n  readonly _cdr = inject(ChangeDetectorRef);\n  protected _slider = inject<_MatSlider>(MAT_SLIDER);\n  private _platform = inject(Platform);\n  private _listenerCleanups: (() => void)[];\n\n  @Input({transform: numberAttribute})\n  get value(): number {\n    return numberAttribute(this._hostElement.value, 0);\n  }\n  set value(value: number) {\n    if (value === null) {\n      value = this._getDefaultValue();\n    }\n    value = isNaN(value) ? 0 : value;\n    const stringValue = value + '';\n    if (!this._hasSetInitialValue) {\n      this._initialValue = stringValue;\n      return;\n    }\n    if (this._isActive) {\n      return;\n    }\n    this._setValue(stringValue);\n  }\n\n  /**\n   * Handles programmatic value setting. This has been split out to\n   * allow the range thumb to override it and add additional necessary logic.\n   */\n  protected _setValue(value: string) {\n    this._hostElement.value = value;\n    this._updateThumbUIByValue();\n    this._slider._onValueChange(this);\n    this._cdr.detectChanges();\n    this._slider._cdr.markForCheck();\n  }\n\n  /** Event emitted when the `value` is changed. */\n  @Output() readonly valueChange: EventEmitter<number> = new EventEmitter<number>();\n\n  /** Event emitted when the slider thumb starts being dragged. */\n  @Output() readonly dragStart: EventEmitter<MatSliderDragEvent> =\n    new EventEmitter<MatSliderDragEvent>();\n\n  /** Event emitted when the slider thumb stops being dragged. */\n  @Output() readonly dragEnd: EventEmitter<MatSliderDragEvent> =\n    new EventEmitter<MatSliderDragEvent>();\n\n  /**\n   * The current translateX in px of the slider visual thumb.\n   * @docs-private\n   */\n  get translateX(): number {\n    if (this._slider.min >= this._slider.max) {\n      this._translateX = this._tickMarkOffset;\n      return this._translateX;\n    }\n    if (this._translateX === undefined) {\n      this._translateX = this._calcTranslateXByValue();\n    }\n    return this._translateX;\n  }\n  set translateX(v: number) {\n    this._translateX = v;\n  }\n  private _translateX: number | undefined;\n\n  /**\n   * Indicates whether this thumb is the start or end thumb.\n   * @docs-private\n   */\n  thumbPosition: _MatThumb = _MatThumb.END;\n\n  /** @docs-private */\n  get min(): number {\n    return numberAttribute(this._hostElement.min, 0);\n  }\n  set min(v: number) {\n    this._hostElement.min = v + '';\n    this._cdr.detectChanges();\n  }\n\n  /** @docs-private */\n  get max(): number {\n    return numberAttribute(this._hostElement.max, 0);\n  }\n  set max(v: number) {\n    this._hostElement.max = v + '';\n    this._cdr.detectChanges();\n  }\n\n  get step(): number {\n    return numberAttribute(this._hostElement.step, 0);\n  }\n  set step(v: number) {\n    this._hostElement.step = v + '';\n    this._cdr.detectChanges();\n  }\n\n  /** @docs-private */\n  get disabled(): boolean {\n    return booleanAttribute(this._hostElement.disabled);\n  }\n  set disabled(v: boolean) {\n    this._hostElement.disabled = v;\n    this._cdr.detectChanges();\n\n    if (this._slider.disabled !== this.disabled) {\n      this._slider.disabled = this.disabled;\n    }\n  }\n\n  /** The percentage of the slider that coincides with the value. */\n  get percentage(): number {\n    if (this._slider.min >= this._slider.max) {\n      return this._slider._isRtl ? 1 : 0;\n    }\n    return (this.value - this._slider.min) / (this._slider.max - this._slider.min);\n  }\n\n  /** @docs-private */\n  get fillPercentage(): number {\n    if (!this._slider._cachedWidth) {\n      return this._slider._isRtl ? 1 : 0;\n    }\n    if (this._translateX === 0) {\n      return 0;\n    }\n    return this.translateX / this._slider._cachedWidth;\n  }\n\n  /** The host native HTML input element. */\n  _hostElement = this._elementRef.nativeElement;\n\n  /** The aria-valuetext string representation of the input's value. */\n  _valuetext = signal('');\n\n  /** The radius of a native html slider's knob. */\n  _knobRadius: number = 8;\n\n  /** The distance in px from the start of the slider track to the first tick mark. */\n  _tickMarkOffset = 3;\n\n  /** Whether user's cursor is currently in a mouse down state on the input. */\n  _isActive: boolean = false;\n\n  /** Whether the input is currently focused (either by tab or after clicking). */\n  _isFocused: boolean = false;\n\n  /** Used to relay updates to _isFocused to the slider visual thumbs. */\n  private _setIsFocused(v: boolean): void {\n    this._isFocused = v;\n  }\n\n  /**\n   * Whether the initial value has been set.\n   * This exists because the initial value cannot be immediately set because the min and max\n   * must first be relayed from the parent MatSlider component, which can only happen later\n   * in the component lifecycle.\n   */\n  private _hasSetInitialValue: boolean = false;\n\n  /** The stored initial value. */\n  _initialValue: string | undefined;\n\n  /** Defined when a user is using a form control to manage slider value & validation. */\n  private _formControl: FormControl | undefined;\n\n  /** Emits when the component is destroyed. */\n  protected readonly _destroyed = new Subject<void>();\n\n  /**\n   * Indicates whether UI updates should be skipped.\n   *\n   * This flag is used to avoid flickering\n   * when correcting values on pointer up/down.\n   */\n  _skipUIUpdate: boolean = false;\n\n  /** Callback called when the slider input value changes. */\n  protected _onChangeFn: ((value: any) => void) | undefined;\n\n  /** Callback called when the slider input has been touched. */\n  private _onTouchedFn: () => void = () => {};\n\n  /**\n   * Whether the NgModel has been initialized.\n   *\n   * This flag is used to ignore ghost null calls to\n   * writeValue which can break slider initialization.\n   *\n   * See https://github.com/angular/angular/issues/14988.\n   */\n  protected _isControlInitialized = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const renderer = inject(Renderer2);\n\n    this._ngZone.runOutsideAngular(() => {\n      this._listenerCleanups = [\n        renderer.listen(this._hostElement, 'pointerdown', this._onPointerDown.bind(this)),\n        renderer.listen(this._hostElement, 'pointermove', this._onPointerMove.bind(this)),\n        renderer.listen(this._hostElement, 'pointerup', this._onPointerUp.bind(this)),\n      ];\n    });\n  }\n\n  ngOnDestroy(): void {\n    this._listenerCleanups.forEach(cleanup => cleanup());\n    this._destroyed.next();\n    this._destroyed.complete();\n    this.dragStart.complete();\n    this.dragEnd.complete();\n  }\n\n  /** @docs-private */\n  initProps(): void {\n    this._updateWidthInactive();\n\n    // If this or the parent slider is disabled, just make everything disabled.\n    if (this.disabled !== this._slider.disabled) {\n      // The MatSlider setter for disabled will relay this and disable both inputs.\n      this._slider.disabled = true;\n    }\n\n    this.step = this._slider.step;\n    this.min = this._slider.min;\n    this.max = this._slider.max;\n    this._initValue();\n  }\n\n  /** @docs-private */\n  initUI(): void {\n    this._updateThumbUIByValue();\n  }\n\n  _initValue(): void {\n    this._hasSetInitialValue = true;\n    if (this._initialValue === undefined) {\n      this.value = this._getDefaultValue();\n    } else {\n      this._hostElement.value = this._initialValue;\n      this._updateThumbUIByValue();\n      this._slider._onValueChange(this);\n      this._cdr.detectChanges();\n    }\n  }\n\n  _getDefaultValue(): number {\n    return this.min;\n  }\n\n  _onBlur(): void {\n    this._setIsFocused(false);\n    this._onTouchedFn();\n  }\n\n  _onFocus(): void {\n    this._slider._setTransition(false);\n    this._slider._updateTrackUI(this);\n    this._setIsFocused(true);\n  }\n\n  _onChange(): void {\n    this.valueChange.emit(this.value);\n    // only used to handle the edge case where user\n    // mousedown on the slider then uses arrow keys.\n    if (this._isActive) {\n      this._updateThumbUIByValue({withAnimation: true});\n    }\n  }\n\n  _onInput(): void {\n    this._onChangeFn?.(this.value);\n    // handles arrowing and updating the value when\n    // a step is defined.\n    if (this._slider.step || !this._isActive) {\n      this._updateThumbUIByValue({withAnimation: true});\n    }\n    this._slider._onValueChange(this);\n  }\n\n  _onNgControlValueChange(): void {\n    // only used to handle when the value change\n    // originates outside of the slider.\n    if (!this._isActive || !this._isFocused) {\n      this._slider._onValueChange(this);\n      this._updateThumbUIByValue();\n    }\n    this._slider.disabled = this._formControl!.disabled;\n  }\n\n  _onPointerDown(event: PointerEvent): void {\n    if (this.disabled || event.button !== 0) {\n      return;\n    }\n\n    // On IOS, dragging only works if the pointer down happens on the\n    // slider thumb and the slider does not receive focus from pointer events.\n    if (this._platform.IOS) {\n      const isCursorOnSliderThumb = this._slider._isCursorOnSliderThumb(\n        event,\n        this._slider._getThumb(this.thumbPosition)._hostElement.getBoundingClientRect(),\n      );\n\n      this._isActive = isCursorOnSliderThumb;\n      this._updateWidthActive();\n      this._slider._updateDimensions();\n      return;\n    }\n\n    this._isActive = true;\n    this._setIsFocused(true);\n    this._updateWidthActive();\n    this._slider._updateDimensions();\n\n    // Does nothing if a step is defined because we\n    // want the value to snap to the values on input.\n    if (!this._slider.step) {\n      this._updateThumbUIByPointerEvent(event, {withAnimation: true});\n    }\n\n    if (!this.disabled) {\n      this._handleValueCorrection(event);\n      this.dragStart.emit({source: this, parent: this._slider, value: this.value});\n    }\n  }\n\n  /**\n   * Corrects the value of the slider on pointer up/down.\n   *\n   * Called on pointer down and up because the value is set based\n   * on the inactive width instead of the active width.\n   */\n  private _handleValueCorrection(event: PointerEvent): void {\n    // Don't update the UI with the current value! The value on pointerdown\n    // and pointerup is calculated in the split second before the input(s)\n    // resize. See _updateWidthInactive() and _updateWidthActive() for more\n    // details.\n    this._skipUIUpdate = true;\n\n    // Note that this function gets triggered before the actual value of the\n    // slider is updated. This means if we were to set the value here, it\n    // would immediately be overwritten. Using setTimeout ensures the setting\n    // of the value happens after the value has been updated by the\n    // pointerdown event.\n    setTimeout(() => {\n      this._skipUIUpdate = false;\n      this._fixValue(event);\n    }, 0);\n  }\n\n  /** Corrects the value of the slider based on the pointer event's position. */\n  _fixValue(event: PointerEvent): void {\n    const xPos = event.clientX - this._slider._cachedLeft;\n    const width = this._slider._cachedWidth;\n    const step = this._slider.step === 0 ? 1 : this._slider.step;\n    const numSteps = Math.floor((this._slider.max - this._slider.min) / step);\n    const percentage = this._slider._isRtl ? 1 - xPos / width : xPos / width;\n\n    // To ensure the percentage is rounded to the necessary number of decimals.\n    const fixedPercentage = Math.round(percentage * numSteps) / numSteps;\n\n    const impreciseValue =\n      fixedPercentage * (this._slider.max - this._slider.min) + this._slider.min;\n    const value = Math.round(impreciseValue / step) * step;\n    const prevValue = this.value;\n\n    if (value === prevValue) {\n      // Because we prevented UI updates, if it turns out that the race\n      // condition didn't happen and the value is already correct, we\n      // have to apply the ui updates now.\n      this._slider._onValueChange(this);\n      this._slider.step > 0\n        ? this._updateThumbUIByValue()\n        : this._updateThumbUIByPointerEvent(event, {withAnimation: this._slider._hasAnimation});\n      return;\n    }\n\n    this.value = value;\n    this.valueChange.emit(this.value);\n    this._onChangeFn?.(this.value);\n    this._slider._onValueChange(this);\n    this._slider.step > 0\n      ? this._updateThumbUIByValue()\n      : this._updateThumbUIByPointerEvent(event, {withAnimation: this._slider._hasAnimation});\n  }\n\n  _onPointerMove(event: PointerEvent): void {\n    // Again, does nothing if a step is defined because\n    // we want the value to snap to the values on input.\n    if (!this._slider.step && this._isActive) {\n      this._updateThumbUIByPointerEvent(event);\n    }\n  }\n\n  _onPointerUp(): void {\n    if (this._isActive) {\n      this._isActive = false;\n      if (this._platform.SAFARI) {\n        this._setIsFocused(false);\n      }\n      this.dragEnd.emit({source: this, parent: this._slider, value: this.value});\n\n      // This setTimeout is to prevent the pointerup from triggering a value\n      // change on the input based on the inactive width. It's not clear why\n      // but for some reason on IOS this race condition is even more common so\n      // the timeout needs to be increased.\n      setTimeout(() => this._updateWidthInactive(), this._platform.IOS ? 10 : 0);\n    }\n  }\n\n  _clamp(v: number): number {\n    const min = this._tickMarkOffset;\n    const max = this._slider._cachedWidth - this._tickMarkOffset;\n    return Math.max(Math.min(v, max), min);\n  }\n\n  _calcTranslateXByValue(): number {\n    if (this._slider._isRtl) {\n      return (\n        (1 - this.percentage) * (this._slider._cachedWidth - this._tickMarkOffset * 2) +\n        this._tickMarkOffset\n      );\n    }\n    return (\n      this.percentage * (this._slider._cachedWidth - this._tickMarkOffset * 2) +\n      this._tickMarkOffset\n    );\n  }\n\n  _calcTranslateXByPointerEvent(event: PointerEvent): number {\n    return event.clientX - this._slider._cachedLeft;\n  }\n\n  /**\n   * Used to set the slider width to the correct\n   * dimensions while the user is dragging.\n   */\n  _updateWidthActive(): void {}\n\n  /**\n   * Sets the slider input to disproportionate dimensions to allow for touch\n   * events to be captured on touch devices.\n   */\n  _updateWidthInactive(): void {\n    this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n    this._hostElement.style.width = `calc(100% + ${\n      this._slider._inputPadding - this._tickMarkOffset * 2\n    }px)`;\n    this._hostElement.style.left = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n  }\n\n  _updateThumbUIByValue(options?: {withAnimation: boolean}): void {\n    this.translateX = this._clamp(this._calcTranslateXByValue());\n    this._updateThumbUI(options);\n  }\n\n  _updateThumbUIByPointerEvent(event: PointerEvent, options?: {withAnimation: boolean}): void {\n    this.translateX = this._clamp(this._calcTranslateXByPointerEvent(event));\n    this._updateThumbUI(options);\n  }\n\n  _updateThumbUI(options?: {withAnimation: boolean}) {\n    this._slider._setTransition(!!options?.withAnimation);\n    this._slider._onTranslateXChange(this);\n  }\n\n  /**\n   * Sets the input's value.\n   * @param value The new value of the input\n   * @docs-private\n   */\n  writeValue(value: any): void {\n    if (this._isControlInitialized || value !== null) {\n      this.value = value;\n    }\n  }\n\n  /**\n   * Registers a callback to be invoked when the input's value changes from user input.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnChange(fn: any): void {\n    this._onChangeFn = fn;\n    this._isControlInitialized = true;\n  }\n\n  /**\n   * Registers a callback to be invoked when the input is blurred by the user.\n   * @param fn The callback to register\n   * @docs-private\n   */\n  registerOnTouched(fn: any): void {\n    this._onTouchedFn = fn;\n  }\n\n  /**\n   * Sets the disabled state of the slider.\n   * @param isDisabled The new disabled state\n   * @docs-private\n   */\n  setDisabledState(isDisabled: boolean): void {\n    this.disabled = isDisabled;\n  }\n\n  focus(): void {\n    this._hostElement.focus();\n  }\n\n  blur(): void {\n    this._hostElement.blur();\n  }\n}\n\n@Directive({\n  selector: 'input[matSliderStartThumb], input[matSliderEndThumb]',\n  exportAs: 'matSliderRangeThumb',\n  providers: [\n    MAT_SLIDER_RANGE_THUMB_VALUE_ACCESSOR,\n    {provide: MAT_SLIDER_RANGE_THUMB, useExisting: MatSliderRangeThumb},\n  ],\n})\nexport class MatSliderRangeThumb extends MatSliderThumb implements _MatSliderRangeThumb {\n  override readonly _cdr = inject(ChangeDetectorRef);\n\n  /** @docs-private */\n  getSibling(): _MatSliderRangeThumb | undefined {\n    if (!this._sibling) {\n      this._sibling = this._slider._getInput(this._isEndThumb ? _MatThumb.START : _MatThumb.END) as\n        | MatSliderRangeThumb\n        | undefined;\n    }\n    return this._sibling;\n  }\n  private _sibling: MatSliderRangeThumb | undefined;\n\n  /**\n   * Returns the minimum translateX position allowed for this slider input's visual thumb.\n   * @docs-private\n   */\n  getMinPos(): number {\n    const sibling = this.getSibling();\n    if (!this._isLeftThumb && sibling) {\n      return sibling.translateX;\n    }\n    return this._tickMarkOffset;\n  }\n\n  /**\n   * Returns the maximum translateX position allowed for this slider input's visual thumb.\n   * @docs-private\n   */\n  getMaxPos(): number {\n    const sibling = this.getSibling();\n    if (this._isLeftThumb && sibling) {\n      return sibling.translateX;\n    }\n    return this._slider._cachedWidth - this._tickMarkOffset;\n  }\n\n  _setIsLeftThumb(): void {\n    this._isLeftThumb =\n      (this._isEndThumb && this._slider._isRtl) || (!this._isEndThumb && !this._slider._isRtl);\n  }\n\n  /** Whether this slider corresponds to the input on the left hand side. */\n  _isLeftThumb: boolean;\n\n  /** Whether this slider corresponds to the input with greater value. */\n  _isEndThumb: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n\n    this._isEndThumb = this._hostElement.hasAttribute('matSliderEndThumb');\n    this._setIsLeftThumb();\n    this.thumbPosition = this._isEndThumb ? _MatThumb.END : _MatThumb.START;\n  }\n\n  override _getDefaultValue(): number {\n    return this._isEndThumb && this._slider._isRange ? this.max : this.min;\n  }\n\n  override _onInput(): void {\n    super._onInput();\n    this._updateSibling();\n    if (!this._isActive) {\n      this._updateWidthInactive();\n    }\n  }\n\n  override _onNgControlValueChange(): void {\n    super._onNgControlValueChange();\n    this.getSibling()?._updateMinMax();\n  }\n\n  override _onPointerDown(event: PointerEvent): void {\n    if (this.disabled || event.button !== 0) {\n      return;\n    }\n    if (this._sibling) {\n      this._sibling._updateWidthActive();\n      this._sibling._hostElement.classList.add('mat-mdc-slider-input-no-pointer-events');\n    }\n    super._onPointerDown(event);\n  }\n\n  override _onPointerUp(): void {\n    super._onPointerUp();\n    if (this._sibling) {\n      setTimeout(() => {\n        this._sibling!._updateWidthInactive();\n        this._sibling!._hostElement.classList.remove('mat-mdc-slider-input-no-pointer-events');\n      });\n    }\n  }\n\n  override _onPointerMove(event: PointerEvent): void {\n    super._onPointerMove(event);\n    if (!this._slider.step && this._isActive) {\n      this._updateSibling();\n    }\n  }\n\n  override _fixValue(event: PointerEvent): void {\n    super._fixValue(event);\n    this._sibling?._updateMinMax();\n  }\n\n  override _clamp(v: number): number {\n    return Math.max(Math.min(v, this.getMaxPos()), this.getMinPos());\n  }\n\n  _updateMinMax(): void {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    if (this._isEndThumb) {\n      this.min = Math.max(this._slider.min, sibling.value);\n      this.max = this._slider.max;\n    } else {\n      this.min = this._slider.min;\n      this.max = Math.min(this._slider.max, sibling.value);\n    }\n  }\n\n  override _updateWidthActive(): void {\n    const minWidth = this._slider._rippleRadius * 2 - this._slider._inputPadding * 2;\n    const maxWidth =\n      this._slider._cachedWidth + this._slider._inputPadding - minWidth - this._tickMarkOffset * 2;\n    const percentage =\n      this._slider.min < this._slider.max\n        ? (this.max - this.min) / (this._slider.max - this._slider.min)\n        : 1;\n    const width = maxWidth * percentage + minWidth;\n    this._hostElement.style.width = `${width}px`;\n    this._hostElement.style.padding = `0 ${this._slider._inputPadding}px`;\n  }\n\n  override _updateWidthInactive(): void {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    const maxWidth = this._slider._cachedWidth - this._tickMarkOffset * 2;\n    const midValue = this._isEndThumb\n      ? this.value - (this.value - sibling.value) / 2\n      : this.value + (sibling.value - this.value) / 2;\n\n    const _percentage = this._isEndThumb\n      ? (this.max - midValue) / (this._slider.max - this._slider.min)\n      : (midValue - this.min) / (this._slider.max - this._slider.min);\n\n    const percentage = this._slider.min < this._slider.max ? _percentage : 1;\n\n    // Extend the native input width by the radius of the ripple\n    let ripplePadding = this._slider._rippleRadius;\n\n    // If one of the inputs is maximally sized (the value of both thumbs is\n    // equal to the min or max), make that input take up all of the width and\n    // make the other unselectable.\n    if (percentage === 1) {\n      ripplePadding = 48;\n    } else if (percentage === 0) {\n      ripplePadding = 0;\n    }\n\n    const width = maxWidth * percentage + ripplePadding;\n    this._hostElement.style.width = `${width}px`;\n    this._hostElement.style.padding = '0px';\n\n    if (this._isLeftThumb) {\n      this._hostElement.style.left = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n      this._hostElement.style.right = 'auto';\n    } else {\n      this._hostElement.style.left = 'auto';\n      this._hostElement.style.right = `-${this._slider._rippleRadius - this._tickMarkOffset}px`;\n    }\n  }\n\n  _updateStaticStyles(): void {\n    this._hostElement.classList.toggle('mat-slider__right-input', !this._isLeftThumb);\n  }\n\n  private _updateSibling(): void {\n    const sibling = this.getSibling();\n    if (!sibling) {\n      return;\n    }\n    sibling._updateMinMax();\n    if (this._isActive) {\n      sibling._updateWidthActive();\n    } else {\n      sibling._updateWidthInactive();\n    }\n  }\n\n  /**\n   * Sets the input's value.\n   * @param value The new value of the input\n   * @docs-private\n   */\n  override writeValue(value: any): void {\n    if (this._isControlInitialized || value !== null) {\n      this.value = value;\n      this._updateWidthInactive();\n      this._updateSibling();\n    }\n  }\n\n  override _setValue(value: string) {\n    super._setValue(value);\n    this._updateWidthInactive();\n    this._updateSibling();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '../core';\nimport {MatSlider} from './slider';\nimport {MatSliderVisualThumb} from './slider-thumb';\nimport {MatSliderThumb, MatSliderRangeThumb} from './slider-input';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    MatRippleModule,\n    MatSlider,\n    MatSliderThumb,\n    MatSliderRangeThumb,\n    MatSliderVisualThumb,\n  ],\n  exports: [MatSlider, MatSliderThumb, MatSliderRangeThumb],\n})\nexport class MatSliderModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAWA;;;AAGG;AACH,IAAY,SAGX;AAHD,CAAA,UAAY,SAAS,EAAA;AACnB,IAAA,SAAA,CAAA,SAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAS;AACT,IAAA,SAAA,CAAA,SAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAO;AACT,CAAC,EAHW,SAAS,KAAT,SAAS,GAGpB,EAAA,CAAA,CAAA;AAED;AACA,IAAY,YAGX;AAHD,CAAA,UAAY,YAAY,EAAA;AACtB,IAAA,YAAA,CAAA,YAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAU;AACV,IAAA,YAAA,CAAA,YAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAY;AACd,CAAC,EAHW,YAAY,KAAZ,YAAY,GAGvB,EAAA,CAAA,CAAA;AAED;;;;;AAKG;AACI,MAAM,UAAU,GAAG,IAAI,cAAc,CAAK,YAAY,CAAC;AAE9D;;;;AAIG;AACI,MAAM,gBAAgB,GAAG,IAAI,cAAc,CAAK,iBAAiB,CAAC;AAEzE;;;;AAIG;AACI,MAAM,sBAAsB,GAAG,IAAI,cAAc,CAAK,sBAAsB,CAAC;AAEpF;;;;AAIG;AACI,MAAM,uBAAuB,GAAG,IAAI,cAAc,CAAK,uBAAuB,CAAC;AActF;;;;AAIG;MACU,eAAe,CAAA;;AAE1B,IAAA,MAAM;;AAGN,IAAA,MAAM;;AAGN,IAAA,KAAK;AACN;;AChDD;;;;;;AAMG;MAaU,oBAAoB,CAAA;AACtB,IAAA,IAAI,GAAG,MAAM,CAAC,iBAAiB,CAAC;AACxB,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACjC,IAAA,OAAO,GAAG,MAAM,CAAa,UAAU,CAAC;AACxC,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;AAC7B,IAAA,iBAAiB;;AAGhB,IAAA,QAAQ;;AAGR,IAAA,aAAa;;AAGb,IAAA,kBAAkB;;AAGI,IAAA,OAAO;;AAGnB,IAAA,KAAK;;AAIxB,IAAA,wBAAwB;;AAGhB,IAAA,YAAY;;AAGZ,IAAA,cAAc;;AAGd,IAAA,eAAe;;AAGf,IAAA,eAAe;;AAGf,IAAA,gBAAgB;;IAGhB,UAAU,GAAY,KAAK;;IAGnC,SAAS,GAAG,KAAK;;IAGjB,wBAAwB,GAAY,KAAK;;AAGzC,IAAA,YAAY,GAAG,MAAM,CAA0B,UAAU,CAAC,CAAC,aAAa;AAEhE,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAGpC,IAAA,WAAA,GAAA;IAEA,eAAe,GAAA;AACb,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC;;;QAI9D,IAAI,CAAC,WAAW,EAAE;YAChB;;AAGF,QAAA,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE;AACxB,QAAA,IAAI,CAAC,YAAY,GAAG,WAAW;QAC/B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY;;;AAIpD,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAe;AAClC,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS;YAC/B,IAAI,CAAC,iBAAiB,GAAG;gBACvB,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC;gBAC1D,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC;gBACxD,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,UAAU,CAAC;gBACpD,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC;gBAC1D,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC;gBAC9C,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC;aAC7C;AACH,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;;AAG/C,IAAA,cAAc,GAAG,CAAC,KAAmB,KAAU;AACrD,QAAA,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;YAChC;;QAGF,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,qBAAqB,EAAE;AACtD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,KAAK,EAAE,IAAI,CAAC;AAClE,QAAA,IAAI,CAAC,UAAU,GAAG,SAAS;QAE3B,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,gBAAgB,EAAE;;aAClB;AACL,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC;;AAE1C,KAAC;IAEO,aAAa,GAAG,MAAW;AACjC,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK;AACvB,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC;AACxC,KAAC;IAEO,QAAQ,GAAG,MAAW;;;AAG5B,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC;QACtC,IAAI,CAAC,gBAAgB,EAAE;QACvB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,4BAA4B,CAAC;AAC/D,KAAC;IAEO,OAAO,GAAG,MAAW;;AAE3B,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACnB,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC;;;AAGxC,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,gBAAgB,EAAE;;QAEzB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,4BAA4B,CAAC;AAClE,KAAC;AAEO,IAAA,YAAY,GAAG,CAAC,KAAmB,KAAU;AACnD,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB;;AAEF,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;QACrB,IAAI,CAAC,iBAAiB,EAAE;AAC1B,KAAC;IAEO,UAAU,GAAG,MAAW;AAC9B,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC;;AAEvC,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE;AACjC,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC;;;;AAKxC,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACzB,IAAI,CAAC,gBAAgB,EAAE;;AAE3B,KAAC;;IAGO,gBAAgB,GAAA;QACtB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;AAChD,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,EAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC,CAAC;YAC5E,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,6BAA6B,CAAC;;;;IAKtE,gBAAgB,GAAA;;QAEtB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE;AAChD,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,EAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC,EAAE,IAAI,CAAC;YAClF,IAAI,CAAC,eAAe,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,6BAA6B,CAAC;;;;IAKtE,iBAAiB,GAAA;QACvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;AACjD,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,EAAC,aAAa,EAAE,GAAG,EAAE,YAAY,EAAE,GAAG,EAAC,CAAC;YACjF,IAAI,CAAC,gBAAgB,EAAE,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,8BAA8B,CAAC;;;;AAKxE,IAAA,gBAAgB,CAAC,SAAqB,EAAA;AAC5C,QAAA,OAAO,SAAS,EAAE,KAAK,KAAK,WAAW,CAAC,SAAS,IAAI,SAAS,EAAE,KAAK,KAAK,WAAW,CAAC,OAAO;;;IAIvF,WAAW,CACjB,SAAgC,EAChC,wBAAkC,EAAA;AAElC,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACzB;;QAEF,IAAI,CAAC,mBAAmB,EAAE;AAC1B,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACzB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CACpC,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK,CACzE;YACD,OAAO,CAAC,mBAAmB,EAAE;;QAE/B,IAAI,IAAI,CAAC,OAAO,CAAC,oBAAoB,EAAE,QAAQ,IAAI,CAAC,wBAAwB,EAAE;YAC5E;;AAEF,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;YACzB,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,GAAG,EAAC,aAAa,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,EAAC,GAAG,SAAS;AACzF,YAAA,QAAQ,EAAE,IAAI;AACd,YAAA,UAAU,EAAE,IAAI;AACjB,SAAA,CAAC;;AAGJ;;;AAGG;AACK,IAAA,WAAW,CAAC,SAAqB,EAAA;QACvC,SAAS,EAAE,OAAO,EAAE;AAEpB,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE;YAC9B;;AAGF,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YAC1B,IAAI,CAAC,mBAAmB,EAAE;;AAG5B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,EAAE;AAClC,QAAA,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE,EAAE;YAClC,IAAI,CAAC,mBAAmB,EAAE;YAC1B,OAAO,CAAC,mBAAmB,EAAE;;;;IAKjC,mBAAmB,GAAA;QACjB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,mCAAmC,CAAC;;;IAItE,mBAAmB,GAAA;QACjB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,mCAAmC,CAAC;;IAGzE,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,SAAS,CAC3B,IAAI,CAAC,aAAa,KAAK,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK,CACzE;;;IAIH,2BAA2B,GAAA;AACzB,QAAA,OAAO,IAAI,CAAC,wBAAwB,EAAE,aAAa;;;IAIrD,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa;;IAGjC,mBAAmB,GAAA;QACjB,QACE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC;AAC3C,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,CAAC;YAC3C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC;;uGAtQrC,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAApB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,oBAAoB,6PAHpB,CAAC,EAAC,OAAO,EAAE,uBAAuB,EAAE,WAAW,EAAE,oBAAoB,EAAC,CAAC,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,SAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAoBvE,SAAS,ECrEtB,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,OAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,MAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,0BAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,yBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,4YASA,qWDyCY,SAAS,EAAA,QAAA,EAAA,2BAAA,EAAA,MAAA,EAAA,CAAA,gBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,iBAAA,EAAA,oBAAA,EAAA,mBAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAER,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAZhC,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,yBAAyB,EAG7B,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,+CAA+C;qBACzD,EACgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,aAAA,EAChC,iBAAiB,CAAC,IAAI,aAC1B,CAAC,EAAC,OAAO,EAAE,uBAAuB,EAAE,WAAW,EAAA,oBAAsB,EAAC,CAAC,EAAA,OAAA,EACzE,CAAC,SAAS,CAAC,EAAA,QAAA,EAAA,4YAAA,EAAA,MAAA,EAAA,CAAA,6SAAA,CAAA,EAAA;wDAUX,QAAQ,EAAA,CAAA;sBAAhB;gBAGQ,aAAa,EAAA,CAAA;sBAArB;gBAGQ,kBAAkB,EAAA,CAAA;sBAA1B;gBAG8B,OAAO,EAAA,CAAA;sBAArC,SAAS;uBAAC,SAAS;gBAGD,KAAK,EAAA,CAAA;sBAAvB,SAAS;uBAAC,MAAM;gBAIjB,wBAAwB,EAAA,CAAA;sBADvB,SAAS;uBAAC,yBAAyB;;;AEvBtC;AACA;AACA;AACA;AAEA;;;AAGG;MAoBU,SAAS,CAAA;AACX,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,IAAI,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAChC,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;IACzD,IAAI,GAAG,MAAM,CAAC,cAAc,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAC/C,IAAA,oBAAoB,GAAG,MAAM,CAAsB,yBAAyB,EAAE;AACrF,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC;;AAGwB,IAAA,YAAY;;AAGC,IAAA,OAAO;;AAGd,IAAA,MAAM;;AAItC,IAAA,OAAO;;AAGP,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,CAAU,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC;QAC9C,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC;QAElD,IAAI,QAAQ,EAAE;AACZ,YAAA,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS;;QAEpC,IAAI,UAAU,EAAE;AACd,YAAA,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS;;;IAGhC,SAAS,GAAY,KAAK;;AAGlC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,CAAU,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,wBAAwB,EAAE;;IAEzB,SAAS,GAAY,KAAK;;IAIlC,aAAa,GAAY,KAAK;;AAG9B,IAAA,IACI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,IAAI;;IAElB,IAAI,GAAG,CAAC,CAAS,EAAA;QACf,MAAM,GAAG,GAAG,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC;AACrE,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;;;IAGhB,IAAI,GAAW,CAAC;AAExB;;;;;;AAMG;AAEH,IAAA,KAAK;;IAIL,aAAa,GAAY,KAAK;AAEtB,IAAA,UAAU,CAAC,GAAW,EAAA;AAC5B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI;AACzB,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG;AACf,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,EAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAC7F,IAAI,CAAC,qBAAqB,EAAE;;AAGtB,IAAA,eAAe,CAAC,GAA+B,EAAA;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAyB;QACtE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAyB;AAE1E,QAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK;AAClC,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK;AAEtC,QAAA,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;AACxB,QAAA,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,CAAC;AAClD,QAAA,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC;QAEvD,UAAU,CAAC,oBAAoB,EAAE;QACjC,QAAQ,CAAC,oBAAoB,EAAE;AAE/B,QAAA,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;cACV,IAAI,CAAC,+BAA+B,CAAC,QAAQ,EAAE,UAAU;cACzD,IAAI,CAAC,+BAA+B,CAAC,UAAU,EAAE,QAAQ,CAAC;AAE9D,QAAA,IAAI,WAAW,KAAK,QAAQ,CAAC,KAAK,EAAE;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;;AAG/B,QAAA,IAAI,aAAa,KAAK,UAAU,CAAC,KAAK,EAAE;AACtC,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;;;AAI3B,IAAA,kBAAkB,CAAC,GAAW,EAAA;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC;QAC3C,IAAI,KAAK,EAAE;AACT,YAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK;AAE5B,YAAA,KAAK,CAAC,GAAG,GAAG,GAAG;YACf,KAAK,CAAC,qBAAqB,EAAE;AAC7B,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;AAE1B,YAAA,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK,EAAE;AAC5B,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;;;;AAMhC,IAAA,IACI,GAAG,GAAA;QACL,OAAO,IAAI,CAAC,IAAI;;IAElB,IAAI,GAAG,CAAC,CAAS,EAAA;QACf,MAAM,GAAG,GAAG,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC;AACrE,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,GAAG,EAAE;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;;;IAGhB,IAAI,GAAW,GAAG;AAElB,IAAA,UAAU,CAAC,GAAW,EAAA;AAC5B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI;AACzB,QAAA,IAAI,CAAC,IAAI,GAAG,GAAG;AACf,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,EAAC,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAC,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAC7F,IAAI,CAAC,qBAAqB,EAAE;;AAGtB,IAAA,eAAe,CAAC,GAA+B,EAAA;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAyB;QACtE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAyB;AAE1E,QAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK;AAClC,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK;AAEtC,QAAA,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG;AACtB,QAAA,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,KAAK,CAAC;AAClD,QAAA,QAAQ,CAAC,GAAG,GAAG,UAAU,CAAC,KAAK;QAE/B,QAAQ,CAAC,oBAAoB,EAAE;QAC/B,UAAU,CAAC,oBAAoB,EAAE;AAEjC,QAAA,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC;cACV,IAAI,CAAC,+BAA+B,CAAC,UAAU,EAAE,QAAQ;cACzD,IAAI,CAAC,+BAA+B,CAAC,QAAQ,EAAE,UAAU,CAAC;AAE9D,QAAA,IAAI,WAAW,KAAK,QAAQ,CAAC,KAAK,EAAE;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;;AAG/B,QAAA,IAAI,aAAa,KAAK,UAAU,CAAC,KAAK,EAAE;AACtC,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;;;AAI3B,IAAA,kBAAkB,CAAC,GAAW,EAAA;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC;QAC3C,IAAI,KAAK,EAAE;AACT,YAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK;AAE5B,YAAA,KAAK,CAAC,GAAG,GAAG,GAAG;YACf,KAAK,CAAC,qBAAqB,EAAE;AAC7B,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;AAE1B,YAAA,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK,EAAE;AAC5B,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;;;;AAMhC,IAAA,IACI,IAAI,GAAA;QACN,OAAO,IAAI,CAAC,KAAK;;IAEnB,IAAI,IAAI,CAAC,CAAS,EAAA;AAChB,QAAA,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;AACtC,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;AACvB,YAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;;;IAGlB,KAAK,GAAW,CAAC;AAEjB,IAAA,WAAW,CAAC,IAAY,EAAA;AAC9B,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI;AACjB,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,IAAI,CAAC,mBAAmB,EAAE;QACpE,IAAI,CAAC,qBAAqB,EAAE;;IAGtB,gBAAgB,GAAA;QACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAyB;QACtE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAyB;AAE1E,QAAA,MAAM,WAAW,GAAG,QAAQ,CAAC,KAAK;AAClC,QAAA,MAAM,aAAa,GAAG,UAAU,CAAC,KAAK;AAEtC,QAAA,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK;AAEvC,QAAA,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI;AACxB,QAAA,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI;AAE1B,QAAA,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK;AAC1B,QAAA,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK;AAE5B,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACzB,YAAA,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;AAC/B,YAAA,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK;;AAGrC,QAAA,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,KAAK,CAAC;AACpD,QAAA,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC;QAEpD,UAAU,CAAC,oBAAoB,EAAE;QACjC,QAAQ,CAAC,oBAAoB,EAAE;QAE/B,QAAQ,CAAC,KAAK,GAAG;cACb,IAAI,CAAC,+BAA+B,CAAC,UAAU,EAAE,QAAQ;cACzD,IAAI,CAAC,+BAA+B,CAAC,QAAQ,EAAE,UAAU,CAAC;AAE9D,QAAA,IAAI,WAAW,KAAK,QAAQ,CAAC,KAAK,EAAE;AAClC,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;;AAG/B,QAAA,IAAI,aAAa,KAAK,UAAU,CAAC,KAAK,EAAE;AACtC,YAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;;;IAI3B,mBAAmB,GAAA;QACzB,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC;QAC3C,IAAI,KAAK,EAAE;AACT,YAAA,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK;AAE5B,YAAA,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK;AACvB,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACzB,gBAAA,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;;YAG3B,KAAK,CAAC,qBAAqB,EAAE;AAE7B,YAAA,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK,EAAE;AAC5B,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC;;;;AAKhC;;;;AAIG;IACM,WAAW,GAA8B,CAAC,KAAa,KAAK,CAAA,EAAG,KAAK,CAAA,CAAE;;AAG/E,IAAA,UAAU;;IAGV,eAAe,GAAG,mBAAmB,EAAE;;AAG/B,IAAA,sBAAsB;;AAGtB,IAAA,eAAe;;AAIvB,IAAA,YAAY;AACZ,IAAA,WAAW;IAEX,aAAa,GAAW,EAAE;;;IAKhB,uBAAuB,GAAW,EAAE;;IAGpC,qBAAqB,GAAW,EAAE;;AAI5C,IAAA,kBAAkB;AAClB,IAAA,oBAAoB;IAEpB,QAAQ,GAAY,KAAK;;IAGzB,MAAM,GAAY,KAAK;IAEf,mBAAmB,GAAY,KAAK;AAE5C;;;AAGG;IACH,mBAAmB,GAAW,CAAC;IAE/B,aAAa,GAAY,KAAK;IAEtB,YAAY,GAAyC,IAAI;AAEzD,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAIpC,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAE5D,QAAA,IAAI,IAAI,CAAC,IAAI,EAAE;AACb,YAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YACnF,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK;;;;IAK3C,WAAW,GAAW,CAAC;AAEvB,IAAA,aAAa;IAEb,eAAe,GAAA;AACb,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC5B,IAAI,CAAC,iBAAiB,EAAE;;QAG1B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC;QAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC;QAC9C,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM;AACpC,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AAEzB,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,eAAe,CACb,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,EAC7B,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAChC;;QAGH,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC;QAC3C,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM;QACzC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW;AAE1D,QAAA,IAAI,CAAC;cACD,IAAI,CAAC,YAAY,CAAC,MAA8B,EAAE,MAA8B;AAClF,cAAE,IAAI,CAAC,eAAe,CAAC,MAAO,CAAC;AAEjC,QAAA,IAAI,CAAC,cAAc,CAAC,MAAO,CAAC;QAC5B,IAAI,CAAC,iBAAiB,EAAE;QACxB,IAAI,CAAC,sBAAsB,EAAE;QAE7B,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;;AAGnB,IAAA,eAAe,CAAC,MAAuB,EAAA;QAC7C,MAAM,CAAC,SAAS,EAAE;QAClB,MAAM,CAAC,MAAM,EAAE;AAEf,QAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;AAEpC,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;QAC/B,MAAM,CAAC,qBAAqB,EAAE;;IAGxB,YAAY,CAAC,MAA4B,EAAE,MAA4B,EAAA;QAC7E,MAAM,CAAC,SAAS,EAAE;QAClB,MAAM,CAAC,MAAM,EAAE;QAEf,MAAM,CAAC,SAAS,EAAE;QAClB,MAAM,CAAC,MAAM,EAAE;QAEf,MAAM,CAAC,aAAa,EAAE;QACtB,MAAM,CAAC,aAAa,EAAE;QAEtB,MAAM,CAAC,mBAAmB,EAAE;QAC5B,MAAM,CAAC,mBAAmB,EAAE;QAE5B,IAAI,CAAC,wBAAwB,EAAE;AAE/B,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;QAE/B,MAAM,CAAC,qBAAqB,EAAE;QAC9B,MAAM,CAAC,qBAAqB,EAAE;;IAGhC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;AACzC,QAAA,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE;AAClC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI;;;IAIrB,YAAY,GAAA;QAClB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK,KAAK,KAAK;AACxC,QAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,oBAAoB,EAAE;QACtE,IAAI,CAAC,iBAAiB,EAAE;;IAGlB,iBAAiB,GAAA;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAyB;QACtE,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAyB;QAE1E,QAAQ,CAAC,eAAe,EAAE;QAC1B,UAAU,CAAC,eAAe,EAAE;AAE5B,QAAA,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC,sBAAsB,EAAE;AACvD,QAAA,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,sBAAsB,EAAE;QAE3D,QAAQ,CAAC,mBAAmB,EAAE;QAC9B,UAAU,CAAC,mBAAmB,EAAE;QAEhC,QAAQ,CAAC,oBAAoB,EAAE;QAC/B,UAAU,CAAC,oBAAoB,EAAE;QAEjC,QAAQ,CAAC,qBAAqB,EAAE;QAChC,UAAU,CAAC,qBAAqB,EAAE;;IAG5B,oBAAoB,GAAA;QAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAE;QAC5C,KAAK,CAAC,qBAAqB,EAAE;;;IAIvB,kBAAkB,GAAA;QACxB,IAAI,OAAO,cAAc,KAAK,WAAW,IAAI,CAAC,cAAc,EAAE;YAC5D;;AAGF,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,CAAC,eAAe,GAAG,IAAI,cAAc,CAAC,MAAK;AAC7C,gBAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;oBACpB;;AAEF,gBAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,oBAAA,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC;;gBAEjC,IAAI,CAAC,SAAS,EAAE;AAClB,aAAC,CAAC;YACF,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC9D,SAAC,CAAC;;;IAII,SAAS,GAAA;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,SAAS;;AAGrF,IAAA,SAAS,CAAC,aAAA,GAA2B,SAAS,CAAC,GAAG,EAAA;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,IAAI,CAAC,GAAG;;QAEjB,OAAO,KAAK,CAAC,KAAK;;IAGZ,WAAW,GAAA;QACjB,OAAO,CAAC,EACN,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,aAAa,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,aAAa,CAC/F;;;IAIH,iBAAiB,GAAA;QACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW;AAC9D,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,IAAI;;;AAIhF,IAAA,qBAAqB,CAAC,MAKrB,EAAA;QACC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK;AAExD,QAAA,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;AAC7B,QAAA,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK;AAC/B,QAAA,UAAU,CAAC,eAAe,GAAG,MAAM,CAAC,eAAe;AACnD,QAAA,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;;;AAIzC,IAAA,sBAAsB,CAAC,KAAa,EAAA;;AAElC,QAAA,MAAM,MAAM,GAAG,KAAK,IAAI,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAChF,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;QACxE,OAAO,CAAA,WAAA,EAAc,UAAU,CAAA,EAAA,CAAI;;;AAKrC,IAAA,mBAAmB,CAAC,MAAuB,EAAA;AACzC,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B;;AAGF,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;AAC3B,QAAA,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;AAC3B,QAAA,IAAI,CAAC,yBAAyB,CAAC,MAA8B,CAAC;;IAGhE,+BAA+B,CAC7B,MAA4B,EAC5B,MAA4B,EAAA;AAE5B,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B;;QAGF,MAAM,CAAC,qBAAqB,EAAE;QAC9B,MAAM,CAAC,qBAAqB,EAAE;;AAGhC,IAAA,cAAc,CAAC,MAAuB,EAAA;AACpC,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B;;AAGF,QAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;QACpC,IAAI,CAAC,iBAAiB,EAAE;AACxB,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;;IAG3B,qBAAqB,GAAA;AACnB,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B;;QAGF,IAAI,CAAC,iBAAiB,EAAE;QACxB,IAAI,CAAC,sBAAsB,EAAE;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;;IAG1B,SAAS,GAAA;AACP,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC7B;;QAGF,IAAI,CAAC,iBAAiB,EAAE;AACxB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAyB;YACpE,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAyB;YAEtE,MAAM,CAAC,qBAAqB,EAAE;YAC9B,MAAM,CAAC,qBAAqB,EAAE;YAE9B,MAAM,CAAC,mBAAmB,EAAE;YAC5B,MAAM,CAAC,mBAAmB,EAAE;YAE5B,MAAM,CAAC,aAAa,EAAE;YACtB,MAAM,CAAC,aAAa,EAAE;YAEtB,MAAM,CAAC,oBAAoB,EAAE;YAC7B,MAAM,CAAC,oBAAoB,EAAE;;aACxB;YACL,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC;YAC5C,IAAI,MAAM,EAAE;gBACV,MAAM,CAAC,qBAAqB,EAAE;;;QAIlC,IAAI,CAAC,iBAAiB,EAAE;QACxB,IAAI,CAAC,sBAAsB,EAAE;AAC7B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;;;IAInB,cAAc,GAAY,KAAK;;IAG/B,qBAAqB,GAAA;QAC3B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC;QAClD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC;AAC9C,QAAA,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,EAAE;AAC5B,YAAA,OAAO,KAAK;;QAEd,OAAO,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,GAAG,EAAE;;AAGzD;;;AAGG;AACK,IAAA,iCAAiC,CAAC,MAA4B,EAAA;AACpE,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAG;QACpC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC;QACxD,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC;QAC1D,YAAY,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,wBAAwB,CAAC;AACpE,QAAA,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,wBAAwB,EAAE,IAAI,CAAC,cAAc,CAAC;;;AAIlF,IAAA,yBAAyB,CAAC,MAA4B,EAAA;QAC5D,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACxC;;QAEF,IAAI,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,qBAAqB,EAAE,EAAE;AACxD,YAAA,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,cAAc;AAC1C,YAAA,IAAI,CAAC,iCAAiC,CAAC,MAAM,CAAC;;;;;;;;;;AAYlD,IAAA,cAAc,CAAC,MAAuB,EAAA;AACpC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB;;QAEF,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAC1B,MAAM,CAAC,aAAa,KAAK,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK,CACxE;AACF,QAAA,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,GAAG,CAAA,WAAA,EAAc,MAAM,CAAC,UAAU,CAAA,GAAA,CAAK;;;;;;;;;AAW3E,IAAA,uBAAuB,CAAC,MAAuB,EAAA;AAC7C,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB;;QAGF,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;AAEhD,QAAA,IAAI,CAAC;cACD,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS;cAC/B,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,gBAAgB,EAAE,SAAS,CAAC;AAEjE,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,MAAM,CAAC,aAAa,KAAK,SAAS,CAAC;AACjC,mBAAG,IAAI,CAAC,uBAAuB,GAAG,SAAS;mBACxC,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC;YAE5C,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC;YACxD,SAAS,CAAC,MAAM,GAAG;kBACf,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,gCAAgC;kBACvE,WAAW,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,gCAAgC,CAAC;;;;IAK3E,wBAAwB,GAAA;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC;QAC5C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC;QAE9C,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;;QAEtC,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC;;;;;;;;;;;;;IAehC,sBAAsB,GAAA;QAC5B,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YAC7C;;QAGF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;AAC1D,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI;AACnD,QAAA,MAAM,UAAU,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AAChE,QAAA,IAAI,CAAC,mBAAmB,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,UAAU;;;;;;;;;;;;;;;;AAkBjE,IAAA,cAAc,CAAC,MAAuB,EAAA;AACpC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB;;AAGF,QAAA,IAAI,CAAC;AACH,cAAE,IAAI,CAAC,mBAAmB,CAAC,MAA8B;AACzD,cAAE,IAAI,CAAC,sBAAsB,CAAC,MAAyB,CAAC;;AAGpD,IAAA,mBAAmB,CAAC,MAA4B,EAAA;AACtD,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,EAAE;QACnC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YAClC;;AAGF,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,YAAY;QAE7F,IAAI,MAAM,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,EAAE;YAC5C,IAAI,CAAC,qBAAqB,CAAC;AACzB,gBAAA,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,GAAG,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,UAAU,CAAI,EAAA,CAAA;AACpD,gBAAA,eAAe,EAAE,OAAO;gBACxB,SAAS,EAAE,CAAU,OAAA,EAAA,gBAAgB,CAAG,CAAA,CAAA;AACzC,aAAA,CAAC;;aACG;YACL,IAAI,CAAC,qBAAqB,CAAC;AACzB,gBAAA,IAAI,EAAE,CAAA,EAAG,OAAO,CAAC,UAAU,CAAI,EAAA,CAAA;AAC/B,gBAAA,KAAK,EAAE,MAAM;AACb,gBAAA,eAAe,EAAE,MAAM;gBACvB,SAAS,EAAE,CAAU,OAAA,EAAA,gBAAgB,CAAG,CAAA,CAAA;AACzC,aAAA,CAAC;;;AAIE,IAAA,sBAAsB,CAAC,MAAuB,EAAA;AACpD,QAAA,IAAI,CAAC;AACH,cAAE,IAAI,CAAC,qBAAqB,CAAC;AACzB,gBAAA,IAAI,EAAE,MAAM;AACZ,gBAAA,KAAK,EAAE,KAAK;AACZ,gBAAA,eAAe,EAAE,OAAO;AACxB,gBAAA,SAAS,EAAE,CAAU,OAAA,EAAA,CAAC,GAAG,MAAM,CAAC,cAAc,CAAG,CAAA,CAAA;aAClD;AACH,cAAE,IAAI,CAAC,qBAAqB,CAAC;AACzB,gBAAA,IAAI,EAAE,KAAK;AACX,gBAAA,KAAK,EAAE,MAAM;AACb,gBAAA,eAAe,EAAE,MAAM;AACvB,gBAAA,SAAS,EAAE,CAAA,OAAA,EAAU,MAAM,CAAC,cAAc,CAAG,CAAA,CAAA;AAC9C,aAAA,CAAC;;;;;;;;;;IAYR,iBAAiB,GAAA;QACf,IACE,CAAC,IAAI,CAAC,aAAa;YACnB,IAAI,CAAC,IAAI,KAAK,SAAS;YACvB,IAAI,CAAC,GAAG,KAAK,SAAS;AACtB,YAAA,IAAI,CAAC,GAAG,KAAK,SAAS,EACtB;YACA;;AAEF,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC;QAC1C,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC;;AAGlF,IAAA,yBAAyB,CAAC,IAAY,EAAA;AAC5C,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,EAAE;QAC9B,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;QACtE,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC;AACxE,QAAA,IAAI,CAAC,MAAM,GAAG,SAAS,EAAE,GAAG,WAAW,EAAE;AAEzC,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS;AAC9B,aAAA,IAAI,CAAC,YAAY,CAAC,MAAM;AACxB,aAAA,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;;AAGnD,IAAA,sBAAsB,CAAC,IAAY,EAAA;AACzC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,EAAE;QACjC,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC;QAElD,MAAM,2BAA2B,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;QAC3F,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,UAAU,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC7E,MAAM,wBAAwB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,QAAQ,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AACtF,QAAA,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,2BAA2B;AAChD,aAAA,IAAI,CAAC,YAAY,CAAC,QAAQ;aAC1B,MAAM,CACL,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAC1C,KAAK,CAAC,wBAAwB,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAC5D;;;AAIL,IAAA,SAAS,CAAC,aAAwB,EAAA;QAChC,IAAI,aAAa,KAAK,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE;YAClD,OAAO,IAAI,CAAC,MAAM;;AAEpB,QAAA,IAAI,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE;YACxB,OAAO,aAAa,KAAK,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI;;QAEnF;;;AAIF,IAAA,SAAS,CAAC,aAAwB,EAAA;QAChC,OAAO,aAAa,KAAK,SAAS,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,IAAK,GAAG,IAAI,CAAC,OAAO,EAAE,KAAM;;AAGrF,IAAA,cAAc,CAAC,aAAsB,EAAA;AACnC,QAAA,IAAI,CAAC,aAAa,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe;AAClF,QAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS,CAAC,MAAM,CAC7C,+BAA+B,EAC/B,IAAI,CAAC,aAAa,CACnB;;;IAIH,sBAAsB,CAAC,KAAmB,EAAE,IAAa,EAAA;AACvD,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC;AAC7B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM;AAC/B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,GAAG,MAAM;AAC/B,QAAA,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO;AAClC,QAAA,MAAM,EAAE,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO;QAClC,OAAO,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;;uGAz1BrD,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,EAuBD,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,CAmBhB,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,gBAAgB,qDAWhB,gBAAgB,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAIhB,eAAe,CAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAuBf,gBAAgB,CAqDhB,EAAA,GAAA,EAAA,CAAA,KAAA,EAAA,KAAA,EAAA,eAAe,CA8Df,EAAA,IAAA,EAAA,CAAA,MAAA,EAAA,MAAA,EAAA,eAAe,iYAtMvB,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAC,CAAC,EAAA,OAAA,EAAA,CAAA,EAAA,YAAA,EAAA,QAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAmB5C,gBAAgB,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAGb,sBAAsB,EANzB,CAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,aAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,EAAA,EAAA,YAAA,EAAA,SAAA,EAAA,SAAA,EAAA,uBAAuB,EC7FvC,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,2iCAoCA,6rSD0CY,oBAAoB,EAAA,QAAA,EAAA,yBAAA,EAAA,MAAA,EAAA,CAAA,UAAA,EAAA,eAAA,EAAA,oBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEnB,SAAS,EAAA,UAAA,EAAA,CAAA;kBAnBrB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,YAAY,EAGhB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,2BAA2B;AACpC,wBAAA,SAAS,EAAE,+BAA+B;AAC1C,wBAAA,2BAA2B,EAAE,UAAU;AACvC,wBAAA,8BAA8B,EAAE,UAAU;AAC1C,wBAAA,8BAA8B,EAAE,UAAU;AAC1C,wBAAA,gCAAgC,EAAE,eAAe;AACjD,wBAAA,iCAAiC,EAAE,iBAAiB;qBACrD,EACS,QAAA,EAAA,WAAW,mBACJ,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAC1B,SAAA,EAAA,CAAC,EAAC,OAAO,EAAE,UAAU,EAAE,WAAW,EAAA,SAAW,EAAC,CAAC,EAAA,OAAA,EACjD,CAAC,oBAAoB,CAAC,EAAA,QAAA,EAAA,2iCAAA,EAAA,MAAA,EAAA,CAAA,qoSAAA,CAAA,EAAA;wDAYL,YAAY,EAAA,CAAA;sBAArC,SAAS;uBAAC,aAAa;gBAGe,OAAO,EAAA,CAAA;sBAA7C,YAAY;uBAAC,uBAAuB;gBAGL,MAAM,EAAA,CAAA;sBAArC,YAAY;uBAAC,gBAAgB;gBAI9B,OAAO,EAAA,CAAA;sBADN,eAAe;AAAC,gBAAA,IAAA,EAAA,CAAA,sBAAsB,EAAE,EAAC,WAAW,EAAE,KAAK,EAAC;gBAKzD,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAoBhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAYpC,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKhC,GAAG,EAAA,CAAA;sBADN,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAoBnC,KAAK,EAAA,CAAA;sBADJ;gBAKD,aAAa,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAsDhC,GAAG,EAAA,CAAA;sBADN,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBA+D/B,IAAI,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAgF1B,WAAW,EAAA,CAAA;sBAAnB;;AA0kBH;AACA,SAAS,eAAe,CACtB,OAAgB,EAChB,eAAmE,EACnE,iBAA8C,EAAA;AAE9C,IAAA,MAAM,UAAU,GACd,CAAC,OAAO,IAAI,iBAAiB,EAAE,YAAY,CAAC,YAAY,CAAC,qBAAqB,CAAC;AACjF,IAAA,MAAM,QAAQ,GAAG,eAAe,EAAE,YAAY,CAAC,YAAY,CACzD,OAAO,GAAG,mBAAmB,GAAG,gBAAgB,CACjD;AAED,IAAA,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,EAAE;AAC5B,QAAA,oCAAoC,EAAE;;AAE1C;AAEA,SAAS,oCAAoC,GAAA;AAC3C,IAAA,MAAM,KAAK,CAAC,CAAA;;;;;;;;;;;;;;AAcV,GAAA,CAAA,CAAC;AACL;;AEx6BA;;;AAGG;AACI,MAAM,+BAA+B,GAAQ;AAClD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,cAAc,CAAC;AAC7C,IAAA,KAAK,EAAE,IAAI;CACZ;AAED;;;AAGG;AACI,MAAM,qCAAqC,GAAQ;AACxD,IAAA,OAAO,EAAE,iBAAiB;AAC1B,IAAA,WAAW,EAAE,UAAU,CAAC,MAAM,mBAAmB,CAAC;AAClD,IAAA,KAAK,EAAE,IAAI;CACZ;AAED;;;;;;;AAOG;MAoBU,cAAc,CAAA;AAChB,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,WAAW,GAAG,MAAM,CAA+B,UAAU,CAAC;AAC9D,IAAA,IAAI,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC/B,IAAA,OAAO,GAAG,MAAM,CAAa,UAAU,CAAC;AAC1C,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,iBAAiB;AAEzB,IAAA,IACI,KAAK,GAAA;QACP,OAAO,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;;IAEpD,IAAI,KAAK,CAAC,KAAa,EAAA;AACrB,QAAA,IAAI,KAAK,KAAK,IAAI,EAAE;AAClB,YAAA,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE;;AAEjC,QAAA,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK;AAChC,QAAA,MAAM,WAAW,GAAG,KAAK,GAAG,EAAE;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AAC7B,YAAA,IAAI,CAAC,aAAa,GAAG,WAAW;YAChC;;AAEF,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB;;AAEF,QAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;;AAG7B;;;AAGG;AACO,IAAA,SAAS,CAAC,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK;QAC/B,IAAI,CAAC,qBAAqB,EAAE;AAC5B,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;AACjC,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACzB,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,EAAE;;;AAIf,IAAA,WAAW,GAAyB,IAAI,YAAY,EAAU;;AAG9D,IAAA,SAAS,GAC1B,IAAI,YAAY,EAAsB;;AAGrB,IAAA,OAAO,GACxB,IAAI,YAAY,EAAsB;AAExC;;;AAGG;AACH,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;AACxC,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe;YACvC,OAAO,IAAI,CAAC,WAAW;;AAEzB,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;AAClC,YAAA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,EAAE;;QAElD,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,CAAS,EAAA;AACtB,QAAA,IAAI,CAAC,WAAW,GAAG,CAAC;;AAEd,IAAA,WAAW;AAEnB;;;AAGG;AACH,IAAA,aAAa,GAAc,SAAS,CAAC,GAAG;;AAGxC,IAAA,IAAI,GAAG,GAAA;QACL,OAAO,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;;IAElD,IAAI,GAAG,CAAC,CAAS,EAAA;QACf,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;;;AAI3B,IAAA,IAAI,GAAG,GAAA;QACL,OAAO,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;;IAElD,IAAI,GAAG,CAAC,CAAS,EAAA;QACf,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,EAAE;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;;AAG3B,IAAA,IAAI,IAAI,GAAA;QACN,OAAO,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;;IAEnD,IAAI,IAAI,CAAC,CAAS,EAAA;QAChB,IAAI,CAAC,YAAY,CAAC,IAAI,GAAG,CAAC,GAAG,EAAE;AAC/B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;;;AAI3B,IAAA,IAAI,QAAQ,GAAA;QACV,OAAO,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC;;IAErD,IAAI,QAAQ,CAAC,CAAU,EAAA;AACrB,QAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,CAAC;AAC9B,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;QAEzB,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE;YAC3C,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ;;;;AAKzC,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE;AACxC,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC;;QAEpC,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;;;AAIhF,IAAA,IAAI,cAAc,GAAA;AAChB,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC;;AAEpC,QAAA,IAAI,IAAI,CAAC,WAAW,KAAK,CAAC,EAAE;AAC1B,YAAA,OAAO,CAAC;;QAEV,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY;;;AAIpD,IAAA,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;;AAG7C,IAAA,UAAU,GAAG,MAAM,CAAC,EAAE,CAAC;;IAGvB,WAAW,GAAW,CAAC;;IAGvB,eAAe,GAAG,CAAC;;IAGnB,SAAS,GAAY,KAAK;;IAG1B,UAAU,GAAY,KAAK;;AAGnB,IAAA,aAAa,CAAC,CAAU,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,CAAC;;AAGrB;;;;;AAKG;IACK,mBAAmB,GAAY,KAAK;;AAG5C,IAAA,aAAa;;AAGL,IAAA,YAAY;;AAGD,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;AAEnD;;;;;AAKG;IACH,aAAa,GAAY,KAAK;;AAGpB,IAAA,WAAW;;AAGb,IAAA,YAAY,GAAe,MAAK,GAAG;AAE3C;;;;;;;AAOG;IACO,qBAAqB,GAAG,KAAK;AAIvC,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC;AAElC,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;YAClC,IAAI,CAAC,iBAAiB,GAAG;AACvB,gBAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjF,gBAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjF,gBAAA,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC9E;AACH,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;AACpD,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAC1B,QAAA,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;AACzB,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;;;IAIzB,SAAS,GAAA;QACP,IAAI,CAAC,oBAAoB,EAAE;;QAG3B,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;;AAE3C,YAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI;;QAG9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI;QAC7B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG;QAC3B,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG;QAC3B,IAAI,CAAC,UAAU,EAAE;;;IAInB,MAAM,GAAA;QACJ,IAAI,CAAC,qBAAqB,EAAE;;IAG9B,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;AAC/B,QAAA,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE;AACpC,YAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,EAAE;;aAC/B;YACL,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa;YAC5C,IAAI,CAAC,qBAAqB,EAAE;AAC5B,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;AACjC,YAAA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;;;IAI7B,gBAAgB,GAAA;QACd,OAAO,IAAI,CAAC,GAAG;;IAGjB,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,YAAY,EAAE;;IAGrB,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC;AAClC,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;AACjC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;;IAG1B,SAAS,GAAA;QACP,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;;;AAGjC,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,qBAAqB,CAAC,EAAC,aAAa,EAAE,IAAI,EAAC,CAAC;;;IAIrD,QAAQ,GAAA;QACN,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC;;;QAG9B,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACxC,IAAI,CAAC,qBAAqB,CAAC,EAAC,aAAa,EAAE,IAAI,EAAC,CAAC;;AAEnD,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;;IAGnC,uBAAuB,GAAA;;;QAGrB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACvC,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;YACjC,IAAI,CAAC,qBAAqB,EAAE;;QAE9B,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAa,CAAC,QAAQ;;AAGrD,IAAA,cAAc,CAAC,KAAmB,EAAA;QAChC,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC;;;;AAKF,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE;YACtB,MAAM,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAC/D,KAAK,EACL,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,YAAY,CAAC,qBAAqB,EAAE,CAChF;AAED,YAAA,IAAI,CAAC,SAAS,GAAG,qBAAqB;YACtC,IAAI,CAAC,kBAAkB,EAAE;AACzB,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAChC;;AAGF,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;AACrB,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;QACxB,IAAI,CAAC,kBAAkB,EAAE;AACzB,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE;;;AAIhC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;YACtB,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,EAAC,aAAa,EAAE,IAAI,EAAC,CAAC;;AAGjE,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC;YAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAC,CAAC;;;AAIhF;;;;;AAKG;AACK,IAAA,sBAAsB,CAAC,KAAmB,EAAA;;;;;AAKhD,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI;;;;;;QAOzB,UAAU,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC1B,YAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;SACtB,EAAE,CAAC,CAAC;;;AAIP,IAAA,SAAS,CAAC,KAAmB,EAAA;QAC3B,MAAM,IAAI,GAAG,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;AACrD,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY;QACvC,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI;QAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,CAAC;QACzE,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK;;AAGxE,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,GAAG,QAAQ;QAEpE,MAAM,cAAc,GAClB,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG;AAC5E,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,IAAI;AACtD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK;AAE5B,QAAA,IAAI,KAAK,KAAK,SAAS,EAAE;;;;AAIvB,YAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;AACjC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;AAClB,kBAAE,IAAI,CAAC,qBAAqB;AAC5B,kBAAE,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,EAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAC,CAAC;YACzF;;AAGF,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;QAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;QACjC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC;AAC9B,QAAA,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC;AACjC,QAAA,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG;AAClB,cAAE,IAAI,CAAC,qBAAqB;AAC5B,cAAE,IAAI,CAAC,4BAA4B,CAAC,KAAK,EAAE,EAAC,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,aAAa,EAAC,CAAC;;AAG3F,IAAA,cAAc,CAAC,KAAmB,EAAA;;;QAGhC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AACxC,YAAA,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC;;;IAI5C,YAAY,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;AACzB,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;;YAE3B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAC,CAAC;;;;;YAM1E,UAAU,CAAC,MAAM,IAAI,CAAC,oBAAoB,EAAE,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC;;;AAI9E,IAAA,MAAM,CAAC,CAAS,EAAA;AACd,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,eAAe;QAChC,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe;AAC5D,QAAA,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;;IAGxC,sBAAsB,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACvB,QACE,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;gBAC9E,IAAI,CAAC,eAAe;;AAGxB,QAAA,QACE,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;YACxE,IAAI,CAAC,eAAe;;AAIxB,IAAA,6BAA6B,CAAC,KAAmB,EAAA;QAC/C,OAAO,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;;AAGjD;;;AAGG;AACH,IAAA,kBAAkB;AAElB;;;AAGG;IACH,oBAAoB,GAAA;AAClB,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG,CAAK,EAAA,EAAA,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI;QACrE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,eAC9B,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,GAAG,CACtD,KAAK;AACL,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,CAAI,CAAA,EAAA,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,IAAI;;AAG1F,IAAA,qBAAqB,CAAC,OAAkC,EAAA;AACtD,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC;AAC5D,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;;IAG9B,4BAA4B,CAAC,KAAmB,EAAE,OAAkC,EAAA;AAClF,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,KAAK,CAAC,CAAC;AACxE,QAAA,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;;AAG9B,IAAA,cAAc,CAAC,OAAkC,EAAA;QAC/C,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,CAAC,OAAO,EAAE,aAAa,CAAC;AACrD,QAAA,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC;;AAGxC;;;;AAIG;AACH,IAAA,UAAU,CAAC,KAAU,EAAA;QACnB,IAAI,IAAI,CAAC,qBAAqB,IAAI,KAAK,KAAK,IAAI,EAAE;AAChD,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;;AAItB;;;;AAIG;AACH,IAAA,gBAAgB,CAAC,EAAO,EAAA;AACtB,QAAA,IAAI,CAAC,WAAW,GAAG,EAAE;AACrB,QAAA,IAAI,CAAC,qBAAqB,GAAG,IAAI;;AAGnC;;;;AAIG;AACH,IAAA,iBAAiB,CAAC,EAAO,EAAA;AACvB,QAAA,IAAI,CAAC,YAAY,GAAG,EAAE;;AAGxB;;;;AAIG;AACH,IAAA,gBAAgB,CAAC,UAAmB,EAAA;AAClC,QAAA,IAAI,CAAC,QAAQ,GAAG,UAAU;;IAG5B,KAAK,GAAA;AACH,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;;IAG3B,IAAI,GAAA;AACF,QAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;;uGArgBf,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EAQN,eAAe,CAbvB,EAAA,EAAA,OAAA,EAAA,EAAA,WAAA,EAAA,aAAA,EAAA,SAAA,EAAA,WAAA,EAAA,OAAA,EAAA,SAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,OAAA,EAAA,EAAA,SAAA,EAAA,EAAA,QAAA,EAAA,aAAA,EAAA,OAAA,EAAA,YAAA,EAAA,MAAA,EAAA,WAAA,EAAA,OAAA,EAAA,YAAA,EAAA,EAAA,UAAA,EAAA,EAAA,qBAAA,EAAA,cAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,SAAA,EAAA;YACT,+BAA+B;AAC/B,YAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAC;AACzD,SAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEU,cAAc,EAAA,UAAA,EAAA,CAAA;kBAnB1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,uBAAuB;AACjC,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,mBAAmB;AAC5B,wBAAA,MAAM,EAAE,OAAO;AACf,wBAAA,uBAAuB,EAAE,cAAc;AACvC,wBAAA,UAAU,EAAE,aAAa;AACzB,wBAAA,SAAS,EAAE,YAAY;;;AAGvB,wBAAA,QAAQ,EAAE,WAAW;AACrB,wBAAA,SAAS,EAAE,YAAY;AACxB,qBAAA;AACD,oBAAA,SAAS,EAAE;wBACT,+BAA+B;AAC/B,wBAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,gBAAgB,EAAC;AACzD,qBAAA;AACF,iBAAA;wDAUK,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAiChB,WAAW,EAAA,CAAA;sBAA7B;gBAGkB,SAAS,EAAA,CAAA;sBAA3B;gBAIkB,OAAO,EAAA,CAAA;sBAAzB;;AAieG,MAAO,mBAAoB,SAAQ,cAAc,CAAA;AACnC,IAAA,IAAI,GAAG,MAAM,CAAC,iBAAiB,CAAC;;IAGlD,UAAU,GAAA;AACR,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,CAE5E;;QAEf,OAAO,IAAI,CAAC,QAAQ;;AAEd,IAAA,QAAQ;AAEhB;;;AAGG;IACH,SAAS,GAAA;AACP,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE;AACjC,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,OAAO,EAAE;YACjC,OAAO,OAAO,CAAC,UAAU;;QAE3B,OAAO,IAAI,CAAC,eAAe;;AAG7B;;;AAGG;IACH,SAAS,GAAA;AACP,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE;AACjC,QAAA,IAAI,IAAI,CAAC,YAAY,IAAI,OAAO,EAAE;YAChC,OAAO,OAAO,CAAC,UAAU;;QAE3B,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe;;IAGzD,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,YAAY;YACf,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;;;AAI5F,IAAA,YAAY;;AAGZ,IAAA,WAAW;AAIX,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;QAEP,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,mBAAmB,CAAC;QACtE,IAAI,CAAC,eAAe,EAAE;AACtB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,KAAK;;IAGhE,gBAAgB,GAAA;QACvB,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;;IAG/D,QAAQ,GAAA;QACf,KAAK,CAAC,QAAQ,EAAE;QAChB,IAAI,CAAC,cAAc,EAAE;AACrB,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,CAAC,oBAAoB,EAAE;;;IAItB,uBAAuB,GAAA;QAC9B,KAAK,CAAC,uBAAuB,EAAE;AAC/B,QAAA,IAAI,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE;;AAG3B,IAAA,cAAc,CAAC,KAAmB,EAAA;QACzC,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACvC;;AAEF,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE;YAClC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,wCAAwC,CAAC;;AAEpF,QAAA,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC;;IAGpB,YAAY,GAAA;QACnB,KAAK,CAAC,YAAY,EAAE;AACpB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,UAAU,CAAC,MAAK;AACd,gBAAA,IAAI,CAAC,QAAS,CAAC,oBAAoB,EAAE;gBACrC,IAAI,CAAC,QAAS,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,wCAAwC,CAAC;AACxF,aAAC,CAAC;;;AAIG,IAAA,cAAc,CAAC,KAAmB,EAAA;AACzC,QAAA,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC;QAC3B,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;YACxC,IAAI,CAAC,cAAc,EAAE;;;AAIhB,IAAA,SAAS,CAAC,KAAmB,EAAA;AACpC,QAAA,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;AACtB,QAAA,IAAI,CAAC,QAAQ,EAAE,aAAa,EAAE;;AAGvB,IAAA,MAAM,CAAC,CAAS,EAAA;QACvB,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC;;IAGlE,aAAa,GAAA;AACX,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE;QACjC,IAAI,CAAC,OAAO,EAAE;YACZ;;AAEF,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC;YACpD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG;;aACtB;YACL,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG;AAC3B,YAAA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,CAAC;;;IAI/C,kBAAkB,GAAA;AACzB,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC;QAChF,MAAM,QAAQ,GACZ,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC;AAC9F,QAAA,MAAM,UAAU,GACd,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;cAC5B,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG;cAC5D,CAAC;AACP,QAAA,MAAM,KAAK,GAAG,QAAQ,GAAG,UAAU,GAAG,QAAQ;QAC9C,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,KAAK,CAAA,EAAA,CAAI;AAC5C,QAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG,CAAK,EAAA,EAAA,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI;;IAG9D,oBAAoB,GAAA;AAC3B,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE;QACjC,IAAI,CAAC,OAAO,EAAE;YACZ;;AAEF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC;AACrE,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC;AACpB,cAAE,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI;AAC9C,cAAE,IAAI,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC;AAEjD,QAAA,MAAM,WAAW,GAAG,IAAI,CAAC;cACrB,CAAC,IAAI,CAAC,GAAG,GAAG,QAAQ,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG;cAC5D,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAEjE,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,WAAW,GAAG,CAAC;;AAGxE,QAAA,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa;;;;AAK9C,QAAA,IAAI,UAAU,KAAK,CAAC,EAAE;YACpB,aAAa,GAAG,EAAE;;AACb,aAAA,IAAI,UAAU,KAAK,CAAC,EAAE;YAC3B,aAAa,GAAG,CAAC;;AAGnB,QAAA,MAAM,KAAK,GAAG,QAAQ,GAAG,UAAU,GAAG,aAAa;QACnD,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAA,EAAG,KAAK,CAAA,EAAA,CAAI;QAC5C,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK;AAEvC,QAAA,IAAI,IAAI,CAAC,YAAY,EAAE;AACrB,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,CAAI,CAAA,EAAA,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,IAAI;YACxF,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM;;aACjC;YACL,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM;AACrC,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,KAAK,GAAG,CAAI,CAAA,EAAA,IAAI,CAAC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,IAAI;;;IAI7F,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,yBAAyB,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC;;IAG3E,cAAc,GAAA;AACpB,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE;QACjC,IAAI,CAAC,OAAO,EAAE;YACZ;;QAEF,OAAO,CAAC,aAAa,EAAE;AACvB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,OAAO,CAAC,kBAAkB,EAAE;;aACvB;YACL,OAAO,CAAC,oBAAoB,EAAE;;;AAIlC;;;;AAIG;AACM,IAAA,UAAU,CAAC,KAAU,EAAA;QAC5B,IAAI,IAAI,CAAC,qBAAqB,IAAI,KAAK,KAAK,IAAI,EAAE;AAChD,YAAA,IAAI,CAAC,KAAK,GAAG,KAAK;YAClB,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,cAAc,EAAE;;;AAIhB,IAAA,SAAS,CAAC,KAAa,EAAA;AAC9B,QAAA,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC;QACtB,IAAI,CAAC,oBAAoB,EAAE;QAC3B,IAAI,CAAC,cAAc,EAAE;;uGAtNZ,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,mBAAmB,EALnB,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sDAAA,EAAA,SAAA,EAAA;YACT,qCAAqC;AACrC,YAAA,EAAC,OAAO,EAAE,sBAAsB,EAAE,WAAW,EAAE,mBAAmB,EAAC;AACpE,SAAA,EAAA,QAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAEU,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAR/B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sDAAsD;AAChE,oBAAA,QAAQ,EAAE,qBAAqB;AAC/B,oBAAA,SAAS,EAAE;wBACT,qCAAqC;AACrC,wBAAA,EAAC,OAAO,EAAE,sBAAsB,EAAE,WAAW,qBAAqB,EAAC;AACpE,qBAAA;AACF,iBAAA;;;MC5kBY,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YATxB,eAAe;YACf,eAAe;YACf,SAAS;YACT,cAAc;YACd,mBAAmB;AACnB,YAAA,oBAAoB,CAEZ,EAAA,OAAA,EAAA,CAAA,SAAS,EAAE,cAAc,EAAE,mBAAmB,CAAA,EAAA,CAAA;AAE7C,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YATxB,eAAe;YACf,eAAe,CAAA,EAAA,CAAA;;2FAQN,eAAe,EAAA,UAAA,EAAA,CAAA;kBAX3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,eAAe;wBACf,SAAS;wBACT,cAAc;wBACd,mBAAmB;wBACnB,oBAAoB;AACrB,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,cAAc,EAAE,mBAAmB,CAAC;AAC1D,iBAAA;;;;;"}