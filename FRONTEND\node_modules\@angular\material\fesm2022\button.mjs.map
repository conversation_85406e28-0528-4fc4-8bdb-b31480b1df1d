{"version": 3, "file": "button.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/button/button.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/button/button.html", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/button/fab.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/button/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, Input, ViewEncapsulation} from '@angular/core';\nimport {MatButtonAppearance, MatButtonBase} from './button-base';\n\n/**\n * Classes that need to be set for each appearance of the button.\n * Note that we use a `Map` here to avoid issues with property renaming.\n */\nconst APPEARANCE_CLASSES: Map<MatButtonAppearance, readonly string[]> = new Map([\n  ['text', ['mat-mdc-button']],\n  ['filled', ['mdc-button--unelevated', 'mat-mdc-unelevated-button']],\n  ['elevated', ['mdc-button--raised', 'mat-mdc-raised-button']],\n  ['outlined', ['mdc-button--outlined', 'mat-mdc-outlined-button']],\n  ['tonal', ['mat-tonal-button']],\n]);\n\n/**\n * Material Design button component. Users interact with a button to perform an action.\n * See https://m3.material.io/components/buttons/overview\n */\n@Component({\n  selector: `\n    button[matButton], a[matButton], button[mat-button], button[mat-raised-button],\n    button[mat-flat-button], button[mat-stroked-button], a[mat-button], a[mat-raised-button],\n    a[mat-flat-button], a[mat-stroked-button]\n  `,\n  templateUrl: 'button.html',\n  styleUrls: ['button.css', 'button-high-contrast.css'],\n  host: {\n    'class': 'mdc-button',\n  },\n  exportAs: 'matButton, matAnchor',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatButton extends MatButtonBase {\n  /** Appearance of the button. */\n  @Input('matButton')\n  get appearance(): MatButtonAppearance | null {\n    return this._appearance;\n  }\n  set appearance(value: MatButtonAppearance | '') {\n    // Allow empty string so users can do `<button matButton></button>`\n    // without having to write out `=\"text\"` every time.\n    this.setAppearance(value || this._config?.defaultAppearance || 'text');\n  }\n  private _appearance: MatButtonAppearance | null = null;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    const inferredAppearance = _inferAppearance(this._elementRef.nativeElement);\n\n    // Only set the appearance if we managed to infer it from the static attributes, rather than\n    // doing something like `setAppearance(inferredAppearance || 'text')`, because doing so can\n    // cause the fallback appearance's classes to be set and then immediately replaced when\n    // the input value is assigned.\n    if (inferredAppearance) {\n      this.setAppearance(inferredAppearance);\n    }\n  }\n\n  /** Programmatically sets the appearance of the button. */\n  setAppearance(appearance: MatButtonAppearance): void {\n    if (appearance === this._appearance) {\n      return;\n    }\n\n    const classList = this._elementRef.nativeElement.classList;\n    const previousClasses = this._appearance ? APPEARANCE_CLASSES.get(this._appearance) : null;\n    const newClasses = APPEARANCE_CLASSES.get(appearance)!;\n\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && !newClasses) {\n      throw new Error(`Unsupported MatButton appearance \"${appearance}\"`);\n    }\n\n    if (previousClasses) {\n      classList.remove(...previousClasses);\n    }\n\n    classList.add(...newClasses);\n    this._appearance = appearance;\n  }\n}\n\n/** Infers the button's appearance from its static attributes. */\nfunction _inferAppearance(button: HTMLElement): MatButtonAppearance | null {\n  if (button.hasAttribute('mat-raised-button')) {\n    return 'elevated';\n  }\n\n  if (button.hasAttribute('mat-stroked-button')) {\n    return 'outlined';\n  }\n\n  if (button.hasAttribute('mat-flat-button')) {\n    return 'filled';\n  }\n\n  if (button.hasAttribute('mat-button')) {\n    return 'text';\n  }\n\n  return null;\n}\n\n// tslint:disable:variable-name\n/**\n * Material Design button component for anchor elements. Anchor elements are used to provide\n * links for the user to navigate across different routes or pages.\n * See https://m3.material.io/components/buttons/overview\n */\nexport const MatAnchor = MatButton;\nexport type MatAnchor = MatButton;\n// tslint:enable:variable-name\n", "<span\n    class=\"mat-mdc-button-persistent-ripple\"\n    [class.mdc-button__ripple]=\"!_isFab\"\n    [class.mdc-fab__ripple]=\"_isFab\"></span>\n\n<ng-content select=\".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])\">\n</ng-content>\n\n<span class=\"mdc-button__label\"><ng-content></ng-content></span>\n\n<ng-content select=\".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]\">\n</ng-content>\n\n<!--\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\n-->\n<span class=\"mat-focus-indicator\"></span>\n\n<span class=\"mat-mdc-button-touch-target\"></span>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  InjectionToken,\n  Input,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n} from '@angular/core';\n\nimport {MatButtonBase} from './button-base';\nimport {ThemePalette} from '../core';\n\n/** Default FAB options that can be overridden. */\nexport interface MatFabDefaultOptions {\n  /**\n   * Default theme color of the button. This API is supported in M2 themes\n   * only, it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/button/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants.\n   */\n  color?: ThemePalette;\n}\n\n/** Injection token to be used to override the default options for FAB. */\nexport const MAT_FAB_DEFAULT_OPTIONS = new InjectionToken<MatFabDefaultOptions>(\n  'mat-mdc-fab-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_FAB_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_FAB_DEFAULT_OPTIONS_FACTORY(): MatFabDefaultOptions {\n  return {\n    // The FAB by default has its color set to accent.\n    color: 'accent',\n  };\n}\n\n// Default FAB configuration.\nconst defaults = MAT_FAB_DEFAULT_OPTIONS_FACTORY();\n\n/**\n * Material Design floating action button (FAB) component. These buttons represent the primary\n * or most common action for users to interact with.\n * See https://m3.material.io/components/floating-action-button/overview\n *\n * The `MatFabButton` class has two appearances: normal and extended.\n */\n@Component({\n  selector: `button[mat-fab], a[mat-fab], button[matFab], a[matFab]`,\n  templateUrl: 'button.html',\n  styleUrl: 'fab.css',\n  host: {\n    'class': 'mdc-fab mat-mdc-fab-base mat-mdc-fab',\n    '[class.mdc-fab--extended]': 'extended',\n    '[class.mat-mdc-extended-fab]': 'extended',\n  },\n  exportAs: 'matButton, matAnchor',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatFabButton extends MatButtonBase {\n  private _options = inject<MatFabDefaultOptions>(MAT_FAB_DEFAULT_OPTIONS, {optional: true});\n\n  override _isFab = true;\n\n  @Input({transform: booleanAttribute}) extended: boolean;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    this._options = this._options || defaults;\n    this.color = this._options!.color || defaults.color;\n  }\n}\n\n/**\n * Material Design mini floating action button (FAB) component. These buttons represent the primary\n * or most common action for users to interact with.\n * See https://m3.material.io/components/floating-action-button/overview\n */\n@Component({\n  selector: `button[mat-mini-fab], a[mat-mini-fab], button[matMiniFab], a[matMiniFab]`,\n  templateUrl: 'button.html',\n  styleUrl: 'fab.css',\n  host: {\n    'class': 'mdc-fab mat-mdc-fab-base mdc-fab--mini mat-mdc-mini-fab',\n  },\n  exportAs: 'matButton, matAnchor',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatMiniFabButton extends MatButtonBase {\n  private _options = inject<MatFabDefaultOptions>(MAT_FAB_DEFAULT_OPTIONS, {optional: true});\n\n  override _isFab = true;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    this._options = this._options || defaults;\n    this.color = this._options!.color || defaults.color;\n  }\n}\n\n// tslint:disable:variable-name\n/**\n * Material Design floating action button (FAB) component for anchor elements. Anchor elements\n * are used to provide links for the user to navigate across different routes or pages.\n * See https://m3.material.io/components/floating-action-button/overview\n *\n * The `MatFabAnchor` class has two appearances: normal and extended.\n */\nexport const MatFabAnchor = MatFabButton;\nexport type MatFabAnchor = MatFabButton;\n\n/**\n * Material Design mini floating action button (FAB) component for anchor elements. Anchor elements\n * are used to provide links for the user to navigate across different routes or pages.\n * See https://m3.material.io/components/floating-action-button/overview\n */\nexport const MatMiniFabAnchor = MatMiniFabButton;\nexport type MatMiniFabAnchor = MatMiniFabButton;\n// tslint:enable:variable-name\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule, MatRippleModule} from '../core';\nimport {MatButton} from './button';\nimport {Mat<PERSON>abButton, MatMiniFabButton} from './fab';\nimport {MatIconButton} from './icon-button';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    MatRippleModule,\n    MatButton,\n    MatMiniFabButton,\n    MatIconButton,\n    MatFabButton,\n  ],\n  exports: [MatCommonModule, MatButton, MatMiniFabButton, MatIconButton, MatFabButton],\n})\nexport class MatButtonModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAWA;;;AAGG;AACH,MAAM,kBAAkB,GAAgD,IAAI,GAAG,CAAC;AAC9E,IAAA,CAAC,MAAM,EAAE,CAAC,gBAAgB,CAAC,CAAC;AAC5B,IAAA,CAAC,QAAQ,EAAE,CAAC,wBAAwB,EAAE,2BAA2B,CAAC,CAAC;AACnE,IAAA,CAAC,UAAU,EAAE,CAAC,oBAAoB,EAAE,uBAAuB,CAAC,CAAC;AAC7D,IAAA,CAAC,UAAU,EAAE,CAAC,sBAAsB,EAAE,yBAAyB,CAAC,CAAC;AACjE,IAAA,CAAC,OAAO,EAAE,CAAC,kBAAkB,CAAC,CAAC;AAChC,CAAA,CAAC;AAEF;;;AAGG;AAgBG,MAAO,SAAU,SAAQ,aAAa,CAAA;;AAE1C,IAAA,IACI,UAAU,GAAA;QACZ,OAAO,IAAI,CAAC,WAAW;;IAEzB,IAAI,UAAU,CAAC,KAA+B,EAAA;;;AAG5C,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE,iBAAiB,IAAI,MAAM,CAAC;;IAEhE,WAAW,GAA+B,IAAI;AAItD,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;QACP,MAAM,kBAAkB,GAAG,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;;;;;QAM3E,IAAI,kBAAkB,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;;;;AAK1C,IAAA,aAAa,CAAC,UAA+B,EAAA;AAC3C,QAAA,IAAI,UAAU,KAAK,IAAI,CAAC,WAAW,EAAE;YACnC;;QAGF,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS;QAC1D,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI;QAC1F,MAAM,UAAU,GAAG,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAE;AAEtD,QAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,CAAC,UAAU,EAAE;AAClE,YAAA,MAAM,IAAI,KAAK,CAAC,qCAAqC,UAAU,CAAA,CAAA,CAAG,CAAC;;QAGrE,IAAI,eAAe,EAAE;AACnB,YAAA,SAAS,CAAC,MAAM,CAAC,GAAG,eAAe,CAAC;;AAGtC,QAAA,SAAS,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;AAC5B,QAAA,IAAI,CAAC,WAAW,GAAG,UAAU;;uGA/CpB,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAT,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,SAAS,6bC1CtB,8yBAoBA,EAAA,MAAA,EAAA,CAAA,o9rBAAA,EAAA,wXAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDsBa,SAAS,EAAA,UAAA,EAAA,CAAA;kBAfrB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA;;;;GAIT,EAGK,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,YAAY;qBACtB,EACS,QAAA,EAAA,sBAAsB,iBACjB,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8yBAAA,EAAA,MAAA,EAAA,CAAA,o9rBAAA,EAAA,wXAAA,CAAA,EAAA;wDAK3C,UAAU,EAAA,CAAA;sBADb,KAAK;uBAAC,WAAW;;AAiDpB;AACA,SAAS,gBAAgB,CAAC,MAAmB,EAAA;AAC3C,IAAA,IAAI,MAAM,CAAC,YAAY,CAAC,mBAAmB,CAAC,EAAE;AAC5C,QAAA,OAAO,UAAU;;AAGnB,IAAA,IAAI,MAAM,CAAC,YAAY,CAAC,oBAAoB,CAAC,EAAE;AAC7C,QAAA,OAAO,UAAU;;AAGnB,IAAA,IAAI,MAAM,CAAC,YAAY,CAAC,iBAAiB,CAAC,EAAE;AAC1C,QAAA,OAAO,QAAQ;;AAGjB,IAAA,IAAI,MAAM,CAAC,YAAY,CAAC,YAAY,CAAC,EAAE;AACrC,QAAA,OAAO,MAAM;;AAGf,IAAA,OAAO,IAAI;AACb;AAEA;AACA;;;;AAIG;AACI,MAAM,SAAS,GAAG;;AEvFzB;MACa,uBAAuB,GAAG,IAAI,cAAc,CACvD,6BAA6B,EAC7B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,+BAA+B;AACzC,CAAA;AAGH;;;;AAIG;SACa,+BAA+B,GAAA;IAC7C,OAAO;;AAEL,QAAA,KAAK,EAAE,QAAQ;KAChB;AACH;AAEA;AACA,MAAM,QAAQ,GAAG,+BAA+B,EAAE;AAElD;;;;;;AAMG;AAcG,MAAO,YAAa,SAAQ,aAAa,CAAA;IACrC,QAAQ,GAAG,MAAM,CAAuB,uBAAuB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAEjF,MAAM,GAAG,IAAI;AAEgB,IAAA,QAAQ;AAI9C,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;QACP,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ;AACzC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK;;uGAZ1C,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,wDAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAKJ,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,yBAAA,EAAA,UAAA,EAAA,4BAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,sCAAA,EAAA,EAAA,QAAA,EAAA,CAAA,WAAA,EAAA,WAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EDlFrC,8yBAoBA,EAAA,MAAA,EAAA,CAAA,4kSAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FCyDa,YAAY,EAAA,UAAA,EAAA,CAAA;kBAbxB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,sDAAA,CAAwD,EAG5D,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,sCAAsC;AAC/C,wBAAA,2BAA2B,EAAE,UAAU;AACvC,wBAAA,8BAA8B,EAAE,UAAU;qBAC3C,EACS,QAAA,EAAA,sBAAsB,iBACjB,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8yBAAA,EAAA,MAAA,EAAA,CAAA,4kSAAA,CAAA,EAAA;wDAOT,QAAQ,EAAA,CAAA;sBAA7C,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;;AAWtC;;;;AAIG;AAYG,MAAO,gBAAiB,SAAQ,aAAa,CAAA;IACzC,QAAQ,GAAG,MAAM,CAAuB,uBAAuB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;IAEjF,MAAM,GAAG,IAAI;AAItB,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;QACP,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,QAAQ;AACzC,QAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK;;uGAV1C,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,gBAAgB,sRD7G7B,8yBAoBA,EAAA,MAAA,EAAA,CAAA,4kSAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FCyFa,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAX5B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,wEAAA,CAA0E,EAG9E,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,yDAAyD;qBACnE,EACS,QAAA,EAAA,sBAAsB,iBACjB,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,8yBAAA,EAAA,MAAA,EAAA,CAAA,4kSAAA,CAAA,EAAA;;AAgBjD;AACA;;;;;;AAMG;AACI,MAAM,YAAY,GAAG;AAG5B;;;;AAIG;AACI,MAAM,gBAAgB,GAAG;;MClHnB,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YATxB,eAAe;YACf,eAAe;YACf,SAAS;YACT,gBAAgB;YAChB,aAAa;YACb,YAAY,CAAA,EAAA,OAAA,EAAA,CAEJ,eAAe,EAAE,SAAS,EAAE,gBAAgB,EAAE,aAAa,EAAE,YAAY,CAAA,EAAA,CAAA;AAExE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YATxB,eAAe;AACf,YAAA,eAAe,EAMP,eAAe,CAAA,EAAA,CAAA;;2FAEd,eAAe,EAAA,UAAA,EAAA,CAAA;kBAX3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,eAAe;wBACf,SAAS;wBACT,gBAAgB;wBAChB,aAAa;wBACb,YAAY;AACb,qBAAA;oBACD,OAAO,EAAE,CAAC,eAAe,EAAE,SAAS,EAAE,gBAAgB,EAAE,aAAa,EAAE,YAAY,CAAC;AACrF,iBAAA;;;;;"}