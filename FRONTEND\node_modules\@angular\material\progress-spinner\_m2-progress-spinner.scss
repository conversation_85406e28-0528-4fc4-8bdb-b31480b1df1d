@use '../core/tokens/m2-utils';
@use '../core/tokens/m3-utils';
@use 'sass:map';

@function get-tokens($theme) {
  @return (
    base: (
      progress-spinner-active-indicator-width: 4px,
      progress-spinner-size: 48px,
    ),
    color: private-get-color-palette-color-tokens($theme, primary),
    typography: (),
    density: (),
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function private-get-color-palette-color-tokens($theme, $color-variant) {
  $system: m2-utils.get-system($theme);
  $system: m3-utils.replace-colors-with-variant($system, primary, $color-variant);

  @return (
    progress-spinner-active-indicator-color: map.get($system, primary)
  );
}
