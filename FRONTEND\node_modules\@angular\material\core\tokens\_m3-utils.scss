@use 'sass:map';
@use 'sass:meta';
@use 'sass:string';

// Replaces color tokens in the map with those defined as the variant color.
@function replace-colors-with-variant($system, $color, $variant) {
  $system: map.set($system, on-#{$color}, map.get($system, on-#{$variant}));
  $system: map.set($system, on-#{$color}-container, map.get($system, on-#{$variant}-container));
  $system: map.set($system, #{$color}, map.get($system, #{$variant}));
  $system: map.set($system, #{$color}-container, map.get($system, #{$variant}-container));
  $system: map.set($system, inverse-#{$color}, map.get($system, inverse-#{$variant}));
  @return $system;
}

// Gets the theme's system values as a flat map.
@function get-system($theme) {
  @return map.get($theme, _mat-system);
}

// Returns the color with an opacity value using color-mix. If the color is a variable name, it
// will wrap it with `var()`.
@function color-with-opacity($color, $opacity) {
  @if (meta.type-of($color) == string and string.index($color, '--') == 1) {
    $color: var($color);
  }

  // Opacity may be a system level value less than 1, instead of the intended
  // whole percentage, e.g. 38%. Remove this support when possible.
  @if (meta.type-of($opacity) == string and string.index($opacity, '--') == 1) {
    $opacity: 'calc(var(#{$opacity}) * 100%)';
  } @else if (meta.type-of($opacity) == number and $opacity < 1) {
    $opacity: '#{$opacity * 100}%';
  }

  @return color-mix(in srgb, #{$color} #{$opacity}, transparent);
}
