{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/sort/testing/sort-header-harness.ts", "../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/sort/testing/sort-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {SortDirection} from '../../sort';\nimport {SortHeaderHarnessFilters} from './sort-harness-filters';\n\n/** <PERSON><PERSON>ss for interacting with a standard Angular Material sort header in tests. */\nexport class MatSortHeaderHarness extends ComponentHarness {\n  static hostSelector = '.mat-sort-header';\n  private _container = this.locatorFor('.mat-sort-header-container');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to\n   * search for a sort header with specific attributes.\n   */\n  static with(options: SortHeaderHarnessFilters = {}): HarnessPredicate<MatSortHeaderHarness> {\n    return new HarnessPredicate(MatSortHeaderHarness, options)\n      .addOption('label', options.label, (harness, label) =>\n        HarnessPredicate.stringMatches(harness.getLabel(), label),\n      )\n      .addOption('sortDirection', options.sortDirection, (harness, sortDirection) => {\n        return HarnessPredicate.stringMatches(harness.getSortDirection(), sortDirection);\n      });\n  }\n\n  /** Gets the label of the sort header. */\n  async getLabel(): Promise<string> {\n    return (await this._container()).text();\n  }\n\n  /** Gets the sorting direction of the header. */\n  async getSortDirection(): Promise<SortDirection> {\n    const host = await this.host();\n    const ariaSort = await host.getAttribute('aria-sort');\n\n    if (ariaSort === 'ascending') {\n      return 'asc';\n    } else if (ariaSort === 'descending') {\n      return 'desc';\n    }\n\n    return '';\n  }\n\n  /** Gets whether the sort header is currently being sorted by. */\n  async isActive(): Promise<boolean> {\n    return !!(await this.getSortDirection());\n  }\n\n  /** Whether the sort header is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-sort-header-disabled');\n  }\n\n  /** Clicks the header to change its sorting direction. Only works if the header is enabled. */\n  async click(): Promise<void> {\n    return (await this.host()).click();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {SortHarnessFilters, SortHeaderHarnessFilters} from './sort-harness-filters';\nimport {MatSortHeaderHarness} from './sort-header-harness';\n\n/** <PERSON><PERSON><PERSON> for interacting with a standard `mat-sort` in tests. */\nexport class MatSortHarness extends ComponentHarness {\n  static hostSelector = '.mat-sort';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `mat-sort` with specific attributes.\n   * @param options Options for narrowing the search.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: SortHarnessFilters = {}): HarnessPredicate<MatSortHarness> {\n    return new HarnessPredicate(MatSortHarness, options);\n  }\n\n  /** Gets all of the sort headers in the `mat-sort`. */\n  async getSortHeaders(filter: SortHeaderHarnessFilters = {}): Promise<MatSortHeaderHarness[]> {\n    return this.locatorForAll(MatSortHeaderHarness.with(filter))();\n  }\n\n  /** Gets the selected header in the `mat-sort`. */\n  async getActiveHeader(): Promise<MatSortHeaderHarness | null> {\n    const headers = await this.getSortHeaders();\n    for (let i = 0; i < headers.length; i++) {\n      if (await headers[i].isActive()) {\n        return headers[i];\n      }\n    }\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;AAYA;AACM,MAAO,oBAAqB,SAAQ,gBAAgB,CAAA;AACxD,IAAA,OAAO,YAAY,GAAG,kBAAkB;AAChC,IAAA,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,4BAA4B,CAAC;AAElE;;;AAGG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAoC,EAAE,EAAA;AAChD,QAAA,OAAO,IAAI,gBAAgB,CAAC,oBAAoB,EAAE,OAAO;aACtD,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAChD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;AAE1D,aAAA,SAAS,CAAC,eAAe,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE,aAAa,KAAI;YAC5E,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,EAAE,EAAE,aAAa,CAAC;AAClF,SAAC,CAAC;;;AAIN,IAAA,MAAM,QAAQ,GAAA;QACZ,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,EAAE;;;AAIzC,IAAA,MAAM,gBAAgB,GAAA;AACpB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC;AAErD,QAAA,IAAI,QAAQ,KAAK,WAAW,EAAE;AAC5B,YAAA,OAAO,KAAK;;AACP,aAAA,IAAI,QAAQ,KAAK,YAAY,EAAE;AACpC,YAAA,OAAO,MAAM;;AAGf,QAAA,OAAO,EAAE;;;AAIX,IAAA,MAAM,QAAQ,GAAA;QACZ,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAC;;;AAI1C,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,0BAA0B,CAAC;;;AAIjE,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;;AClDtC;AACM,MAAO,cAAe,SAAQ,gBAAgB,CAAA;AAClD,IAAA,OAAO,YAAY,GAAG,WAAW;AAEjC;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAA8B,EAAE,EAAA;AAC1C,QAAA,OAAO,IAAI,gBAAgB,CAAC,cAAc,EAAE,OAAO,CAAC;;;AAItD,IAAA,MAAM,cAAc,CAAC,MAAA,GAAmC,EAAE,EAAA;AACxD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;;AAIhE,IAAA,MAAM,eAAe,GAAA;AACnB,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,EAAE;AAC3C,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,EAAE;AAC/B,gBAAA,OAAO,OAAO,CAAC,CAAC,CAAC;;;AAGrB,QAAA,OAAO,IAAI;;;;;;"}