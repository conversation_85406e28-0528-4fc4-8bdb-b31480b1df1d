{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/stepper/testing/step-harness.ts", "../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/stepper/testing/step-harness-filters.ts", "../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/stepper/testing/stepper-harness.ts", "../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/stepper/testing/stepper-button-harnesses.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n  HarnessLoader,\n} from '@angular/cdk/testing';\nimport {StepHarnessFilters} from './step-harness-filters';\n\n/** <PERSON><PERSON>ss for interacting with a standard Angular Material step in tests. */\nexport class MatStepHarness extends ContentContainerComponentHarness<string> {\n  /** The selector for the host element of a `MatStep` instance. */\n  static hostSelector = '.mat-step-header';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatStepHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which steps are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: StepHarnessFilters = {}): HarnessPredicate<MatStepHarness> {\n    return new HarnessPredicate(MatStepHarness, options)\n      .addOption('label', options.label, (harness, label) =>\n        HarnessPredicate.stringMatches(harness.getLabel(), label),\n      )\n      .addOption(\n        'selected',\n        options.selected,\n        async (harness, selected) => (await harness.isSelected()) === selected,\n      )\n      .addOption(\n        'completed',\n        options.completed,\n        async (harness, completed) => (await harness.isCompleted()) === completed,\n      )\n      .addOption(\n        'invalid',\n        options.invalid,\n        async (harness, invalid) => (await harness.hasErrors()) === invalid,\n      );\n  }\n\n  /** Gets the label of the step. */\n  async getLabel(): Promise<string> {\n    return (await this.locatorFor('.mat-step-text-label')()).text();\n  }\n\n  /** Gets the `aria-label` of the step. */\n  async getAriaLabel(): Promise<string | null> {\n    return (await this.host()).getAttribute('aria-label');\n  }\n\n  /** Gets the value of the `aria-labelledby` attribute. */\n  async getAriaLabelledby(): Promise<string | null> {\n    return (await this.host()).getAttribute('aria-labelledby');\n  }\n\n  /** Whether the step is selected. */\n  async isSelected(): Promise<boolean> {\n    const host = await this.host();\n    return (await host.getAttribute('aria-selected')) === 'true';\n  }\n\n  /** Whether the step has been filled out. */\n  async isCompleted(): Promise<boolean> {\n    const state = await this._getIconState();\n    return state === 'done' || (state === 'edit' && !(await this.isSelected()));\n  }\n\n  /**\n   * Whether the step is currently showing its error state. Note that this doesn't mean that there\n   * are or aren't any invalid form controls inside the step, but that the step is showing its\n   * error-specific styling which depends on there being invalid controls, as well as the\n   * `ErrorStateMatcher` determining that an error should be shown and that the `showErrors`\n   * option was enabled through the `STEPPER_GLOBAL_OPTIONS` injection token.\n   */\n  async hasErrors(): Promise<boolean> {\n    return (await this._getIconState()) === 'error';\n  }\n\n  /** Whether the step is optional. */\n  async isOptional(): Promise<boolean> {\n    // If the node with the optional text is present, it means that the step is optional.\n    const optionalNode = await this.locatorForOptional('.mat-step-optional')();\n    return !!optionalNode;\n  }\n\n  /**\n   * Selects the given step by clicking on the label. The step may not be selected\n   * if the stepper doesn't allow it (e.g. if there are validation errors).\n   */\n  async select(): Promise<void> {\n    await (await this.host()).click();\n  }\n\n  protected override async getRootHarnessLoader(): Promise<HarnessLoader> {\n    const contentId = await (await this.host()).getAttribute('aria-controls');\n    return this.documentRootLocatorFactory().harnessLoaderFor(`#${contentId}`);\n  }\n\n  /**\n   * Gets the state of the step. Note that we have a `StepState` which we could use to type the\n   * return value, but it's basically the same as `string`, because the type has `| string`.\n   */\n  private async _getIconState(): Promise<string> {\n    // The state is exposed on the icon with a class that looks like `mat-step-icon-state-{{state}}`\n    const icon = await this.locatorFor('.mat-step-icon')();\n    const classes = (await icon.getAttribute('class'))!;\n    const match = classes.match(/mat-step-icon-state-([a-z]+)/);\n\n    if (!match) {\n      throw Error(`Could not determine step state from \"${classes}\".`);\n    }\n\n    return match[1];\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {BaseHarnessFilters} from '@angular/cdk/testing';\n\n/** Possible orientations for a stepper. */\nexport enum StepperOrientation {\n  HORIZONTAL,\n  VERTICAL,\n}\n\n/** A set of criteria that can be used to filter a list of `MatStepHarness` instances. */\nexport interface StepHarnessFilters extends BaseHarnessFilters {\n  /** Only find instances whose label matches the given value. */\n  label?: string | RegExp;\n  /** Only find steps with the given selected state. */\n  selected?: boolean;\n  /** Only find completed steps. */\n  completed?: boolean;\n  /** Only find steps that have errors. */\n  invalid?: boolean;\n}\n\n/** A set of criteria that can be used to filter a list of `MatStepperHarness` instances. */\nexport interface StepperHarnessFilters extends BaseHarnessFilters {\n  /** Only find instances whose orientation matches the given value. */\n  orientation?: StepperOrientation;\n}\n\n/**\n * A set of criteria that can be used to filter a list of\n * `MatStepperNextHarness` and `MatStepperPreviousHarness` instances.\n */\nexport interface StepperButtonHarnessFilters extends BaseHarnessFilters {\n  /** Only find instances whose text matches the given value. */\n  text?: string | RegExp;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {MatStepHarness} from './step-harness';\nimport {\n  StepperHarnessFilters,\n  StepHarnessFilters,\n  StepperOrientation,\n} from './step-harness-filters';\n\n/** <PERSON><PERSON>ss for interacting with a standard Material stepper in tests. */\nexport class MatStepperHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatStepper` instance. */\n  static hostSelector = '.mat-stepper-horizontal, .mat-stepper-vertical';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatStepperHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which stepper instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: StepperHarnessFilters = {}): HarnessPredicate<MatStepperHarness> {\n    return new HarnessPredicate(MatStepperHarness, options).addOption(\n      'orientation',\n      options.orientation,\n      async (harness, orientation) => (await harness.getOrientation()) === orientation,\n    );\n  }\n\n  /**\n   * Gets the list of steps in the stepper.\n   * @param filter Optionally filters which steps are included.\n   */\n  async getSteps(filter: StepHarnessFilters = {}): Promise<MatStepHarness[]> {\n    return this.locatorForAll(MatStepHarness.with(filter))();\n  }\n\n  /** Gets the orientation of the stepper. */\n  async getOrientation(): Promise<StepperOrientation> {\n    const host = await this.host();\n    return (await host.hasClass('mat-stepper-horizontal'))\n      ? StepperOrientation.HORIZONTAL\n      : StepperOrientation.VERTICAL;\n  }\n\n  /**\n   * Selects a step in this stepper.\n   * @param filter An optional filter to apply to the child steps. The first step matching the\n   *    filter will be selected.\n   */\n  async selectStep(filter: StepHarnessFilters = {}): Promise<void> {\n    const steps = await this.getSteps(filter);\n    if (!steps.length) {\n      throw Error(`Cannot find mat-step matching filter ${JSON.stringify(filter)}`);\n    }\n    await steps[0].select();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {StepperButtonHarnessFilters} from './step-harness-filters';\n\n/** Base class for stepper button harnesses. */\nabstract class StepperButtonHarness extends ComponentHarness {\n  /** Gets the text of the button. */\n  async getText(): Promise<string> {\n    return (await this.host()).text();\n  }\n\n  /** Clicks the button. */\n  async click(): Promise<void> {\n    return (await this.host()).click();\n  }\n}\n\n/** Harness for interacting with a standard Angular Material stepper next button in tests. */\nexport class MatStepperNextHarness extends StepperButtonHarness {\n  /** The selector for the host element of a `MatStep` instance. */\n  static hostSelector = '.mat-stepper-next';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatStepperNextHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which steps are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: StepperButtonHarnessFilters = {}): HarnessPredicate<MatStepperNextHarness> {\n    return new HarnessPredicate(MatStepperNextHarness, options).addOption(\n      'text',\n      options.text,\n      (harness, text) => HarnessPredicate.stringMatches(harness.getText(), text),\n    );\n  }\n}\n\n/** Harness for interacting with a standard Angular Material stepper previous button in tests. */\nexport class MatStepperPreviousHarness extends StepperButtonHarness {\n  /** The selector for the host element of a `MatStep` instance. */\n  static hostSelector = '.mat-stepper-previous';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatStepperPreviousHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which steps are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: StepperButtonHarnessFilters = {},\n  ): HarnessPredicate<MatStepperPreviousHarness> {\n    return new HarnessPredicate(MatStepperPreviousHarness, options).addOption(\n      'text',\n      options.text,\n      (harness, text) => HarnessPredicate.stringMatches(harness.getText(), text),\n    );\n  }\n}\n"], "names": [], "mappings": ";;AAeA;AACM,MAAO,cAAe,SAAQ,gCAAwC,CAAA;;AAE1E,IAAA,OAAO,YAAY,GAAG,kBAAkB;AAExC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAA8B,EAAE,EAAA;AAC1C,QAAA,OAAO,IAAI,gBAAgB,CAAC,cAAc,EAAE,OAAO;aAChD,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAChD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;aAE1D,SAAS,CACR,UAAU,EACV,OAAO,CAAC,QAAQ,EAChB,OAAO,OAAO,EAAE,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;aAEvE,SAAS,CACR,WAAW,EACX,OAAO,CAAC,SAAS,EACjB,OAAO,OAAO,EAAE,SAAS,KAAK,CAAC,MAAM,OAAO,CAAC,WAAW,EAAE,MAAM,SAAS;aAE1E,SAAS,CACR,SAAS,EACT,OAAO,CAAC,OAAO,EACf,OAAO,OAAO,EAAE,OAAO,KAAK,CAAC,MAAM,OAAO,CAAC,SAAS,EAAE,MAAM,OAAO,CACpE;;;AAIL,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,sBAAsB,CAAC,EAAE,EAAE,IAAI,EAAE;;;AAIjE,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,YAAY,CAAC;;;AAIvD,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC;;;AAI5D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,OAAO,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM;;;AAI9D,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE;AACxC,QAAA,OAAO,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,IAAI,EAAE,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;;AAG7E;;;;;;AAMG;AACH,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,MAAM,OAAO;;;AAIjD,IAAA,MAAM,UAAU,GAAA;;QAEd,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,EAAE;QAC1E,OAAO,CAAC,CAAC,YAAY;;AAGvB;;;AAGG;AACH,IAAA,MAAM,MAAM,GAAA;QACV,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;AAGhB,IAAA,MAAM,oBAAoB,GAAA;AAC3C,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,eAAe,CAAC;QACzE,OAAO,IAAI,CAAC,0BAA0B,EAAE,CAAC,gBAAgB,CAAC,CAAI,CAAA,EAAA,SAAS,CAAE,CAAA,CAAC;;AAG5E;;;AAGG;AACK,IAAA,MAAM,aAAa,GAAA;;QAEzB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE;QACtD,MAAM,OAAO,IAAI,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAE;QACnD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC;QAE3D,IAAI,CAAC,KAAK,EAAE;AACV,YAAA,MAAM,KAAK,CAAC,CAAA,qCAAA,EAAwC,OAAO,CAAA,EAAA,CAAI,CAAC;;AAGlE,QAAA,OAAO,KAAK,CAAC,CAAC,CAAC;;;;AC/GnB;IACY;AAAZ,CAAA,UAAY,kBAAkB,EAAA;AAC5B,IAAA,kBAAA,CAAA,kBAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACV,IAAA,kBAAA,CAAA,kBAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,UAAQ;AACV,CAAC,EAHW,kBAAkB,KAAlB,kBAAkB,GAG7B,EAAA,CAAA,CAAA;;ACGD;AACM,MAAO,iBAAkB,SAAQ,gBAAgB,CAAA;;AAErD,IAAA,OAAO,YAAY,GAAG,gDAAgD;AAEtE;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAiC,EAAE,EAAA;AAC7C,QAAA,OAAO,IAAI,gBAAgB,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,SAAS,CAC/D,aAAa,EACb,OAAO,CAAC,WAAW,EACnB,OAAO,OAAO,EAAE,WAAW,KAAK,CAAC,MAAM,OAAO,CAAC,cAAc,EAAE,MAAM,WAAW,CACjF;;AAGH;;;AAGG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAA,GAA6B,EAAE,EAAA;AAC5C,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;;AAI1D,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,wBAAwB,CAAC;cACjD,kBAAkB,CAAC;AACrB,cAAE,kBAAkB,CAAC,QAAQ;;AAGjC;;;;AAIG;AACH,IAAA,MAAM,UAAU,CAAC,MAAA,GAA6B,EAAE,EAAA;QAC9C,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AACzC,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjB,MAAM,KAAK,CAAC,CAAA,qCAAA,EAAwC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAE,CAAA,CAAC;;AAE/E,QAAA,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;;;;AClD3B;AACA,MAAe,oBAAqB,SAAQ,gBAAgB,CAAA;;AAE1D,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;AAErC;AAED;AACM,MAAO,qBAAsB,SAAQ,oBAAoB,CAAA;;AAE7D,IAAA,OAAO,YAAY,GAAG,mBAAmB;AAEzC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAuC,EAAE,EAAA;AACnD,QAAA,OAAO,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAC,SAAS,CACnE,MAAM,EACN,OAAO,CAAC,IAAI,EACZ,CAAC,OAAO,EAAE,IAAI,KAAK,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAC3E;;;AAIL;AACM,MAAO,yBAA0B,SAAQ,oBAAoB,CAAA;;AAEjE,IAAA,OAAO,YAAY,GAAG,uBAAuB;AAE7C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAAuC,EAAE,EAAA;AAEzC,QAAA,OAAO,IAAI,gBAAgB,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC,SAAS,CACvE,MAAM,EACN,OAAO,CAAC,IAAI,EACZ,CAAC,OAAO,EAAE,IAAI,KAAK,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC,CAC3E;;;;;;"}