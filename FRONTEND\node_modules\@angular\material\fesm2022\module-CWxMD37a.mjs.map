{"version": 3, "file": "module-CWxMD37a.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/tooltip/tooltip.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/tooltip/tooltip.html", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/tooltip/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {takeUntil} from 'rxjs/operators';\nimport {\n  BooleanInput,\n  coerceBooleanProperty,\n  coerceNumberProperty,\n  NumberInput,\n} from '@angular/cdk/coercion';\nimport {ESCAPE, hasModifierKey} from '@angular/cdk/keycodes';\nimport {\n  AfterViewInit,\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  Directive,\n  ElementRef,\n  InjectionToken,\n  Input,\n  NgZone,\n  OnDestroy,\n  ViewChild,\n  ViewContainerRef,\n  ViewEncapsulation,\n  inject,\n  afterNextRender,\n  Injector,\n  DOCUMENT,\n} from '@angular/core';\nimport {NgClass} from '@angular/common';\nimport {normalizePassiveListenerOptions, Platform} from '@angular/cdk/platform';\nimport {AriaDescriber, FocusMonitor} from '@angular/cdk/a11y';\nimport {Directionality} from '@angular/cdk/bidi';\nimport {\n  ConnectedPosition,\n  ConnectionPositionPair,\n  createFlexibleConnectedPositionStrategy,\n  createOverlayRef,\n  createRepositionScrollStrategy,\n  FlexibleConnectedPositionStrategy,\n  HorizontalConnectionPos,\n  OriginConnectionPosition,\n  OverlayConnectionPosition,\n  OverlayRef,\n  ScrollDispatcher,\n  ScrollStrategy,\n  VerticalConnectionPos,\n} from '@angular/cdk/overlay';\nimport {ComponentPortal} from '@angular/cdk/portal';\nimport {Observable, Subject} from 'rxjs';\nimport {_animationsDisabled} from '../core';\n\n/** Possible positions for a tooltip. */\nexport type TooltipPosition = 'left' | 'right' | 'above' | 'below' | 'before' | 'after';\n\n/**\n * Options for how the tooltip trigger should handle touch gestures.\n * See `MatTooltip.touchGestures` for more information.\n */\nexport type TooltipTouchGestures = 'auto' | 'on' | 'off';\n\n/** Possible visibility states of a tooltip. */\nexport type TooltipVisibility = 'initial' | 'visible' | 'hidden';\n\n/** Time in ms to throttle repositioning after scroll events. */\nexport const SCROLL_THROTTLE_MS = 20;\n\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nexport function getMatTooltipInvalidPositionError(position: string) {\n  return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nexport const MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'mat-tooltip-scroll-strategy',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const injector = inject(Injector);\n      return () => createRepositionScrollStrategy(injector, {scrollThrottle: SCROLL_THROTTLE_MS});\n    },\n  },\n);\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(_overlay: unknown): () => ScrollStrategy {\n  const injector = inject(Injector);\n  return () => createRepositionScrollStrategy(injector, {scrollThrottle: SCROLL_THROTTLE_MS});\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n  provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n  deps: [] as any[],\n  useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY,\n};\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY(): MatTooltipDefaultOptions {\n  return {\n    showDelay: 0,\n    hideDelay: 0,\n    touchendHideDelay: 1500,\n  };\n}\n\n/** Injection token to be used to override the default options for `matTooltip`. */\nexport const MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken<MatTooltipDefaultOptions>(\n  'mat-tooltip-default-options',\n  {\n    providedIn: 'root',\n    factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY,\n  },\n);\n\n/** Default `matTooltip` options that can be overridden. */\nexport interface MatTooltipDefaultOptions {\n  /** Default delay when the tooltip is shown. */\n  showDelay: number;\n\n  /** Default delay when the tooltip is hidden. */\n  hideDelay: number;\n\n  /** Default delay when hiding the tooltip on a touch device. */\n  touchendHideDelay: number;\n\n  /** Time between the user putting the pointer on a tooltip trigger and the long press event being fired on a touch device. */\n  touchLongPressShowDelay?: number;\n\n  /** Default touch gesture handling for tooltips. */\n  touchGestures?: TooltipTouchGestures;\n\n  /** Default position for tooltips. */\n  position?: TooltipPosition;\n\n  /**\n   * Default value for whether tooltips should be positioned near the click or touch origin\n   * instead of outside the element bounding box.\n   */\n  positionAtOrigin?: boolean;\n\n  /** Disables the ability for the user to interact with the tooltip element. */\n  disableTooltipInteractivity?: boolean;\n\n  /**\n   * Default classes to be applied to the tooltip. These default classes will not be applied if\n   * `tooltipClass` is defined directly on the tooltip element, as it will override the default.\n   */\n  tooltipClass?: string | string[];\n}\n\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nexport const TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\n\nconst PANEL_CLASS = 'tooltip-panel';\n\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({passive: true});\n\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\n\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\n@Directive({\n  selector: '[matTooltip]',\n  exportAs: 'matTooltip',\n  host: {\n    'class': 'mat-mdc-tooltip-trigger',\n    '[class.mat-mdc-tooltip-disabled]': 'disabled',\n  },\n})\nexport class MatTooltip implements OnDestroy, AfterViewInit {\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _ngZone = inject(NgZone);\n  private _platform = inject(Platform);\n  private _ariaDescriber = inject(AriaDescriber);\n  private _focusMonitor = inject(FocusMonitor);\n  protected _dir = inject(Directionality);\n  private _injector = inject(Injector);\n  private _viewContainerRef = inject(ViewContainerRef);\n  private _animationsDisabled = _animationsDisabled();\n  private _defaultOptions = inject<MatTooltipDefaultOptions>(MAT_TOOLTIP_DEFAULT_OPTIONS, {\n    optional: true,\n  });\n\n  _overlayRef: OverlayRef | null;\n  _tooltipInstance: TooltipComponent | null;\n  _overlayPanelClass: string[] | undefined; // Used for styling internally.\n\n  private _portal: ComponentPortal<TooltipComponent>;\n  private _position: TooltipPosition = 'below';\n  private _positionAtOrigin: boolean = false;\n  private _disabled: boolean = false;\n  private _tooltipClass: string | string[] | Set<string> | {[key: string]: any};\n  private _viewInitialized = false;\n  private _pointerExitEventsInitialized = false;\n  private readonly _tooltipComponent = TooltipComponent;\n  private _viewportMargin = 8;\n  private _currentPosition: TooltipPosition;\n  private readonly _cssClassPrefix: string = 'mat-mdc';\n  private _ariaDescriptionPending: boolean;\n  private _dirSubscribed = false;\n\n  /** Allows the user to define the position of the tooltip relative to the parent element */\n  @Input('matTooltipPosition')\n  get position(): TooltipPosition {\n    return this._position;\n  }\n\n  set position(value: TooltipPosition) {\n    if (value !== this._position) {\n      this._position = value;\n\n      if (this._overlayRef) {\n        this._updatePosition(this._overlayRef);\n        this._tooltipInstance?.show(0);\n        this._overlayRef.updatePosition();\n      }\n    }\n  }\n\n  /**\n   * Whether tooltip should be relative to the click or touch origin\n   * instead of outside the element bounding box.\n   */\n  @Input('matTooltipPositionAtOrigin')\n  get positionAtOrigin(): boolean {\n    return this._positionAtOrigin;\n  }\n\n  set positionAtOrigin(value: BooleanInput) {\n    this._positionAtOrigin = coerceBooleanProperty(value);\n    this._detach();\n    this._overlayRef = null;\n  }\n\n  /** Disables the display of the tooltip. */\n  @Input('matTooltipDisabled')\n  get disabled(): boolean {\n    return this._disabled;\n  }\n\n  set disabled(value: BooleanInput) {\n    const isDisabled = coerceBooleanProperty(value);\n\n    if (this._disabled !== isDisabled) {\n      this._disabled = isDisabled;\n\n      // If tooltip is disabled, hide immediately.\n      if (isDisabled) {\n        this.hide(0);\n      } else {\n        this._setupPointerEnterEventsIfNeeded();\n      }\n\n      this._syncAriaDescription(this.message);\n    }\n  }\n\n  /** The default delay in ms before showing the tooltip after show is called */\n  @Input('matTooltipShowDelay')\n  get showDelay(): number {\n    return this._showDelay;\n  }\n\n  set showDelay(value: NumberInput) {\n    this._showDelay = coerceNumberProperty(value);\n  }\n\n  private _showDelay: number;\n\n  /** The default delay in ms before hiding the tooltip after hide is called */\n  @Input('matTooltipHideDelay')\n  get hideDelay(): number {\n    return this._hideDelay;\n  }\n\n  set hideDelay(value: NumberInput) {\n    this._hideDelay = coerceNumberProperty(value);\n\n    if (this._tooltipInstance) {\n      this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n    }\n  }\n\n  private _hideDelay: number;\n\n  /**\n   * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n   * uses a long press gesture to show and hide, however it can conflict with the native browser\n   * gestures. To work around the conflict, Angular Material disables native gestures on the\n   * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n   * elements). The different values for this option configure the touch event handling as follows:\n   * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n   *   browser gestures on particular elements. In particular, it allows text selection on inputs\n   *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n   * - `on` - Enables touch gestures for all elements and disables native\n   *   browser gestures with no exceptions.\n   * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n   *   showing on touch devices.\n   */\n  @Input('matTooltipTouchGestures') touchGestures: TooltipTouchGestures = 'auto';\n\n  /** The message to be displayed in the tooltip */\n  @Input('matTooltip')\n  get message(): string {\n    return this._message;\n  }\n\n  set message(value: string | null | undefined) {\n    const oldMessage = this._message;\n\n    // If the message is not a string (e.g. number), convert it to a string and trim it.\n    // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n    // away the string-conversion: https://github.com/angular/components/issues/20684\n    this._message = value != null ? String(value).trim() : '';\n\n    if (!this._message && this._isTooltipVisible()) {\n      this.hide(0);\n    } else {\n      this._setupPointerEnterEventsIfNeeded();\n      this._updateTooltipMessage();\n    }\n\n    this._syncAriaDescription(oldMessage);\n  }\n\n  private _message = '';\n\n  /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n  @Input('matTooltipClass')\n  get tooltipClass() {\n    return this._tooltipClass;\n  }\n\n  set tooltipClass(value: string | string[] | Set<string> | {[key: string]: any}) {\n    this._tooltipClass = value;\n    if (this._tooltipInstance) {\n      this._setTooltipClass(this._tooltipClass);\n    }\n  }\n\n  /** Manually-bound passive event listeners. */\n  private readonly _passiveListeners: (readonly [string, EventListenerOrEventListenerObject])[] =\n    [];\n\n  /** Timer started at the last `touchstart` event. */\n  private _touchstartTimeout: null | ReturnType<typeof setTimeout> = null;\n\n  /** Emits when the component is destroyed. */\n  private readonly _destroyed = new Subject<void>();\n\n  /** Whether ngOnDestroyed has been called. */\n  private _isDestroyed = false;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const defaultOptions = this._defaultOptions;\n\n    if (defaultOptions) {\n      this._showDelay = defaultOptions.showDelay;\n      this._hideDelay = defaultOptions.hideDelay;\n\n      if (defaultOptions.position) {\n        this.position = defaultOptions.position;\n      }\n\n      if (defaultOptions.positionAtOrigin) {\n        this.positionAtOrigin = defaultOptions.positionAtOrigin;\n      }\n\n      if (defaultOptions.touchGestures) {\n        this.touchGestures = defaultOptions.touchGestures;\n      }\n\n      if (defaultOptions.tooltipClass) {\n        this.tooltipClass = defaultOptions.tooltipClass;\n      }\n    }\n\n    this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n  }\n\n  ngAfterViewInit() {\n    // This needs to happen after view init so the initial values for all inputs have been set.\n    this._viewInitialized = true;\n    this._setupPointerEnterEventsIfNeeded();\n\n    this._focusMonitor\n      .monitor(this._elementRef)\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(origin => {\n        // Note that the focus monitor runs outside the Angular zone.\n        if (!origin) {\n          this._ngZone.run(() => this.hide(0));\n        } else if (origin === 'keyboard') {\n          this._ngZone.run(() => this.show());\n        }\n      });\n  }\n\n  /**\n   * Dispose the tooltip when destroyed.\n   */\n  ngOnDestroy() {\n    const nativeElement = this._elementRef.nativeElement;\n\n    // Optimization: Do not call clearTimeout unless there is an active timer.\n    if (this._touchstartTimeout) {\n      clearTimeout(this._touchstartTimeout);\n    }\n\n    if (this._overlayRef) {\n      this._overlayRef.dispose();\n      this._tooltipInstance = null;\n    }\n\n    // Clean up the event listeners set in the constructor\n    this._passiveListeners.forEach(([event, listener]) => {\n      nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n    });\n    this._passiveListeners.length = 0;\n\n    this._destroyed.next();\n    this._destroyed.complete();\n\n    this._isDestroyed = true;\n\n    this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n    this._focusMonitor.stopMonitoring(nativeElement);\n  }\n\n  /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n  show(delay: number = this.showDelay, origin?: {x: number; y: number}): void {\n    if (this.disabled || !this.message || this._isTooltipVisible()) {\n      this._tooltipInstance?._cancelPendingAnimations();\n      return;\n    }\n\n    const overlayRef = this._createOverlay(origin);\n    this._detach();\n    this._portal =\n      this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n    const instance = (this._tooltipInstance = overlayRef.attach(this._portal).instance);\n    instance._triggerElement = this._elementRef.nativeElement;\n    instance._mouseLeaveHideDelay = this._hideDelay;\n    instance\n      .afterHidden()\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => this._detach());\n    this._setTooltipClass(this._tooltipClass);\n    this._updateTooltipMessage();\n    instance.show(delay);\n  }\n\n  /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n  hide(delay: number = this.hideDelay): void {\n    const instance = this._tooltipInstance;\n\n    if (instance) {\n      if (instance.isVisible()) {\n        instance.hide(delay);\n      } else {\n        instance._cancelPendingAnimations();\n        this._detach();\n      }\n    }\n  }\n\n  /** Shows/hides the tooltip */\n  toggle(origin?: {x: number; y: number}): void {\n    this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n  }\n\n  /** Returns true if the tooltip is currently visible to the user */\n  _isTooltipVisible(): boolean {\n    return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n  }\n\n  /** Create the overlay config and position strategy */\n  private _createOverlay(origin?: {x: number; y: number}): OverlayRef {\n    if (this._overlayRef) {\n      const existingStrategy = this._overlayRef.getConfig()\n        .positionStrategy as FlexibleConnectedPositionStrategy;\n\n      if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n        return this._overlayRef;\n      }\n\n      this._detach();\n    }\n\n    const scrollableAncestors = this._injector\n      .get(ScrollDispatcher)\n      .getAncestorScrollContainers(this._elementRef);\n\n    const panelClass = `${this._cssClassPrefix}-${PANEL_CLASS}`;\n\n    // Create connected position strategy that listens for scroll events to reposition.\n    const strategy = createFlexibleConnectedPositionStrategy(\n      this._injector,\n      this.positionAtOrigin ? origin || this._elementRef : this._elementRef,\n    )\n      .withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`)\n      .withFlexibleDimensions(false)\n      .withViewportMargin(this._viewportMargin)\n      .withScrollableContainers(scrollableAncestors);\n\n    strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n      this._updateCurrentPositionClass(change.connectionPair);\n\n      if (this._tooltipInstance) {\n        if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n          // After position changes occur and the overlay is clipped by\n          // a parent scrollable then close the tooltip.\n          this._ngZone.run(() => this.hide(0));\n        }\n      }\n    });\n\n    this._overlayRef = createOverlayRef(this._injector, {\n      direction: this._dir,\n      positionStrategy: strategy,\n      panelClass: this._overlayPanelClass ? [...this._overlayPanelClass, panelClass] : panelClass,\n      scrollStrategy: this._injector.get(MAT_TOOLTIP_SCROLL_STRATEGY)(),\n      disableAnimations: this._animationsDisabled,\n    });\n\n    this._updatePosition(this._overlayRef);\n\n    this._overlayRef\n      .detachments()\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => this._detach());\n\n    this._overlayRef\n      .outsidePointerEvents()\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n\n    this._overlayRef\n      .keydownEvents()\n      .pipe(takeUntil(this._destroyed))\n      .subscribe(event => {\n        if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n          event.preventDefault();\n          event.stopPropagation();\n          this._ngZone.run(() => this.hide(0));\n        }\n      });\n\n    if (this._defaultOptions?.disableTooltipInteractivity) {\n      this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n    }\n\n    if (!this._dirSubscribed) {\n      this._dirSubscribed = true;\n      this._dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n        if (this._overlayRef) {\n          this._updatePosition(this._overlayRef);\n        }\n      });\n    }\n\n    return this._overlayRef;\n  }\n\n  /** Detaches the currently-attached tooltip. */\n  private _detach() {\n    if (this._overlayRef && this._overlayRef.hasAttached()) {\n      this._overlayRef.detach();\n    }\n\n    this._tooltipInstance = null;\n  }\n\n  /** Updates the position of the current tooltip. */\n  private _updatePosition(overlayRef: OverlayRef) {\n    const position = overlayRef.getConfig().positionStrategy as FlexibleConnectedPositionStrategy;\n    const origin = this._getOrigin();\n    const overlay = this._getOverlayPosition();\n\n    position.withPositions([\n      this._addOffset({...origin.main, ...overlay.main}),\n      this._addOffset({...origin.fallback, ...overlay.fallback}),\n    ]);\n  }\n\n  /** Adds the configured offset to a position. Used as a hook for child classes. */\n  protected _addOffset(position: ConnectedPosition): ConnectedPosition {\n    const offset = UNBOUNDED_ANCHOR_GAP;\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n\n    if (position.originY === 'top') {\n      position.offsetY = -offset;\n    } else if (position.originY === 'bottom') {\n      position.offsetY = offset;\n    } else if (position.originX === 'start') {\n      position.offsetX = isLtr ? -offset : offset;\n    } else if (position.originX === 'end') {\n      position.offsetX = isLtr ? offset : -offset;\n    }\n\n    return position;\n  }\n\n  /**\n   * Returns the origin position and a fallback position based on the user's position preference.\n   * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n   */\n  _getOrigin(): {main: OriginConnectionPosition; fallback: OriginConnectionPosition} {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let originPosition: OriginConnectionPosition;\n\n    if (position == 'above' || position == 'below') {\n      originPosition = {originX: 'center', originY: position == 'above' ? 'top' : 'bottom'};\n    } else if (\n      position == 'before' ||\n      (position == 'left' && isLtr) ||\n      (position == 'right' && !isLtr)\n    ) {\n      originPosition = {originX: 'start', originY: 'center'};\n    } else if (\n      position == 'after' ||\n      (position == 'right' && isLtr) ||\n      (position == 'left' && !isLtr)\n    ) {\n      originPosition = {originX: 'end', originY: 'center'};\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n\n    const {x, y} = this._invertPosition(originPosition!.originX, originPosition!.originY);\n\n    return {\n      main: originPosition!,\n      fallback: {originX: x, originY: y},\n    };\n  }\n\n  /** Returns the overlay position and a fallback position based on the user's preference */\n  _getOverlayPosition(): {main: OverlayConnectionPosition; fallback: OverlayConnectionPosition} {\n    const isLtr = !this._dir || this._dir.value == 'ltr';\n    const position = this.position;\n    let overlayPosition: OverlayConnectionPosition;\n\n    if (position == 'above') {\n      overlayPosition = {overlayX: 'center', overlayY: 'bottom'};\n    } else if (position == 'below') {\n      overlayPosition = {overlayX: 'center', overlayY: 'top'};\n    } else if (\n      position == 'before' ||\n      (position == 'left' && isLtr) ||\n      (position == 'right' && !isLtr)\n    ) {\n      overlayPosition = {overlayX: 'end', overlayY: 'center'};\n    } else if (\n      position == 'after' ||\n      (position == 'right' && isLtr) ||\n      (position == 'left' && !isLtr)\n    ) {\n      overlayPosition = {overlayX: 'start', overlayY: 'center'};\n    } else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      throw getMatTooltipInvalidPositionError(position);\n    }\n\n    const {x, y} = this._invertPosition(overlayPosition!.overlayX, overlayPosition!.overlayY);\n\n    return {\n      main: overlayPosition!,\n      fallback: {overlayX: x, overlayY: y},\n    };\n  }\n\n  /** Updates the tooltip message and repositions the overlay according to the new message length */\n  private _updateTooltipMessage() {\n    // Must wait for the message to be painted to the tooltip so that the overlay can properly\n    // calculate the correct positioning based on the size of the text.\n    if (this._tooltipInstance) {\n      this._tooltipInstance.message = this.message;\n      this._tooltipInstance._markForCheck();\n\n      afterNextRender(\n        () => {\n          if (this._tooltipInstance) {\n            this._overlayRef!.updatePosition();\n          }\n        },\n        {\n          injector: this._injector,\n        },\n      );\n    }\n  }\n\n  /** Updates the tooltip class */\n  private _setTooltipClass(tooltipClass: string | string[] | Set<string> | {[key: string]: any}) {\n    if (this._tooltipInstance) {\n      this._tooltipInstance.tooltipClass = tooltipClass;\n      this._tooltipInstance._markForCheck();\n    }\n  }\n\n  /** Inverts an overlay position. */\n  private _invertPosition(x: HorizontalConnectionPos, y: VerticalConnectionPos) {\n    if (this.position === 'above' || this.position === 'below') {\n      if (y === 'top') {\n        y = 'bottom';\n      } else if (y === 'bottom') {\n        y = 'top';\n      }\n    } else {\n      if (x === 'end') {\n        x = 'start';\n      } else if (x === 'start') {\n        x = 'end';\n      }\n    }\n\n    return {x, y};\n  }\n\n  /** Updates the class on the overlay panel based on the current position of the tooltip. */\n  private _updateCurrentPositionClass(connectionPair: ConnectionPositionPair): void {\n    const {overlayY, originX, originY} = connectionPair;\n    let newPosition: TooltipPosition;\n\n    // If the overlay is in the middle along the Y axis,\n    // it means that it's either before or after.\n    if (overlayY === 'center') {\n      // Note that since this information is used for styling, we want to\n      // resolve `start` and `end` to their real values, otherwise consumers\n      // would have to remember to do it themselves on each consumption.\n      if (this._dir && this._dir.value === 'rtl') {\n        newPosition = originX === 'end' ? 'left' : 'right';\n      } else {\n        newPosition = originX === 'start' ? 'left' : 'right';\n      }\n    } else {\n      newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n    }\n\n    if (newPosition !== this._currentPosition) {\n      const overlayRef = this._overlayRef;\n\n      if (overlayRef) {\n        const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n        overlayRef.removePanelClass(classPrefix + this._currentPosition);\n        overlayRef.addPanelClass(classPrefix + newPosition);\n      }\n\n      this._currentPosition = newPosition;\n    }\n  }\n\n  /** Binds the pointer events to the tooltip trigger. */\n  private _setupPointerEnterEventsIfNeeded() {\n    // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n    if (\n      this._disabled ||\n      !this.message ||\n      !this._viewInitialized ||\n      this._passiveListeners.length\n    ) {\n      return;\n    }\n\n    // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n    // first tap from firing its click event or can cause the tooltip to open for clicks.\n    if (this._platformSupportsMouseEvents()) {\n      this._passiveListeners.push([\n        'mouseenter',\n        event => {\n          this._setupPointerExitEventsIfNeeded();\n          let point = undefined;\n          if ((event as MouseEvent).x !== undefined && (event as MouseEvent).y !== undefined) {\n            point = event as MouseEvent;\n          }\n          this.show(undefined, point);\n        },\n      ]);\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n\n      this._passiveListeners.push([\n        'touchstart',\n        event => {\n          const touch = (event as TouchEvent).targetTouches?.[0];\n          const origin = touch ? {x: touch.clientX, y: touch.clientY} : undefined;\n          // Note that it's important that we don't `preventDefault` here,\n          // because it can prevent click events from firing on the element.\n          this._setupPointerExitEventsIfNeeded();\n          if (this._touchstartTimeout) {\n            clearTimeout(this._touchstartTimeout);\n          }\n\n          const DEFAULT_LONGPRESS_DELAY = 500;\n          this._touchstartTimeout = setTimeout(() => {\n            this._touchstartTimeout = null;\n            this.show(undefined, origin);\n          }, this._defaultOptions?.touchLongPressShowDelay ?? DEFAULT_LONGPRESS_DELAY);\n        },\n      ]);\n    }\n\n    this._addListeners(this._passiveListeners);\n  }\n\n  private _setupPointerExitEventsIfNeeded() {\n    if (this._pointerExitEventsInitialized) {\n      return;\n    }\n    this._pointerExitEventsInitialized = true;\n\n    const exitListeners: (readonly [string, EventListenerOrEventListenerObject])[] = [];\n    if (this._platformSupportsMouseEvents()) {\n      exitListeners.push(\n        [\n          'mouseleave',\n          event => {\n            const newTarget = (event as MouseEvent).relatedTarget as Node | null;\n            if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n              this.hide();\n            }\n          },\n        ],\n        ['wheel', event => this._wheelListener(event as WheelEvent)],\n      );\n    } else if (this.touchGestures !== 'off') {\n      this._disableNativeGesturesIfNecessary();\n      const touchendListener = () => {\n        if (this._touchstartTimeout) {\n          clearTimeout(this._touchstartTimeout);\n        }\n        this.hide(this._defaultOptions?.touchendHideDelay);\n      };\n\n      exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n    }\n\n    this._addListeners(exitListeners);\n    this._passiveListeners.push(...exitListeners);\n  }\n\n  private _addListeners(listeners: (readonly [string, EventListenerOrEventListenerObject])[]) {\n    listeners.forEach(([event, listener]) => {\n      this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n    });\n  }\n\n  private _platformSupportsMouseEvents() {\n    return !this._platform.IOS && !this._platform.ANDROID;\n  }\n\n  /** Listener for the `wheel` event on the element. */\n  private _wheelListener(event: WheelEvent) {\n    if (this._isTooltipVisible()) {\n      const elementUnderPointer = this._injector\n        .get(DOCUMENT)\n        .elementFromPoint(event.clientX, event.clientY);\n      const element = this._elementRef.nativeElement;\n\n      // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n      // won't fire if the user scrolls away using the wheel without moving their cursor. We\n      // work around it by finding the element under the user's cursor and closing the tooltip\n      // if it's not the trigger.\n      if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n        this.hide();\n      }\n    }\n  }\n\n  /** Disables the native browser gestures, based on how the tooltip has been configured. */\n  private _disableNativeGesturesIfNecessary() {\n    const gestures = this.touchGestures;\n\n    if (gestures !== 'off') {\n      const element = this._elementRef.nativeElement;\n      const style = element.style;\n\n      // If gestures are set to `auto`, we don't disable text selection on inputs and\n      // textareas, because it prevents the user from typing into them on iOS Safari.\n      if (gestures === 'on' || (element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA')) {\n        style.userSelect =\n          (style as any).msUserSelect =\n          style.webkitUserSelect =\n          (style as any).MozUserSelect =\n            'none';\n      }\n\n      // If we have `auto` gestures and the element uses native HTML dragging,\n      // we don't set `-webkit-user-drag` because it prevents the native behavior.\n      if (gestures === 'on' || !element.draggable) {\n        (style as any).webkitUserDrag = 'none';\n      }\n\n      style.touchAction = 'none';\n      (style as any).webkitTapHighlightColor = 'transparent';\n    }\n  }\n\n  /** Updates the tooltip's ARIA description based on it current state. */\n  private _syncAriaDescription(oldMessage: string): void {\n    if (this._ariaDescriptionPending) {\n      return;\n    }\n\n    this._ariaDescriptionPending = true;\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, oldMessage, 'tooltip');\n\n    // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n    // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n    // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n    // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n    if (!this._isDestroyed) {\n      afterNextRender(\n        {\n          write: () => {\n            this._ariaDescriptionPending = false;\n\n            if (this.message && !this.disabled) {\n              this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n            }\n          },\n        },\n        {injector: this._injector},\n      );\n    }\n  }\n}\n\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\n@Component({\n  selector: 'mat-tooltip-component',\n  templateUrl: 'tooltip.html',\n  styleUrl: 'tooltip.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    '(mouseleave)': '_handleMouseLeave($event)',\n    'aria-hidden': 'true',\n  },\n  imports: [NgClass],\n})\nexport class TooltipComponent implements OnDestroy {\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n  protected _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  /* Whether the tooltip text overflows to multiple lines */\n  _isMultiline = false;\n\n  /** Message to display in the tooltip */\n  message: string;\n\n  /** Classes to be added to the tooltip. Supports the same syntax as `ngClass`. */\n  tooltipClass: string | string[] | Set<string> | {[key: string]: any};\n\n  /** The timeout ID of any current timer set to show the tooltip */\n  private _showTimeoutId: ReturnType<typeof setTimeout> | undefined;\n\n  /** The timeout ID of any current timer set to hide the tooltip */\n  private _hideTimeoutId: ReturnType<typeof setTimeout> | undefined;\n\n  /** Element that caused the tooltip to open. */\n  _triggerElement: HTMLElement;\n\n  /** Amount of milliseconds to delay the closing sequence. */\n  _mouseLeaveHideDelay: number;\n\n  /** Whether animations are currently disabled. */\n  private _animationsDisabled = _animationsDisabled();\n\n  /** Reference to the internal tooltip element. */\n  @ViewChild('tooltip', {\n    // Use a static query here since we interact directly with\n    // the DOM which can happen before `ngAfterViewInit`.\n    static: true,\n  })\n  _tooltip: ElementRef<HTMLElement>;\n\n  /** Whether interactions on the page should close the tooltip */\n  private _closeOnInteraction = false;\n\n  /** Whether the tooltip is currently visible. */\n  private _isVisible = false;\n\n  /** Subject for notifying that the tooltip has been hidden from the view */\n  private readonly _onHide: Subject<void> = new Subject();\n\n  /** Name of the show animation and the class that toggles it. */\n  private readonly _showAnimation = 'mat-mdc-tooltip-show';\n\n  /** Name of the hide animation and the class that toggles it. */\n  private readonly _hideAnimation = 'mat-mdc-tooltip-hide';\n\n  constructor(...args: unknown[]);\n\n  constructor() {}\n\n  /**\n   * Shows the tooltip with an animation originating from the provided origin\n   * @param delay Amount of milliseconds to the delay showing the tooltip.\n   */\n  show(delay: number): void {\n    // Cancel the delayed hide if it is scheduled\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n\n    this._showTimeoutId = setTimeout(() => {\n      this._toggleVisibility(true);\n      this._showTimeoutId = undefined;\n    }, delay);\n  }\n\n  /**\n   * Begins the animation to hide the tooltip after the provided delay in ms.\n   * @param delay Amount of milliseconds to delay showing the tooltip.\n   */\n  hide(delay: number): void {\n    // Cancel the delayed show if it is scheduled\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n\n    this._hideTimeoutId = setTimeout(() => {\n      this._toggleVisibility(false);\n      this._hideTimeoutId = undefined;\n    }, delay);\n  }\n\n  /** Returns an observable that notifies when the tooltip has been hidden from view. */\n  afterHidden(): Observable<void> {\n    return this._onHide;\n  }\n\n  /** Whether the tooltip is being displayed. */\n  isVisible(): boolean {\n    return this._isVisible;\n  }\n\n  ngOnDestroy() {\n    this._cancelPendingAnimations();\n    this._onHide.complete();\n    this._triggerElement = null!;\n  }\n\n  /**\n   * Interactions on the HTML body should close the tooltip immediately as defined in the\n   * material design spec.\n   * https://material.io/design/components/tooltips.html#behavior\n   */\n  _handleBodyInteraction(): void {\n    if (this._closeOnInteraction) {\n      this.hide(0);\n    }\n  }\n\n  /**\n   * Marks that the tooltip needs to be checked in the next change detection run.\n   * Mainly used for rendering the initial text before positioning a tooltip, which\n   * can be problematic in components with OnPush change detection.\n   */\n  _markForCheck(): void {\n    this._changeDetectorRef.markForCheck();\n  }\n\n  _handleMouseLeave({relatedTarget}: MouseEvent) {\n    if (!relatedTarget || !this._triggerElement.contains(relatedTarget as Node)) {\n      if (this.isVisible()) {\n        this.hide(this._mouseLeaveHideDelay);\n      } else {\n        this._finalizeAnimation(false);\n      }\n    }\n  }\n\n  /**\n   * Callback for when the timeout in this.show() gets completed.\n   * This method is only needed by the mdc-tooltip, and so it is only implemented\n   * in the mdc-tooltip, not here.\n   */\n  protected _onShow(): void {\n    this._isMultiline = this._isTooltipMultiline();\n    this._markForCheck();\n  }\n\n  /** Whether the tooltip text has overflown to the next line */\n  private _isTooltipMultiline() {\n    const rect = this._elementRef.nativeElement.getBoundingClientRect();\n    return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n  }\n\n  /** Event listener dispatched when an animation on the tooltip finishes. */\n  _handleAnimationEnd({animationName}: AnimationEvent) {\n    if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n      this._finalizeAnimation(animationName === this._showAnimation);\n    }\n  }\n\n  /** Cancels any pending animation sequences. */\n  _cancelPendingAnimations() {\n    if (this._showTimeoutId != null) {\n      clearTimeout(this._showTimeoutId);\n    }\n\n    if (this._hideTimeoutId != null) {\n      clearTimeout(this._hideTimeoutId);\n    }\n\n    this._showTimeoutId = this._hideTimeoutId = undefined;\n  }\n\n  /** Handles the cleanup after an animation has finished. */\n  private _finalizeAnimation(toVisible: boolean) {\n    if (toVisible) {\n      this._closeOnInteraction = true;\n    } else if (!this.isVisible()) {\n      this._onHide.next();\n    }\n  }\n\n  /** Toggles the visibility of the tooltip element. */\n  private _toggleVisibility(isVisible: boolean) {\n    // We set the classes directly here ourselves so that toggling the tooltip state\n    // isn't bound by change detection. This allows us to hide it even if the\n    // view ref has been detached from the CD tree.\n    const tooltip = this._tooltip.nativeElement;\n    const showClass = this._showAnimation;\n    const hideClass = this._hideAnimation;\n    tooltip.classList.remove(isVisible ? hideClass : showClass);\n    tooltip.classList.add(isVisible ? showClass : hideClass);\n    if (this._isVisible !== isVisible) {\n      this._isVisible = isVisible;\n      this._changeDetectorRef.markForCheck();\n    }\n\n    // It's common for internal apps to disable animations using `* { animation: none !important }`\n    // which can break the opening sequence. Try to detect such cases and work around them.\n    if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n      const styles = getComputedStyle(tooltip);\n\n      // Use `getPropertyValue` to avoid issues with property renaming.\n      if (\n        styles.getPropertyValue('animation-duration') === '0s' ||\n        styles.getPropertyValue('animation-name') === 'none'\n      ) {\n        this._animationsDisabled = true;\n      }\n    }\n\n    if (isVisible) {\n      this._onShow();\n    }\n\n    if (this._animationsDisabled) {\n      tooltip.classList.add('_mat-animation-noopable');\n      this._finalizeAnimation(isVisible);\n    }\n  }\n}\n", "<div\n  #tooltip\n  class=\"mdc-tooltip mat-mdc-tooltip\"\n  [ngClass]=\"tooltipClass\"\n  (animationend)=\"_handleAnimationEnd($event)\"\n  [class.mdc-tooltip--multiline]=\"_isMultiline\">\n  <div class=\"mat-mdc-tooltip-surface mdc-tooltip__surface\">{{message}}</div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {A11yModule} from '@angular/cdk/a11y';\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {CdkScrollableModule} from '@angular/cdk/scrolling';\nimport {MatCommonModule} from '../core';\nimport {\n  MatTooltip,\n  TooltipComponent,\n  MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER,\n} from './tooltip';\n\n@NgModule({\n  imports: [A11yModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n  exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n  providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n})\nexport class MatTooltipModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAqEA;AACO,MAAM,kBAAkB,GAAG;AAElC;;;AAGG;AACG,SAAU,iCAAiC,CAAC,QAAgB,EAAA;AAChE,IAAA,OAAO,KAAK,CAAC,CAAA,kBAAA,EAAqB,QAAQ,CAAA,aAAA,CAAe,CAAC;AAC5D;AAEA;MACa,2BAA2B,GAAG,IAAI,cAAc,CAC3D,6BAA6B,EAC7B;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjC,QAAA,OAAO,MAAM,8BAA8B,CAAC,QAAQ,EAAE,EAAC,cAAc,EAAE,kBAAkB,EAAC,CAAC;KAC5F;AACF,CAAA;AAGH;;;;AAIG;AACG,SAAU,mCAAmC,CAAC,QAAiB,EAAA;AACnE,IAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjC,IAAA,OAAO,MAAM,8BAA8B,CAAC,QAAQ,EAAE,EAAC,cAAc,EAAE,kBAAkB,EAAC,CAAC;AAC7F;AAEA;;;;AAIG;AACU,MAAA,4CAA4C,GAAG;AAC1D,IAAA,OAAO,EAAE,2BAA2B;AACpC,IAAA,IAAI,EAAE,EAAW;AACjB,IAAA,UAAU,EAAE,mCAAmC;;AAGjD;;;;AAIG;SACa,mCAAmC,GAAA;IACjD,OAAO;AACL,QAAA,SAAS,EAAE,CAAC;AACZ,QAAA,SAAS,EAAE,CAAC;AACZ,QAAA,iBAAiB,EAAE,IAAI;KACxB;AACH;AAEA;MACa,2BAA2B,GAAG,IAAI,cAAc,CAC3D,6BAA6B,EAC7B;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,mCAAmC;AAC7C,CAAA;AAuCH;;;;AAIG;AACI,MAAM,mBAAmB,GAAG;AAEnC,MAAM,WAAW,GAAG,eAAe;AAEnC;AACA,MAAM,sBAAsB,GAAG,+BAA+B,CAAC,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;AAE/E;AACA;AACA,MAAM,8BAA8B,GAAG,CAAC;AACxC,MAAM,oBAAoB,GAAG,CAAC;AAC9B,MAAM,UAAU,GAAG,EAAE;AACrB,MAAM,SAAS,GAAG,GAAG;AAErB;;;;;AAKG;MASU,UAAU,CAAA;AACb,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC;AACtC,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AAClC,IAAA,IAAI,GAAG,MAAM,CAAC,cAAc,CAAC;AAC/B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;IAC5C,mBAAmB,GAAG,mBAAmB,EAAE;AAC3C,IAAA,eAAe,GAAG,MAAM,CAA2B,2BAA2B,EAAE;AACtF,QAAA,QAAQ,EAAE,IAAI;AACf,KAAA,CAAC;AAEF,IAAA,WAAW;AACX,IAAA,gBAAgB;IAChB,kBAAkB,CAAuB;AAEjC,IAAA,OAAO;IACP,SAAS,GAAoB,OAAO;IACpC,iBAAiB,GAAY,KAAK;IAClC,SAAS,GAAY,KAAK;AAC1B,IAAA,aAAa;IACb,gBAAgB,GAAG,KAAK;IACxB,6BAA6B,GAAG,KAAK;IAC5B,iBAAiB,GAAG,gBAAgB;IAC7C,eAAe,GAAG,CAAC;AACnB,IAAA,gBAAgB;IACP,eAAe,GAAW,SAAS;AAC5C,IAAA,uBAAuB;IACvB,cAAc,GAAG,KAAK;;AAG9B,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAGvB,IAAI,QAAQ,CAAC,KAAsB,EAAA;AACjC,QAAA,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE;AAC5B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AAEtB,YAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,gBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC;AACtC,gBAAA,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,CAAC;AAC9B,gBAAA,IAAI,CAAC,WAAW,CAAC,cAAc,EAAE;;;;AAKvC;;;AAGG;AACH,IAAA,IACI,gBAAgB,GAAA;QAClB,OAAO,IAAI,CAAC,iBAAiB;;IAG/B,IAAI,gBAAgB,CAAC,KAAmB,EAAA;AACtC,QAAA,IAAI,CAAC,iBAAiB,GAAG,qBAAqB,CAAC,KAAK,CAAC;QACrD,IAAI,CAAC,OAAO,EAAE;AACd,QAAA,IAAI,CAAC,WAAW,GAAG,IAAI;;;AAIzB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAGvB,IAAI,QAAQ,CAAC,KAAmB,EAAA;AAC9B,QAAA,MAAM,UAAU,GAAG,qBAAqB,CAAC,KAAK,CAAC;AAE/C,QAAA,IAAI,IAAI,CAAC,SAAS,KAAK,UAAU,EAAE;AACjC,YAAA,IAAI,CAAC,SAAS,GAAG,UAAU;;YAG3B,IAAI,UAAU,EAAE;AACd,gBAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;iBACP;gBACL,IAAI,CAAC,gCAAgC,EAAE;;AAGzC,YAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC;;;;AAK3C,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAGxB,IAAI,SAAS,CAAC,KAAkB,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,oBAAoB,CAAC,KAAK,CAAC;;AAGvC,IAAA,UAAU;;AAGlB,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAGxB,IAAI,SAAS,CAAC,KAAkB,EAAA;AAC9B,QAAA,IAAI,CAAC,UAAU,GAAG,oBAAoB,CAAC,KAAK,CAAC;AAE7C,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,GAAG,IAAI,CAAC,UAAU;;;AAIxD,IAAA,UAAU;AAElB;;;;;;;;;;;;;AAaG;IAC+B,aAAa,GAAyB,MAAM;;AAG9E,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAGtB,IAAI,OAAO,CAAC,KAAgC,EAAA;AAC1C,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ;;;;QAKhC,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE;QAEzD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC9C,YAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;aACP;YACL,IAAI,CAAC,gCAAgC,EAAE;YACvC,IAAI,CAAC,qBAAqB,EAAE;;AAG9B,QAAA,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC;;IAG/B,QAAQ,GAAG,EAAE;;AAGrB,IAAA,IACI,YAAY,GAAA;QACd,OAAO,IAAI,CAAC,aAAa;;IAG3B,IAAI,YAAY,CAAC,KAA6D,EAAA;AAC5E,QAAA,IAAI,CAAC,aAAa,GAAG,KAAK;AAC1B,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC;;;;IAK5B,iBAAiB,GAChC,EAAE;;IAGI,kBAAkB,GAAyC,IAAI;;AAGtD,IAAA,UAAU,GAAG,IAAI,OAAO,EAAQ;;IAGzC,YAAY,GAAG,KAAK;AAI5B,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe;QAE3C,IAAI,cAAc,EAAE;AAClB,YAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,SAAS;AAC1C,YAAA,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,SAAS;AAE1C,YAAA,IAAI,cAAc,CAAC,QAAQ,EAAE;AAC3B,gBAAA,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAC,QAAQ;;AAGzC,YAAA,IAAI,cAAc,CAAC,gBAAgB,EAAE;AACnC,gBAAA,IAAI,CAAC,gBAAgB,GAAG,cAAc,CAAC,gBAAgB;;AAGzD,YAAA,IAAI,cAAc,CAAC,aAAa,EAAE;AAChC,gBAAA,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,aAAa;;AAGnD,YAAA,IAAI,cAAc,CAAC,YAAY,EAAE;AAC/B,gBAAA,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,YAAY;;;AAInD,QAAA,IAAI,CAAC,eAAe,GAAG,8BAA8B;;IAGvD,eAAe,GAAA;;AAEb,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;QAC5B,IAAI,CAAC,gCAAgC,EAAE;AAEvC,QAAA,IAAI,CAAC;AACF,aAAA,OAAO,CAAC,IAAI,CAAC,WAAW;AACxB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,MAAM,IAAG;;YAElB,IAAI,CAAC,MAAM,EAAE;AACX,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;AAC/B,iBAAA,IAAI,MAAM,KAAK,UAAU,EAAE;AAChC,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;;AAEvC,SAAC,CAAC;;AAGN;;AAEG;IACH,WAAW,GAAA;AACT,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;;AAGpD,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,YAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC;;AAGvC,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;AAC1B,YAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;;AAI9B,QAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAI;YACnD,aAAa,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,sBAAsB,CAAC;AAC5E,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC;AAEjC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;AACtB,QAAA,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;AAE1B,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI;AAExB,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC;AAC7E,QAAA,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,aAAa,CAAC;;;AAIlD,IAAA,IAAI,CAAC,KAAgB,GAAA,IAAI,CAAC,SAAS,EAAE,MAA+B,EAAA;AAClE,QAAA,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC9D,YAAA,IAAI,CAAC,gBAAgB,EAAE,wBAAwB,EAAE;YACjD;;QAGF,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;QAC9C,IAAI,CAAC,OAAO,EAAE;AACd,QAAA,IAAI,CAAC,OAAO;AACV,YAAA,IAAI,CAAC,OAAO,IAAI,IAAI,eAAe,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC;AACrF,QAAA,MAAM,QAAQ,IAAI,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC;QACnF,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AACzD,QAAA,QAAQ,CAAC,oBAAoB,GAAG,IAAI,CAAC,UAAU;QAC/C;AACG,aAAA,WAAW;AACX,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;AAClC,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,CAAC;QACzC,IAAI,CAAC,qBAAqB,EAAE;AAC5B,QAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;;AAItB,IAAA,IAAI,CAAC,KAAA,GAAgB,IAAI,CAAC,SAAS,EAAA;AACjC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB;QAEtC,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,QAAQ,CAAC,SAAS,EAAE,EAAE;AACxB,gBAAA,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;;iBACf;gBACL,QAAQ,CAAC,wBAAwB,EAAE;gBACnC,IAAI,CAAC,OAAO,EAAE;;;;;AAMpB,IAAA,MAAM,CAAC,MAA+B,EAAA;QACpC,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC;;;IAIvE,iBAAiB,GAAA;AACf,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE;;;AAI7D,IAAA,cAAc,CAAC,MAA+B,EAAA;AACpD,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS;AAChD,iBAAA,gBAAqD;AAExD,YAAA,IAAI,CAAC,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,MAAM,KAAK,gBAAgB,CAAC,OAAO,YAAY,UAAU,EAAE;gBACzF,OAAO,IAAI,CAAC,WAAW;;YAGzB,IAAI,CAAC,OAAO,EAAE;;AAGhB,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC;aAC9B,GAAG,CAAC,gBAAgB;AACpB,aAAA,2BAA2B,CAAC,IAAI,CAAC,WAAW,CAAC;QAEhD,MAAM,UAAU,GAAG,CAAG,EAAA,IAAI,CAAC,eAAe,CAAA,CAAA,EAAI,WAAW,CAAA,CAAE;;QAG3D,MAAM,QAAQ,GAAG,uCAAuC,CACtD,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,gBAAgB,GAAG,MAAM,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW;AAEpE,aAAA,qBAAqB,CAAC,CAAI,CAAA,EAAA,IAAI,CAAC,eAAe,UAAU;aACxD,sBAAsB,CAAC,KAAK;AAC5B,aAAA,kBAAkB,CAAC,IAAI,CAAC,eAAe;aACvC,wBAAwB,CAAC,mBAAmB,CAAC;AAEhD,QAAA,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,IAAG;AAC3E,YAAA,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,cAAc,CAAC;AAEvD,YAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,gBAAA,IAAI,MAAM,CAAC,wBAAwB,CAAC,gBAAgB,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE;;;AAGzF,oBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;;AAG1C,SAAC,CAAC;QAEF,IAAI,CAAC,WAAW,GAAG,gBAAgB,CAAC,IAAI,CAAC,SAAS,EAAE;YAClD,SAAS,EAAE,IAAI,CAAC,IAAI;AACpB,YAAA,gBAAgB,EAAE,QAAQ;AAC1B,YAAA,UAAU,EAAE,IAAI,CAAC,kBAAkB,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,GAAG,UAAU;YAC3F,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,2BAA2B,CAAC,EAAE;YACjE,iBAAiB,EAAE,IAAI,CAAC,mBAAmB;AAC5C,SAAA,CAAC;AAEF,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC;AAEtC,QAAA,IAAI,CAAC;AACF,aAAA,WAAW;AACX,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;AAElC,QAAA,IAAI,CAAC;AACF,aAAA,oBAAoB;AACpB,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,MAAM,IAAI,CAAC,gBAAgB,EAAE,sBAAsB,EAAE,CAAC;AAEnE,QAAA,IAAI,CAAC;AACF,aAAA,aAAa;AACb,aAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC;aAC/B,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBAClF,KAAK,CAAC,cAAc,EAAE;gBACtB,KAAK,CAAC,eAAe,EAAE;AACvB,gBAAA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;AAExC,SAAC,CAAC;AAEJ,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE,2BAA2B,EAAE;YACrD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAG,EAAA,IAAI,CAAC,eAAe,CAAgC,8BAAA,CAAA,CAAC;;AAGzF,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AACxB,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI;AAC1B,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS,CAAC,MAAK;AAC/D,gBAAA,IAAI,IAAI,CAAC,WAAW,EAAE;AACpB,oBAAA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC;;AAE1C,aAAC,CAAC;;QAGJ,OAAO,IAAI,CAAC,WAAW;;;IAIjB,OAAO,GAAA;QACb,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;AACtD,YAAA,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;;AAG3B,QAAA,IAAI,CAAC,gBAAgB,GAAG,IAAI;;;AAItB,IAAA,eAAe,CAAC,UAAsB,EAAA;QAC5C,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC,gBAAqD;AAC7F,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,EAAE;AAChC,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,EAAE;QAE1C,QAAQ,CAAC,aAAa,CAAC;AACrB,YAAA,IAAI,CAAC,UAAU,CAAC,EAAC,GAAG,MAAM,CAAC,IAAI,EAAE,GAAG,OAAO,CAAC,IAAI,EAAC,CAAC;AAClD,YAAA,IAAI,CAAC,UAAU,CAAC,EAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAC,CAAC;AAC3D,SAAA,CAAC;;;AAIM,IAAA,UAAU,CAAC,QAA2B,EAAA;QAC9C,MAAM,MAAM,GAAG,oBAAoB;AACnC,QAAA,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK;AAEpD,QAAA,IAAI,QAAQ,CAAC,OAAO,KAAK,KAAK,EAAE;AAC9B,YAAA,QAAQ,CAAC,OAAO,GAAG,CAAC,MAAM;;AACrB,aAAA,IAAI,QAAQ,CAAC,OAAO,KAAK,QAAQ,EAAE;AACxC,YAAA,QAAQ,CAAC,OAAO,GAAG,MAAM;;AACpB,aAAA,IAAI,QAAQ,CAAC,OAAO,KAAK,OAAO,EAAE;AACvC,YAAA,QAAQ,CAAC,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,GAAG,MAAM;;AACtC,aAAA,IAAI,QAAQ,CAAC,OAAO,KAAK,KAAK,EAAE;AACrC,YAAA,QAAQ,CAAC,OAAO,GAAG,KAAK,GAAG,MAAM,GAAG,CAAC,MAAM;;AAG7C,QAAA,OAAO,QAAQ;;AAGjB;;;AAGG;IACH,UAAU,GAAA;AACR,QAAA,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK;AACpD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC9B,QAAA,IAAI,cAAwC;QAE5C,IAAI,QAAQ,IAAI,OAAO,IAAI,QAAQ,IAAI,OAAO,EAAE;YAC9C,cAAc,GAAG,EAAC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,IAAI,OAAO,GAAG,KAAK,GAAG,QAAQ,EAAC;;aAChF,IACL,QAAQ,IAAI,QAAQ;AACpB,aAAC,QAAQ,IAAI,MAAM,IAAI,KAAK,CAAC;aAC5B,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAC/B;YACA,cAAc,GAAG,EAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAC;;aACjD,IACL,QAAQ,IAAI,OAAO;AACnB,aAAC,QAAQ,IAAI,OAAO,IAAI,KAAK,CAAC;aAC7B,QAAQ,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,EAC9B;YACA,cAAc,GAAG,EAAC,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAC;;AAC/C,aAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACxD,YAAA,MAAM,iCAAiC,CAAC,QAAQ,CAAC;;AAGnD,QAAA,MAAM,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,IAAI,CAAC,eAAe,CAAC,cAAe,CAAC,OAAO,EAAE,cAAe,CAAC,OAAO,CAAC;QAErF,OAAO;AACL,YAAA,IAAI,EAAE,cAAe;YACrB,QAAQ,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAC;SACnC;;;IAIH,mBAAmB,GAAA;AACjB,QAAA,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK;AACpD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ;AAC9B,QAAA,IAAI,eAA0C;AAE9C,QAAA,IAAI,QAAQ,IAAI,OAAO,EAAE;YACvB,eAAe,GAAG,EAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAC;;AACrD,aAAA,IAAI,QAAQ,IAAI,OAAO,EAAE;YAC9B,eAAe,GAAG,EAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAC;;aAClD,IACL,QAAQ,IAAI,QAAQ;AACpB,aAAC,QAAQ,IAAI,MAAM,IAAI,KAAK,CAAC;aAC5B,QAAQ,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAC/B;YACA,eAAe,GAAG,EAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAC;;aAClD,IACL,QAAQ,IAAI,OAAO;AACnB,aAAC,QAAQ,IAAI,OAAO,IAAI,KAAK,CAAC;aAC7B,QAAQ,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,EAC9B;YACA,eAAe,GAAG,EAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAC;;AACpD,aAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACxD,YAAA,MAAM,iCAAiC,CAAC,QAAQ,CAAC;;AAGnD,QAAA,MAAM,EAAC,CAAC,EAAE,CAAC,EAAC,GAAG,IAAI,CAAC,eAAe,CAAC,eAAgB,CAAC,QAAQ,EAAE,eAAgB,CAAC,QAAQ,CAAC;QAEzF,OAAO;AACL,YAAA,IAAI,EAAE,eAAgB;YACtB,QAAQ,EAAE,EAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAC;SACrC;;;IAIK,qBAAqB,GAAA;;;AAG3B,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO;AAC5C,YAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE;YAErC,eAAe,CACb,MAAK;AACH,gBAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,oBAAA,IAAI,CAAC,WAAY,CAAC,cAAc,EAAE;;AAEtC,aAAC,EACD;gBACE,QAAQ,EAAE,IAAI,CAAC,SAAS;AACzB,aAAA,CACF;;;;AAKG,IAAA,gBAAgB,CAAC,YAAoE,EAAA;AAC3F,QAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,YAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,GAAG,YAAY;AACjD,YAAA,IAAI,CAAC,gBAAgB,CAAC,aAAa,EAAE;;;;IAKjC,eAAe,CAAC,CAA0B,EAAE,CAAwB,EAAA;AAC1E,QAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;AAC1D,YAAA,IAAI,CAAC,KAAK,KAAK,EAAE;gBACf,CAAC,GAAG,QAAQ;;AACP,iBAAA,IAAI,CAAC,KAAK,QAAQ,EAAE;gBACzB,CAAC,GAAG,KAAK;;;aAEN;AACL,YAAA,IAAI,CAAC,KAAK,KAAK,EAAE;gBACf,CAAC,GAAG,OAAO;;AACN,iBAAA,IAAI,CAAC,KAAK,OAAO,EAAE;gBACxB,CAAC,GAAG,KAAK;;;AAIb,QAAA,OAAO,EAAC,CAAC,EAAE,CAAC,EAAC;;;AAIP,IAAA,2BAA2B,CAAC,cAAsC,EAAA;QACxE,MAAM,EAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAC,GAAG,cAAc;AACnD,QAAA,IAAI,WAA4B;;;AAIhC,QAAA,IAAI,QAAQ,KAAK,QAAQ,EAAE;;;;AAIzB,YAAA,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,EAAE;AAC1C,gBAAA,WAAW,GAAG,OAAO,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;;iBAC7C;AACL,gBAAA,WAAW,GAAG,OAAO,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;;;aAEjD;AACL,YAAA,WAAW,GAAG,QAAQ,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;;AAG9E,QAAA,IAAI,WAAW,KAAK,IAAI,CAAC,gBAAgB,EAAE;AACzC,YAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW;YAEnC,IAAI,UAAU,EAAE;gBACd,MAAM,WAAW,GAAG,CAAG,EAAA,IAAI,CAAC,eAAe,CAAA,CAAA,EAAI,WAAW,CAAA,CAAA,CAAG;gBAC7D,UAAU,CAAC,gBAAgB,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAChE,gBAAA,UAAU,CAAC,aAAa,CAAC,WAAW,GAAG,WAAW,CAAC;;AAGrD,YAAA,IAAI,CAAC,gBAAgB,GAAG,WAAW;;;;IAK/B,gCAAgC,GAAA;;QAEtC,IACE,IAAI,CAAC,SAAS;YACd,CAAC,IAAI,CAAC,OAAO;YACb,CAAC,IAAI,CAAC,gBAAgB;AACtB,YAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAC7B;YACA;;;;AAKF,QAAA,IAAI,IAAI,CAAC,4BAA4B,EAAE,EAAE;AACvC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBAC1B,YAAY;AACZ,gBAAA,KAAK,IAAG;oBACN,IAAI,CAAC,+BAA+B,EAAE;oBACtC,IAAI,KAAK,GAAG,SAAS;AACrB,oBAAA,IAAK,KAAoB,CAAC,CAAC,KAAK,SAAS,IAAK,KAAoB,CAAC,CAAC,KAAK,SAAS,EAAE;wBAClF,KAAK,GAAG,KAAmB;;AAE7B,oBAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC;iBAC5B;AACF,aAAA,CAAC;;AACG,aAAA,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE;YACvC,IAAI,CAAC,iCAAiC,EAAE;AAExC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;gBAC1B,YAAY;AACZ,gBAAA,KAAK,IAAG;oBACN,MAAM,KAAK,GAAI,KAAoB,CAAC,aAAa,GAAG,CAAC,CAAC;oBACtD,MAAM,MAAM,GAAG,KAAK,GAAG,EAAC,CAAC,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,KAAK,CAAC,OAAO,EAAC,GAAG,SAAS;;;oBAGvE,IAAI,CAAC,+BAA+B,EAAE;AACtC,oBAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,wBAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC;;oBAGvC,MAAM,uBAAuB,GAAG,GAAG;AACnC,oBAAA,IAAI,CAAC,kBAAkB,GAAG,UAAU,CAAC,MAAK;AACxC,wBAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;AAC9B,wBAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,CAAC;qBAC7B,EAAE,IAAI,CAAC,eAAe,EAAE,uBAAuB,IAAI,uBAAuB,CAAC;iBAC7E;AACF,aAAA,CAAC;;AAGJ,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB,CAAC;;IAGpC,+BAA+B,GAAA;AACrC,QAAA,IAAI,IAAI,CAAC,6BAA6B,EAAE;YACtC;;AAEF,QAAA,IAAI,CAAC,6BAA6B,GAAG,IAAI;QAEzC,MAAM,aAAa,GAA8D,EAAE;AACnF,QAAA,IAAI,IAAI,CAAC,4BAA4B,EAAE,EAAE;YACvC,aAAa,CAAC,IAAI,CAChB;gBACE,YAAY;AACZ,gBAAA,KAAK,IAAG;AACN,oBAAA,MAAM,SAAS,GAAI,KAAoB,CAAC,aAA4B;AACpE,oBAAA,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE;wBACvE,IAAI,CAAC,IAAI,EAAE;;iBAEd;AACF,aAAA,EACD,CAAC,OAAO,EAAE,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,KAAmB,CAAC,CAAC,CAC7D;;AACI,aAAA,IAAI,IAAI,CAAC,aAAa,KAAK,KAAK,EAAE;YACvC,IAAI,CAAC,iCAAiC,EAAE;YACxC,MAAM,gBAAgB,GAAG,MAAK;AAC5B,gBAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,oBAAA,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC;;gBAEvC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,iBAAiB,CAAC;AACpD,aAAC;AAED,YAAA,aAAa,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC;;AAGvF,QAAA,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;QACjC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC;;AAGvC,IAAA,aAAa,CAAC,SAAoE,EAAA;QACxF,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAI;AACtC,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,sBAAsB,CAAC;AAC1F,SAAC,CAAC;;IAGI,4BAA4B,GAAA;AAClC,QAAA,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO;;;AAI/C,IAAA,cAAc,CAAC,KAAiB,EAAA;AACtC,QAAA,IAAI,IAAI,CAAC,iBAAiB,EAAE,EAAE;AAC5B,YAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC;iBAC9B,GAAG,CAAC,QAAQ;iBACZ,gBAAgB,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC;AACjD,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;;;;;AAM9C,YAAA,IAAI,mBAAmB,KAAK,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;gBAC7E,IAAI,CAAC,IAAI,EAAE;;;;;IAMT,iCAAiC,GAAA;AACvC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa;AAEnC,QAAA,IAAI,QAAQ,KAAK,KAAK,EAAE;AACtB,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;AAC9C,YAAA,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK;;;AAI3B,YAAA,IAAI,QAAQ,KAAK,IAAI,KAAK,OAAO,CAAC,QAAQ,KAAK,OAAO,IAAI,OAAO,CAAC,QAAQ,KAAK,UAAU,CAAC,EAAE;AAC1F,gBAAA,KAAK,CAAC,UAAU;AACb,oBAAA,KAAa,CAAC,YAAY;AAC3B,wBAAA,KAAK,CAAC,gBAAgB;AACrB,4BAAA,KAAa,CAAC,aAAa;AAC1B,gCAAA,MAAM;;;;YAKZ,IAAI,QAAQ,KAAK,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;AAC1C,gBAAA,KAAa,CAAC,cAAc,GAAG,MAAM;;AAGxC,YAAA,KAAK,CAAC,WAAW,GAAG,MAAM;AACzB,YAAA,KAAa,CAAC,uBAAuB,GAAG,aAAa;;;;AAKlD,IAAA,oBAAoB,CAAC,UAAkB,EAAA;AAC7C,QAAA,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC;;AAGF,QAAA,IAAI,CAAC,uBAAuB,GAAG,IAAI;AACnC,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,UAAU,EAAE,SAAS,CAAC;;;;;AAM5F,QAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;AACtB,YAAA,eAAe,CACb;gBACE,KAAK,EAAE,MAAK;AACV,oBAAA,IAAI,CAAC,uBAAuB,GAAG,KAAK;oBAEpC,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClC,wBAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC;;iBAExF;aACF,EACD,EAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,EAAC,CAC3B;;;uGArvBM,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAV,UAAU,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,EAAA,UAAA,CAAA,EAAA,gBAAA,EAAA,CAAA,4BAAA,EAAA,kBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,EAAA,UAAA,CAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,EAAA,WAAA,CAAA,EAAA,SAAA,EAAA,CAAA,qBAAA,EAAA,WAAA,CAAA,EAAA,aAAA,EAAA,CAAA,yBAAA,EAAA,eAAA,CAAA,EAAA,OAAA,EAAA,CAAA,YAAA,EAAA,SAAA,CAAA,EAAA,YAAA,EAAA,CAAA,iBAAA,EAAA,cAAA,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,gCAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,yBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBARtB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,cAAc;AACxB,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,yBAAyB;AAClC,wBAAA,kCAAkC,EAAE,UAAU;AAC/C,qBAAA;AACF,iBAAA;wDAmCK,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,oBAAoB;gBAsBvB,gBAAgB,EAAA,CAAA;sBADnB,KAAK;uBAAC,4BAA4B;gBAa/B,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,oBAAoB;gBAwBvB,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,qBAAqB;gBAaxB,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,qBAAqB;gBA6BM,aAAa,EAAA,CAAA;sBAA9C,KAAK;uBAAC,yBAAyB;gBAI5B,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,YAAY;gBA2Bf,YAAY,EAAA,CAAA;sBADf,KAAK;uBAAC,iBAAiB;;AA2lB1B;;;AAGG;MAaU,gBAAgB,CAAA;AACnB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;AAC5C,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;;IAGnE,YAAY,GAAG,KAAK;;AAGpB,IAAA,OAAO;;AAGP,IAAA,YAAY;;AAGJ,IAAA,cAAc;;AAGd,IAAA,cAAc;;AAGtB,IAAA,eAAe;;AAGf,IAAA,oBAAoB;;IAGZ,mBAAmB,GAAG,mBAAmB,EAAE;;AAQnD,IAAA,QAAQ;;IAGA,mBAAmB,GAAG,KAAK;;IAG3B,UAAU,GAAG,KAAK;;AAGT,IAAA,OAAO,GAAkB,IAAI,OAAO,EAAE;;IAGtC,cAAc,GAAG,sBAAsB;;IAGvC,cAAc,GAAG,sBAAsB;AAIxD,IAAA,WAAA,GAAA;AAEA;;;AAGG;AACH,IAAA,IAAI,CAAC,KAAa,EAAA;;AAEhB,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE;AAC/B,YAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;;AAGnC,QAAA,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,MAAK;AACpC,YAAA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;AAC5B,YAAA,IAAI,CAAC,cAAc,GAAG,SAAS;SAChC,EAAE,KAAK,CAAC;;AAGX;;;AAGG;AACH,IAAA,IAAI,CAAC,KAAa,EAAA;;AAEhB,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE;AAC/B,YAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;;AAGnC,QAAA,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,MAAK;AACpC,YAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC;AAC7B,YAAA,IAAI,CAAC,cAAc,GAAG,SAAS;SAChC,EAAE,KAAK,CAAC;;;IAIX,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,OAAO;;;IAIrB,SAAS,GAAA;QACP,OAAO,IAAI,CAAC,UAAU;;IAGxB,WAAW,GAAA;QACT,IAAI,CAAC,wBAAwB,EAAE;AAC/B,QAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;AACvB,QAAA,IAAI,CAAC,eAAe,GAAG,IAAK;;AAG9B;;;;AAIG;IACH,sBAAsB,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;;AAIhB;;;;AAIG;IACH,aAAa,GAAA;AACX,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;IAGxC,iBAAiB,CAAC,EAAC,aAAa,EAAa,EAAA;AAC3C,QAAA,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,aAAqB,CAAC,EAAE;AAC3E,YAAA,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;AACpB,gBAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;;iBAC/B;AACL,gBAAA,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;;;;AAKpC;;;;AAIG;IACO,OAAO,GAAA;AACf,QAAA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,EAAE;QAC9C,IAAI,CAAC,aAAa,EAAE;;;IAId,mBAAmB,GAAA;QACzB,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,qBAAqB,EAAE;QACnE,OAAO,IAAI,CAAC,MAAM,GAAG,UAAU,IAAI,IAAI,CAAC,KAAK,IAAI,SAAS;;;IAI5D,mBAAmB,CAAC,EAAC,aAAa,EAAiB,EAAA;AACjD,QAAA,IAAI,aAAa,KAAK,IAAI,CAAC,cAAc,IAAI,aAAa,KAAK,IAAI,CAAC,cAAc,EAAE;YAClF,IAAI,CAAC,kBAAkB,CAAC,aAAa,KAAK,IAAI,CAAC,cAAc,CAAC;;;;IAKlE,wBAAwB,GAAA;AACtB,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE;AAC/B,YAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;;AAGnC,QAAA,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,EAAE;AAC/B,YAAA,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;;QAGnC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,SAAS;;;AAI/C,IAAA,kBAAkB,CAAC,SAAkB,EAAA;QAC3C,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;;AAC1B,aAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE;AAC5B,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE;;;;AAKf,IAAA,iBAAiB,CAAC,SAAkB,EAAA;;;;AAI1C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa;AAC3C,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc;AACrC,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc;AACrC,QAAA,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;AAC3D,QAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC;AACxD,QAAA,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE;AACjC,YAAA,IAAI,CAAC,UAAU,GAAG,SAAS;AAC3B,YAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;;AAKxC,QAAA,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,OAAO,gBAAgB,KAAK,UAAU,EAAE;AACpF,YAAA,MAAM,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC;;AAGxC,YAAA,IACE,MAAM,CAAC,gBAAgB,CAAC,oBAAoB,CAAC,KAAK,IAAI;gBACtD,MAAM,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,MAAM,EACpD;AACA,gBAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI;;;QAInC,IAAI,SAAS,EAAE;YACb,IAAI,CAAC,OAAO,EAAE;;AAGhB,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,yBAAyB,CAAC;AAChD,YAAA,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC;;;uGArN3B,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,uBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,aAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,EAAA,YAAA,EAAA,2BAAA,EAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,UAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,SAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,MAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECt9B7B,0RAQA,EAAA,MAAA,EAAA,CAAA,6uEAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,ED48BY,OAAO,EAAA,QAAA,EAAA,WAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEN,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAZ5B,SAAS;+BACE,uBAAuB,EAAA,aAAA,EAGlB,iBAAiB,CAAC,IAAI,mBACpB,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA;AACJ,wBAAA,cAAc,EAAE,2BAA2B;AAC3C,wBAAA,aAAa,EAAE,MAAM;qBACtB,EACQ,OAAA,EAAA,CAAC,OAAO,CAAC,EAAA,QAAA,EAAA,0RAAA,EAAA,MAAA,EAAA,CAAA,6uEAAA,CAAA,EAAA;wDAoClB,QAAQ,EAAA,CAAA;sBALP,SAAS;AAAC,gBAAA,IAAA,EAAA,CAAA,SAAS,EAAE;;;AAGpB,wBAAA,MAAM,EAAE,IAAI;AACb,qBAAA;;;ME/9BU,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,YAJjB,UAAU,EAAE,aAAa,EAAE,eAAe,EAAE,UAAU,EAAE,gBAAgB,aACxE,UAAU,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,CAAA,EAAA,CAAA;AAGjE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,EAFhB,SAAA,EAAA,CAAC,4CAA4C,CAAC,EAF/C,OAAA,EAAA,CAAA,UAAU,EAAE,aAAa,EAAE,eAAe,EACZ,eAAe,EAAE,mBAAmB,CAAA,EAAA,CAAA;;2FAGjE,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,eAAe,EAAE,UAAU,EAAE,gBAAgB,CAAC;oBACnF,OAAO,EAAE,CAAC,UAAU,EAAE,gBAAgB,EAAE,eAAe,EAAE,mBAAmB,CAAC;oBAC7E,SAAS,EAAE,CAAC,4CAA4C,CAAC;AAC1D,iBAAA;;;;;"}