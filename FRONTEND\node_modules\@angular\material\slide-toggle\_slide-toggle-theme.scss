@use 'sass:map';
@use '../core/style/sass-utils';
@use '../core/theming/inspection';
@use '../core/tokens/token-utils';
@use '../core/typography/typography';
@use './m2-slide-toggle';
@use './m3-slide-toggle';
@use '../core/tokens/m2-utils';

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  $tokens: map.get(m2-slide-toggle.get-tokens($theme), base);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-slide-toggle.get-tokens($theme), base);
  }

  @include token-utils.values($tokens);
}

/// Outputs color theme styles for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin color($theme, $color-variant: null) {
  $tokens: map.get(m2-slide-toggle.get-tokens($theme), color);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-slide-toggle.get-tokens($theme, $color-variant), color);
  }

  @include token-utils.values($tokens);

  @if inspection.get-theme-version($theme) != 1 {
    $mat-tokens: map.get(m2-slide-toggle.get-tokens($theme), color);
    $system: m2-utils.get-system($theme);

    .mat-mdc-slide-toggle {
      // Change the color palette related tokens to accent or warn if applicable
      &.mat-accent {
        $tokens: m2-slide-toggle.private-get-color-palette-color-tokens($theme, secondary);
        @include token-utils.values($tokens);
      }

      &.mat-warn {
        $tokens: m2-slide-toggle.private-get-color-palette-color-tokens($theme, error);
        @include token-utils.values($tokens);
      }
    }
  }
}

/// Outputs typography theme styles for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.values(
        map.get(m3-slide-toggle.get-tokens($theme), typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      // TODO: See if this can be removed
      @include token-utils.values(map.get(m2-slide-toggle.get-tokens($theme), typography));

      .mat-mdc-slide-toggle {
        @include token-utils.values(
            map.get(m2-slide-toggle.get-tokens($theme), typography));
      }
    }
  }
}

/// Outputs density theme styles for the mat-slide-toggle.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  $tokens: map.get(m2-slide-toggle.get-tokens($theme), density);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-slide-toggle.get-tokens($theme), density);
  }

  @include token-utils.values($tokens);
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: slide-toggle,
      tokens: token-utils.get-overrides(m3-slide-toggle.get-tokens(), slide-toggle)
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-icon.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin theme($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include base($theme);
    @include color($theme, $color-variant);
    @include density($theme);
    @include typography($theme);
  } @else {
    @include base($theme);
    @if inspection.theme-has($theme, color) {
      @include color($theme);
    }
    @if inspection.theme-has($theme, density) {
      @include density($theme);
    }
    @if inspection.theme-has($theme, typography) {
      @include typography($theme);
    }
  }
}
