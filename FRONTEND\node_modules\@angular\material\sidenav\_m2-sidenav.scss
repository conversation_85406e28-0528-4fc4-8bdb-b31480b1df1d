@use 'sass:color';
@use 'sass:map';
@use 'sass:meta';
@use '../core/theming/inspection';
@use '../core/style/elevation';
@use '../core/tokens/m2-utils';
@use '../core/tokens/m3-utils';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);
  // TODO: Use system colors instead of checking theme type
  $is-dark: false;
  @if (meta.type-of($theme) == map and map.get($theme, color)) {
    $is-dark: inspection.get-theme-type($theme) ==  dark;
  }
  $scrim-opacity: 0.6;
  $scrim-color: m3-utils.color-with-opacity(map.get($system, surface), 60%);
  $fallback-scrim-color: if($is-dark, rgba(#fff, $scrim-opacity), rgba(#000, $scrim-opacity));

  @return (
    base: (
      sidenav-container-shape: 0,
      sidenav-container-elevation-shadow: elevation.get-box-shadow(16),
      sidenav-container-width: auto,
    ),
    color: (
      sidenav-container-divider-color: map.get($system, outline),
      sidenav-container-background-color: map.get($system, surface),
      sidenav-container-text-color: map.get($system, on-surface),
      sidenav-content-background-color: map.get($system, background),
      sidenav-content-text-color: map.get($system, on-surface),

      // We use invert() here to have the darken the background color expected to be used.
      // If the background is light, we use a dark backdrop. If the background is dark, we
      // use a light backdrop. If the value isn't a color, Sass will throw an error so we
      // fall back to something generic.
      sidenav-scrim-color: if(meta.type-of($scrim-color) == color,
        color.invert($scrim-color), $fallback-scrim-color),
    ),
    typography: (),
    density: (),
  );
}
