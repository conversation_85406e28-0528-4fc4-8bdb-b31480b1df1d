@use 'sass:color';
@use 'sass:meta';
@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/tokens/m3-utils';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);

  @return (
    base: (
      progress-bar-active-indicator-height: 4px,
      progress-bar-track-height: 4px,
      progress-bar-track-shape: 0,
    ),
    color: private-get-color-palette-color-tokens($theme, primary),
    typography: (),
    density: (),
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function private-get-color-palette-color-tokens($theme, $color-variant) {
  $system: m2-utils.get-system($theme);
  $system: m3-utils.replace-colors-with-variant($system, primary, $color-variant);

  @return (
    progress-bar-active-indicator-color: map.get($system, primary),
    progress-bar-track-color: if(
        meta.type-of(map.get($system, primary)) == color,
        color.adjust(map.get($system, primary), $alpha: -0.75),
        map.get($system, primary)
    )
  );
}
