"use strict";
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v6.30.2
// source: envelope.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports.Signature = exports.Envelope = void 0;
exports.Envelope = {
    fromJSON(object) {
        return {
            payload: isSet(object.payload) ? Buffer.from(bytesFromBase64(object.payload)) : Buffer.alloc(0),
            payloadType: isSet(object.payloadType) ? globalThis.String(object.payloadType) : "",
            signatures: globalThis.Array.isArray(object?.signatures)
                ? object.signatures.map((e) => exports.Signature.fromJSON(e))
                : [],
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.payload.length !== 0) {
            obj.payload = base64FromBytes(message.payload);
        }
        if (message.payloadType !== "") {
            obj.payloadType = message.payloadType;
        }
        if (message.signatures?.length) {
            obj.signatures = message.signatures.map((e) => exports.Signature.toJSON(e));
        }
        return obj;
    },
};
exports.Signature = {
    fromJSON(object) {
        return {
            sig: isSet(object.sig) ? Buffer.from(bytesFromBase64(object.sig)) : Buffer.alloc(0),
            keyid: isSet(object.keyid) ? globalThis.String(object.keyid) : "",
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.sig.length !== 0) {
            obj.sig = base64FromBytes(message.sig);
        }
        if (message.keyid !== "") {
            obj.keyid = message.keyid;
        }
        return obj;
    },
};
function bytesFromBase64(b64) {
    return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}
function base64FromBytes(arr) {
    return globalThis.Buffer.from(arr).toString("base64");
}
function isSet(value) {
    return value !== null && value !== undefined;
}
