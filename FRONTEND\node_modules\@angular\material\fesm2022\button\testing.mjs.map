{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/button/testing/button-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {booleanAttribute} from '@angular/core';\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {ButtonAppearance, ButtonHarnessFilters, ButtonVariant} from './button-harness-filters';\n\n/** Harness for interacting with a mat-button in tests. */\nexport class MatButtonHarness extends ContentContainerComponentHarness {\n  // Note: `.mat-mdc-button-base` should be enough for all buttons, however some apps are using\n  // the harness without actually having an applied button. Keep the attributes for backwards\n  // compatibility.\n\n  /** Selector for the harness. */\n  static hostSelector = `.mat-mdc-button-base, [matButton], [mat-button], [matIconButton],\n    [matFab], [matMiniFab], [mat-raised-button], [mat-flat-button], [mat-icon-button],\n    [mat-stroked-button], [mat-fab], [mat-mini-fab]`;\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a button with specific attributes.\n   * @param options Options for narrowing the search:\n   *   - `selector` finds a button whose host element matches the given selector.\n   *   - `text` finds a button with specific text content.\n   *   - `variant` finds buttons matching a specific variant.\n   *   - `appearance` finds buttons matching a specific appearance.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatButtonHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ButtonHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('text', options.text, (harness, text) =>\n        HarnessPredicate.stringMatches(harness.getText(), text),\n      )\n      .addOption('variant', options.variant, (harness, variant) =>\n        HarnessPredicate.stringMatches(harness.getVariant(), variant),\n      )\n      .addOption('appearance', options.appearance, (harness, appearance) =>\n        HarnessPredicate.stringMatches(harness.getAppearance(), appearance),\n      )\n      .addOption('disabled', options.disabled, async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      });\n  }\n\n  /**\n   * Clicks the button at the given position relative to its top-left.\n   * @param relativeX The relative x position of the click.\n   * @param relativeY The relative y position of the click.\n   */\n  click(relativeX: number, relativeY: number): Promise<void>;\n  /** Clicks the button at its center. */\n  click(location: 'center'): Promise<void>;\n  /** Clicks the button. */\n  click(): Promise<void>;\n  async click(...args: [] | ['center'] | [number, number]): Promise<void> {\n    return (await this.host()).click(...(args as []));\n  }\n\n  /** Gets a boolean promise indicating if the button is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const host = await this.host();\n    return (\n      booleanAttribute(await host.getAttribute('disabled')) ||\n      (await host.hasClass('mat-mdc-button-disabled'))\n    );\n  }\n\n  /** Gets a promise for the button's label text. */\n  async getText(): Promise<string> {\n    return (await this.host()).text();\n  }\n\n  /** Focuses the button and returns a void promise that indicates when the action is complete. */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /** Blurs the button and returns a void promise that indicates when the action is complete. */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the button is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n\n  /** Gets the variant of the button. */\n  async getVariant(): Promise<ButtonVariant> {\n    const host = await this.host();\n\n    // TODO(crisbeto): we're checking both classes and attributes for backwards compatibility\n    // with some internal apps that were applying the attribute without importing the directive.\n    // Really we should be only targeting the classes.\n    if (\n      (await host.hasClass('mat-mdc-icon-button')) ||\n      (await host.getAttribute('mat-icon-button')) != null\n    ) {\n      return 'icon';\n    }\n\n    if (\n      (await host.hasClass('mat-mdc-mini-fab')) ||\n      (await host.getAttribute('mat-mini-fab')) != null\n    ) {\n      return 'mini-fab';\n    }\n\n    if ((await host.hasClass('mat-mdc-fab')) || (await host.getAttribute('mat-fab')) != null) {\n      return 'fab';\n    }\n\n    return 'basic';\n  }\n\n  /** Gets the appearance of the button. */\n  async getAppearance(): Promise<ButtonAppearance | null> {\n    const host = await this.host();\n\n    if (await host.hasClass('mat-mdc-outlined-button')) {\n      return 'outlined';\n    }\n\n    if (await host.hasClass('mat-mdc-raised-button')) {\n      return 'elevated';\n    }\n\n    if (await host.hasClass('mat-mdc-unelevated-button')) {\n      return 'filled';\n    }\n\n    if (await host.hasClass('mat-mdc-button')) {\n      return 'text';\n    }\n\n    if (await host.hasClass('mat-tonal-button')) {\n      return 'tonal';\n    }\n\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;;AAgBA;AACM,MAAO,gBAAiB,SAAQ,gCAAgC,CAAA;;;;;IAMpE,OAAO,YAAY,GAAG,CAAA;;oDAE4B;AAElD;;;;;;;;AAQG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAgC,EAAE,EAAA;AAElC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;aACtC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAC7C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;aAExD,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,OAAO,KACtD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,OAAO,CAAC;aAE9D,SAAS,CAAC,YAAY,EAAE,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,UAAU,KAC/D,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,aAAa,EAAE,EAAE,UAAU,CAAC;AAEpE,aAAA,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAI;YACnE,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;AAClD,SAAC,CAAC;;AAaN,IAAA,MAAM,KAAK,CAAC,GAAG,IAAwC,EAAA;AACrD,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,CAAC,GAAI,IAAW,CAAC;;;AAInD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,QACE,gBAAgB,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;aACpD,MAAM,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;;;AAKpD,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;AAIpC,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE;;;AAIxC,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;;;;QAK9B,IACE,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,qBAAqB,CAAC;YAC3C,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,KAAK,IAAI,EACpD;AACA,YAAA,OAAO,MAAM;;QAGf,IACE,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;YACxC,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,IAAI,EACjD;AACA,YAAA,OAAO,UAAU;;QAGnB,IAAI,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,IAAI,EAAE;AACxF,YAAA,OAAO,KAAK;;AAGd,QAAA,OAAO,OAAO;;;AAIhB,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAE9B,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,EAAE;AAClD,YAAA,OAAO,UAAU;;QAGnB,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAE;AAChD,YAAA,OAAO,UAAU;;QAGnB,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,2BAA2B,CAAC,EAAE;AACpD,YAAA,OAAO,QAAQ;;QAGjB,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE;AACzC,YAAA,OAAO,MAAM;;QAGf,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;AAC3C,YAAA,OAAO,OAAO;;AAGhB,QAAA,OAAO,IAAI;;;;;;"}