@use 'sass:map';
@use '../../tokens/m2-utils';
@use '../../tokens/m3-utils';

@function get-tokens($theme) {
  @return (
    base: (),
    color: private-get-color-palette-color-tokens($theme, secondary),
    typography: (),
    density: (),
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function private-get-color-palette-color-tokens($theme, $color-variant) {
  $system: m2-utils.get-system($theme);
  $system: m3-utils.replace-colors-with-variant($system, secondary, $color-variant);

  @return (
    pseudo-checkbox-full-selected-icon-color: map.get($system, secondary),
    pseudo-checkbox-full-selected-checkmark-color: map.get($system, background),
    pseudo-checkbox-full-unselected-icon-color: map.get($system, on-surface-variant),
    pseudo-checkbox-full-disabled-selected-checkmark-color: map.get($system, background),
    pseudo-checkbox-full-disabled-unselected-icon-color:
        m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
    pseudo-checkbox-full-disabled-selected-icon-color:
        m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
    pseudo-checkbox-minimal-selected-checkmark-color: map.get($system, secondary),
    pseudo-checkbox-minimal-disabled-selected-checkmark-color:
        m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
  );
}
