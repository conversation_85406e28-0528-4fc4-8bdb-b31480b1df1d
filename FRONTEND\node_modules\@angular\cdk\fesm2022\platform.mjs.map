{"version": 3, "file": "platform.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/platform/platform-module.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/platform/features/input-types.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\n\n@NgModule({})\nexport class PlatformModule {}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Cached result Set of input types support by the current browser. */\nlet supportedInputTypes: Set<string>;\n\n/** Types of `<input>` that *might* be supported. */\nconst candidateInputTypes = [\n  // `color` must come first. Chrome 56 shows a warning if we change the type to `color` after\n  // first changing it to something else:\n  // The specified value \"\" does not conform to the required format.\n  // The format is \"#rrggbb\" where rr, gg, bb are two-digit hexadecimal numbers.\n  'color',\n  'button',\n  'checkbox',\n  'date',\n  'datetime-local',\n  'email',\n  'file',\n  'hidden',\n  'image',\n  'month',\n  'number',\n  'password',\n  'radio',\n  'range',\n  'reset',\n  'search',\n  'submit',\n  'tel',\n  'text',\n  'time',\n  'url',\n  'week',\n];\n\n/** @returns The input types supported by this browser. */\nexport function getSupportedInputTypes(): Set<string> {\n  // Result is cached.\n  if (supportedInputTypes) {\n    return supportedInputTypes;\n  }\n\n  // We can't check if an input type is not supported until we're on the browser, so say that\n  // everything is supported when not on the browser. We don't use `Platform` here since it's\n  // just a helper function and can't inject it.\n  if (typeof document !== 'object' || !document) {\n    supportedInputTypes = new Set(candidateInputTypes);\n    return supportedInputTypes;\n  }\n\n  let featureTestInput = document.createElement('input');\n  supportedInputTypes = new Set(\n    candidateInputTypes.filter(value => {\n      featureTestInput.setAttribute('type', value);\n      return featureTestInput.type === value;\n    }),\n  );\n\n  return supportedInputTypes;\n}\n"], "names": [], "mappings": ";;;;;;;;;MAWa,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAd,cAAc,EAAA,CAAA;wGAAd,cAAc,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAD1B,QAAQ;mBAAC,EAAE;;;ACFZ;AACA,IAAI,mBAAgC;AAEpC;AACA,MAAM,mBAAmB,GAAG;;;;;IAK1B,OAAO;IACP,QAAQ;IACR,UAAU;IACV,MAAM;IACN,gBAAgB;IAChB,OAAO;IACP,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,QAAQ;IACR,UAAU;IACV,OAAO;IACP,OAAO;IACP,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,KAAK;IACL,MAAM;IACN,MAAM;IACN,KAAK;IACL,MAAM;CACP;AAED;SACgB,sBAAsB,GAAA;;IAEpC,IAAI,mBAAmB,EAAE;AACvB,QAAA,OAAO,mBAAmB;;;;;IAM5B,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,QAAQ,EAAE;AAC7C,QAAA,mBAAmB,GAAG,IAAI,GAAG,CAAC,mBAAmB,CAAC;AAClD,QAAA,OAAO,mBAAmB;;IAG5B,IAAI,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;IACtD,mBAAmB,GAAG,IAAI,GAAG,CAC3B,mBAAmB,CAAC,MAAM,CAAC,KAAK,IAAG;AACjC,QAAA,gBAAgB,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC;AAC5C,QAAA,OAAO,gBAAgB,CAAC,IAAI,KAAK,KAAK;KACvC,CAAC,CACH;AAED,IAAA,OAAO,mBAAmB;AAC5B;;;;"}