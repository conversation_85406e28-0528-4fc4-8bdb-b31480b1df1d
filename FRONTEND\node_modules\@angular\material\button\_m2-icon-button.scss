@use 'sass:map';
@use '../core/theming/theming';
@use '../core/tokens/m2-utils';
@use '../core/tokens/m3-utils';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);
  $density-scale: theming.clamp-density(map.get($system, density-scale), -3);

  @return (
    base: (
      icon-button-icon-size: 24px,
      icon-button-container-shape: 50%,
    ),
    color: (
      icon-button-disabled-icon-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      icon-button-disabled-state-layer-color: map.get($system, on-surface-variant),
      icon-button-focus-state-layer-opacity: map.get($system, focus-state-layer-opacity),
      icon-button-hover-state-layer-opacity: map.get($system, hover-state-layer-opacity),
      icon-button-icon-color: inherit,
      icon-button-pressed-state-layer-opacity: map.get($system, pressed-state-layer-opacity),
      icon-button-ripple-color: m3-utils.color-with-opacity(
          map.get($system, on-surface), map.get($system, pressed-state-layer-opacity)),
      icon-button-state-layer-color: map.get($system, on-surface),
    ),
    typography: (),
    density: (
      icon-button-touch-target-display: if($density-scale < -1, none, block),
      icon-button-state-layer-size: map.get((
        0: 48px,
        -1: 44px,
        -2: 40px,
        -3: 36px,
        -4: 32px,
        -5: 28px,
      ), $density-scale)
    ),
  );
}

// Generates the mapping for the properties that change based on the button palette color.
@function private-get-color-palette-color-tokens($theme, $color-variant) {
  $system: m2-utils.get-system($theme);
  $system: m3-utils.replace-colors-with-variant($system, primary, $color-variant);

  @return (
    icon-button-icon-color: map.get($system, primary),
    icon-button-state-layer-color: map.get($system, primary),
    icon-button-ripple-color: m3-utils.color-with-opacity(
        map.get($system, primary), map.get($system, pressed-state-layer-opacity)),
  );
}
