{"version": 3, "file": "list-key-manager-C7tp3RbG.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/key-manager/list-key-manager.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  DOWN_ARROW,\n  END,\n  HOME,\n  LEFT_ARROW,\n  PAGE_DOWN,\n  PAGE_UP,\n  RIGHT_ARROW,\n  TAB,\n  UP_ARROW,\n  hasModifierKey,\n} from '../../keycodes';\nimport {EffectRef, Injector, QueryList, Signal, effect, isSignal, signal} from '@angular/core';\nimport {Subject, Subscription} from 'rxjs';\nimport {Typeahead} from './typeahead';\n\n/** This interface is for items that can be passed to a ListKeyManager. */\nexport interface ListKeyManagerOption {\n  /** Whether the option is disabled. */\n  disabled?: boolean;\n\n  /** Gets the label for this option. */\n  getLabel?(): string;\n}\n\n/** Modifier keys handled by the ListKeyManager. */\nexport type ListKeyManagerModifierKey = 'altKey' | 'ctrlKey' | 'metaKey' | 'shiftKey';\n\n/**\n * This class manages keyboard events for selectable lists. If you pass it a query list\n * of items, it will set the active item correctly when arrow events occur.\n */\nexport class ListKeyManager<T extends ListKeyManagerOption> {\n  private _activeItemIndex = signal(-1);\n  private _activeItem = signal<T | null>(null);\n  private _wrap = false;\n  private _typeaheadSubscription = Subscription.EMPTY;\n  private _itemChangesSubscription?: Subscription;\n  private _vertical = true;\n  private _horizontal: 'ltr' | 'rtl' | null;\n  private _allowedModifierKeys: ListKeyManagerModifierKey[] = [];\n  private _homeAndEnd = false;\n  private _pageUpAndDown = {enabled: false, delta: 10};\n  private _effectRef: EffectRef | undefined;\n  private _typeahead?: Typeahead<T>;\n\n  /**\n   * Predicate function that can be used to check whether an item should be skipped\n   * by the key manager. By default, disabled items are skipped.\n   */\n  private _skipPredicateFn = (item: T) => item.disabled;\n\n  constructor(items: QueryList<T> | T[] | readonly T[]);\n  constructor(items: Signal<T[]> | Signal<readonly T[]>, injector: Injector);\n  constructor(\n    private _items: QueryList<T> | T[] | readonly T[] | Signal<T[]> | Signal<readonly T[]>,\n    injector?: Injector,\n  ) {\n    // We allow for the items to be an array because, in some cases, the consumer may\n    // not have access to a QueryList of the items they want to manage (e.g. when the\n    // items aren't being collected via `ViewChildren` or `ContentChildren`).\n    if (_items instanceof QueryList) {\n      this._itemChangesSubscription = _items.changes.subscribe((newItems: QueryList<T>) =>\n        this._itemsChanged(newItems.toArray()),\n      );\n    } else if (isSignal(_items)) {\n      if (!injector && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throw new Error('ListKeyManager constructed with a signal must receive an injector');\n      }\n\n      this._effectRef = effect(() => this._itemsChanged(_items()), {injector});\n    }\n  }\n\n  /**\n   * Stream that emits any time the TAB key is pressed, so components can react\n   * when focus is shifted off of the list.\n   */\n  readonly tabOut = new Subject<void>();\n\n  /** Stream that emits whenever the active item of the list manager changes. */\n  readonly change = new Subject<number>();\n\n  /**\n   * Sets the predicate function that determines which items should be skipped by the\n   * list key manager.\n   * @param predicate Function that determines whether the given item should be skipped.\n   */\n  skipPredicate(predicate: (item: T) => boolean): this {\n    this._skipPredicateFn = predicate;\n    return this;\n  }\n\n  /**\n   * Configures wrapping mode, which determines whether the active item will wrap to\n   * the other end of list when there are no more items in the given direction.\n   * @param shouldWrap Whether the list should wrap when reaching the end.\n   */\n  withWrap(shouldWrap = true): this {\n    this._wrap = shouldWrap;\n    return this;\n  }\n\n  /**\n   * Configures whether the key manager should be able to move the selection vertically.\n   * @param enabled Whether vertical selection should be enabled.\n   */\n  withVerticalOrientation(enabled: boolean = true): this {\n    this._vertical = enabled;\n    return this;\n  }\n\n  /**\n   * Configures the key manager to move the selection horizontally.\n   * Passing in `null` will disable horizontal movement.\n   * @param direction Direction in which the selection can be moved.\n   */\n  withHorizontalOrientation(direction: 'ltr' | 'rtl' | null): this {\n    this._horizontal = direction;\n    return this;\n  }\n\n  /**\n   * Modifier keys which are allowed to be held down and whose default actions will be prevented\n   * as the user is pressing the arrow keys. Defaults to not allowing any modifier keys.\n   */\n  withAllowedModifierKeys(keys: ListKeyManagerModifierKey[]): this {\n    this._allowedModifierKeys = keys;\n    return this;\n  }\n\n  /**\n   * Turns on typeahead mode which allows users to set the active item by typing.\n   * @param debounceInterval Time to wait after the last keystroke before setting the active item.\n   */\n  withTypeAhead(debounceInterval: number = 200): this {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const items = this._getItemsArray();\n      if (items.length > 0 && items.some(item => typeof item.getLabel !== 'function')) {\n        throw Error('ListKeyManager items in typeahead mode must implement the `getLabel` method.');\n      }\n    }\n\n    this._typeaheadSubscription.unsubscribe();\n\n    const items = this._getItemsArray();\n    this._typeahead = new Typeahead(items, {\n      debounceInterval: typeof debounceInterval === 'number' ? debounceInterval : undefined,\n      skipPredicate: item => this._skipPredicateFn(item),\n    });\n\n    this._typeaheadSubscription = this._typeahead.selectedItem.subscribe(item => {\n      this.setActiveItem(item);\n    });\n\n    return this;\n  }\n\n  /** Cancels the current typeahead sequence. */\n  cancelTypeahead(): this {\n    this._typeahead?.reset();\n    return this;\n  }\n\n  /**\n   * Configures the key manager to activate the first and last items\n   * respectively when the Home or End key is pressed.\n   * @param enabled Whether pressing the Home or End key activates the first/last item.\n   */\n  withHomeAndEnd(enabled: boolean = true): this {\n    this._homeAndEnd = enabled;\n    return this;\n  }\n\n  /**\n   * Configures the key manager to activate every 10th, configured or first/last element in up/down direction\n   * respectively when the Page-Up or Page-Down key is pressed.\n   * @param enabled Whether pressing the Page-Up or Page-Down key activates the first/last item.\n   * @param delta Whether pressing the Home or End key activates the first/last item.\n   */\n  withPageUpDown(enabled: boolean = true, delta: number = 10): this {\n    this._pageUpAndDown = {enabled, delta};\n    return this;\n  }\n\n  /**\n   * Sets the active item to the item at the index specified.\n   * @param index The index of the item to be set as active.\n   */\n  setActiveItem(index: number): void;\n\n  /**\n   * Sets the active item to the specified item.\n   * @param item The item to be set as active.\n   */\n  setActiveItem(item: T): void;\n\n  /**\n   * Sets the active item to the specified item.\n   * @param item The item to be set as active.\n   */\n  setActiveItem(item: T | number): void;\n\n  setActiveItem(item: any): void {\n    const previousActiveItem = this._activeItem();\n\n    this.updateActiveItem(item);\n\n    if (this._activeItem() !== previousActiveItem) {\n      this.change.next(this._activeItemIndex());\n    }\n  }\n\n  /**\n   * Sets the active item depending on the key event passed in.\n   * @param event Keyboard event to be used for determining which element should be active.\n   */\n  onKeydown(event: KeyboardEvent): void {\n    const keyCode = event.keyCode;\n    const modifiers: ListKeyManagerModifierKey[] = ['altKey', 'ctrlKey', 'metaKey', 'shiftKey'];\n    const isModifierAllowed = modifiers.every(modifier => {\n      return !event[modifier] || this._allowedModifierKeys.indexOf(modifier) > -1;\n    });\n\n    switch (keyCode) {\n      case TAB:\n        this.tabOut.next();\n        return;\n\n      case DOWN_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case UP_ARROW:\n        if (this._vertical && isModifierAllowed) {\n          this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case RIGHT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setPreviousItemActive() : this.setNextItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case LEFT_ARROW:\n        if (this._horizontal && isModifierAllowed) {\n          this._horizontal === 'rtl' ? this.setNextItemActive() : this.setPreviousItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case HOME:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setFirstItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case END:\n        if (this._homeAndEnd && isModifierAllowed) {\n          this.setLastItemActive();\n          break;\n        } else {\n          return;\n        }\n\n      case PAGE_UP:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex() - this._pageUpAndDown.delta;\n          this._setActiveItemByIndex(targetIndex > 0 ? targetIndex : 0, 1);\n          break;\n        } else {\n          return;\n        }\n\n      case PAGE_DOWN:\n        if (this._pageUpAndDown.enabled && isModifierAllowed) {\n          const targetIndex = this._activeItemIndex() + this._pageUpAndDown.delta;\n          const itemsLength = this._getItemsArray().length;\n          this._setActiveItemByIndex(targetIndex < itemsLength ? targetIndex : itemsLength - 1, -1);\n          break;\n        } else {\n          return;\n        }\n\n      default:\n        if (isModifierAllowed || hasModifierKey(event, 'shiftKey')) {\n          this._typeahead?.handleKey(event);\n        }\n\n        // Note that we return here, in order to avoid preventing\n        // the default action of non-navigational keys.\n        return;\n    }\n\n    this._typeahead?.reset();\n    event.preventDefault();\n  }\n\n  /** Index of the currently active item. */\n  get activeItemIndex(): number | null {\n    return this._activeItemIndex();\n  }\n\n  /** The active item. */\n  get activeItem(): T | null {\n    return this._activeItem();\n  }\n\n  /** Gets whether the user is currently typing into the manager using the typeahead feature. */\n  isTyping(): boolean {\n    return !!this._typeahead && this._typeahead.isTyping();\n  }\n\n  /** Sets the active item to the first enabled item in the list. */\n  setFirstItemActive(): void {\n    this._setActiveItemByIndex(0, 1);\n  }\n\n  /** Sets the active item to the last enabled item in the list. */\n  setLastItemActive(): void {\n    this._setActiveItemByIndex(this._getItemsArray().length - 1, -1);\n  }\n\n  /** Sets the active item to the next enabled item in the list. */\n  setNextItemActive(): void {\n    this._activeItemIndex() < 0 ? this.setFirstItemActive() : this._setActiveItemByDelta(1);\n  }\n\n  /** Sets the active item to a previous enabled item in the list. */\n  setPreviousItemActive(): void {\n    this._activeItemIndex() < 0 && this._wrap\n      ? this.setLastItemActive()\n      : this._setActiveItemByDelta(-1);\n  }\n\n  /**\n   * Allows setting the active without any other effects.\n   * @param index Index of the item to be set as active.\n   */\n  updateActiveItem(index: number): void;\n\n  /**\n   * Allows setting the active item without any other effects.\n   * @param item Item to be set as active.\n   */\n  updateActiveItem(item: T): void;\n\n  updateActiveItem(item: any): void {\n    const itemArray = this._getItemsArray();\n    const index = typeof item === 'number' ? item : itemArray.indexOf(item);\n    const activeItem = itemArray[index];\n\n    // Explicitly check for `null` and `undefined` because other falsy values are valid.\n    this._activeItem.set(activeItem == null ? null : activeItem);\n    this._activeItemIndex.set(index);\n    this._typeahead?.setCurrentSelectedItemIndex(index);\n  }\n\n  /** Cleans up the key manager. */\n  destroy() {\n    this._typeaheadSubscription.unsubscribe();\n    this._itemChangesSubscription?.unsubscribe();\n    this._effectRef?.destroy();\n    this._typeahead?.destroy();\n    this.tabOut.complete();\n    this.change.complete();\n  }\n\n  /**\n   * This method sets the active item, given a list of items and the delta between the\n   * currently active item and the new active item. It will calculate differently\n   * depending on whether wrap mode is turned on.\n   */\n  private _setActiveItemByDelta(delta: -1 | 1): void {\n    this._wrap ? this._setActiveInWrapMode(delta) : this._setActiveInDefaultMode(delta);\n  }\n\n  /**\n   * Sets the active item properly given \"wrap\" mode. In other words, it will continue to move\n   * down the list until it finds an item that is not disabled, and it will wrap if it\n   * encounters either end of the list.\n   */\n  private _setActiveInWrapMode(delta: -1 | 1): void {\n    const items = this._getItemsArray();\n\n    for (let i = 1; i <= items.length; i++) {\n      const index = (this._activeItemIndex() + delta * i + items.length) % items.length;\n      const item = items[index];\n\n      if (!this._skipPredicateFn(item)) {\n        this.setActiveItem(index);\n        return;\n      }\n    }\n  }\n\n  /**\n   * Sets the active item properly given the default mode. In other words, it will\n   * continue to move down the list until it finds an item that is not disabled. If\n   * it encounters either end of the list, it will stop and not wrap.\n   */\n  private _setActiveInDefaultMode(delta: -1 | 1): void {\n    this._setActiveItemByIndex(this._activeItemIndex() + delta, delta);\n  }\n\n  /**\n   * Sets the active item to the first enabled item starting at the index specified. If the\n   * item is disabled, it will move in the fallbackDelta direction until it either\n   * finds an enabled item or encounters the end of the list.\n   */\n  private _setActiveItemByIndex(index: number, fallbackDelta: -1 | 1): void {\n    const items = this._getItemsArray();\n\n    if (!items[index]) {\n      return;\n    }\n\n    while (this._skipPredicateFn(items[index])) {\n      index += fallbackDelta;\n\n      if (!items[index]) {\n        return;\n      }\n    }\n\n    this.setActiveItem(index);\n  }\n\n  /** Returns the items as an array. */\n  private _getItemsArray(): T[] | readonly T[] {\n    if (isSignal(this._items)) {\n      return this._items();\n    }\n\n    return this._items instanceof QueryList ? this._items.toArray() : this._items;\n  }\n\n  /** Callback for when the items have changed. */\n  private _itemsChanged(newItems: T[] | readonly T[]) {\n    this._typeahead?.setItems(newItems);\n    const activeItem = this._activeItem();\n    if (activeItem) {\n      const newIndex = newItems.indexOf(activeItem);\n\n      if (newIndex > -1 && newIndex !== this._activeItemIndex()) {\n        this._activeItemIndex.set(newIndex);\n        this._typeahead?.setCurrentSelectedItemIndex(newIndex);\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAoCA;;;AAGG;MACU,cAAc,CAAA;AAuBf,IAAA,MAAA;AAtBF,IAAA,gBAAgB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC7B,IAAA,WAAW,GAAG,MAAM,CAAW,IAAI,CAAC;IACpC,KAAK,GAAG,KAAK;AACb,IAAA,sBAAsB,GAAG,YAAY,CAAC,KAAK;AAC3C,IAAA,wBAAwB;IACxB,SAAS,GAAG,IAAI;AAChB,IAAA,WAAW;IACX,oBAAoB,GAAgC,EAAE;IACtD,WAAW,GAAG,KAAK;IACnB,cAAc,GAAG,EAAC,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,EAAE,EAAC;AAC5C,IAAA,UAAU;AACV,IAAA,UAAU;AAElB;;;AAGG;IACK,gBAAgB,GAAG,CAAC,IAAO,KAAK,IAAI,CAAC,QAAQ;IAIrD,WACU,CAAA,MAA8E,EACtF,QAAmB,EAAA;QADX,IAAM,CAAA,MAAA,GAAN,MAAM;;;;AAMd,QAAA,IAAI,MAAM,YAAY,SAAS,EAAE;YAC/B,IAAI,CAAC,wBAAwB,GAAG,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,QAAsB,KAC9E,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,CACvC;;AACI,aAAA,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;AAC3B,YAAA,IAAI,CAAC,QAAQ,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;AAChE,gBAAA,MAAM,IAAI,KAAK,CAAC,mEAAmE,CAAC;;YAGtF,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAC,QAAQ,EAAC,CAAC;;;AAI5E;;;AAGG;AACM,IAAA,MAAM,GAAG,IAAI,OAAO,EAAQ;;AAG5B,IAAA,MAAM,GAAG,IAAI,OAAO,EAAU;AAEvC;;;;AAIG;AACH,IAAA,aAAa,CAAC,SAA+B,EAAA;AAC3C,QAAA,IAAI,CAAC,gBAAgB,GAAG,SAAS;AACjC,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;IACH,QAAQ,CAAC,UAAU,GAAG,IAAI,EAAA;AACxB,QAAA,IAAI,CAAC,KAAK,GAAG,UAAU;AACvB,QAAA,OAAO,IAAI;;AAGb;;;AAGG;IACH,uBAAuB,CAAC,UAAmB,IAAI,EAAA;AAC7C,QAAA,IAAI,CAAC,SAAS,GAAG,OAAO;AACxB,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;AACH,IAAA,yBAAyB,CAAC,SAA+B,EAAA;AACvD,QAAA,IAAI,CAAC,WAAW,GAAG,SAAS;AAC5B,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,uBAAuB,CAAC,IAAiC,EAAA;AACvD,QAAA,IAAI,CAAC,oBAAoB,GAAG,IAAI;AAChC,QAAA,OAAO,IAAI;;AAGb;;;AAGG;IACH,aAAa,CAAC,mBAA2B,GAAG,EAAA;AAC1C,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE;YACnC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,UAAU,CAAC,EAAE;AAC/E,gBAAA,MAAM,KAAK,CAAC,8EAA8E,CAAC;;;AAI/F,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;AAEzC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE;AACnC,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,SAAS,CAAC,KAAK,EAAE;AACrC,YAAA,gBAAgB,EAAE,OAAO,gBAAgB,KAAK,QAAQ,GAAG,gBAAgB,GAAG,SAAS;YACrF,aAAa,EAAE,IAAI,IAAI,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACnD,SAAA,CAAC;AAEF,QAAA,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,IAAI,IAAG;AAC1E,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;AAC1B,SAAC,CAAC;AAEF,QAAA,OAAO,IAAI;;;IAIb,eAAe,GAAA;AACb,QAAA,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE;AACxB,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;IACH,cAAc,CAAC,UAAmB,IAAI,EAAA;AACpC,QAAA,IAAI,CAAC,WAAW,GAAG,OAAO;AAC1B,QAAA,OAAO,IAAI;;AAGb;;;;;AAKG;AACH,IAAA,cAAc,CAAC,OAAA,GAAmB,IAAI,EAAE,QAAgB,EAAE,EAAA;QACxD,IAAI,CAAC,cAAc,GAAG,EAAC,OAAO,EAAE,KAAK,EAAC;AACtC,QAAA,OAAO,IAAI;;AAqBb,IAAA,aAAa,CAAC,IAAS,EAAA;AACrB,QAAA,MAAM,kBAAkB,GAAG,IAAI,CAAC,WAAW,EAAE;AAE7C,QAAA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AAE3B,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,KAAK,kBAAkB,EAAE;YAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE,CAAC;;;AAI7C;;;AAGG;AACH,IAAA,SAAS,CAAC,KAAoB,EAAA;AAC5B,QAAA,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO;QAC7B,MAAM,SAAS,GAAgC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC;QAC3F,MAAM,iBAAiB,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,IAAG;AACnD,YAAA,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC7E,SAAC,CAAC;QAEF,QAAQ,OAAO;AACb,YAAA,KAAK,GAAG;AACN,gBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;gBAClB;AAEF,YAAA,KAAK,UAAU;AACb,gBAAA,IAAI,IAAI,CAAC,SAAS,IAAI,iBAAiB,EAAE;oBACvC,IAAI,CAAC,iBAAiB,EAAE;oBACxB;;qBACK;oBACL;;AAGJ,YAAA,KAAK,QAAQ;AACX,gBAAA,IAAI,IAAI,CAAC,SAAS,IAAI,iBAAiB,EAAE;oBACvC,IAAI,CAAC,qBAAqB,EAAE;oBAC5B;;qBACK;oBACL;;AAGJ,YAAA,KAAK,WAAW;AACd,gBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,iBAAiB,EAAE;AACzC,oBAAA,IAAI,CAAC,WAAW,KAAK,KAAK,GAAG,IAAI,CAAC,qBAAqB,EAAE,GAAG,IAAI,CAAC,iBAAiB,EAAE;oBACpF;;qBACK;oBACL;;AAGJ,YAAA,KAAK,UAAU;AACb,gBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,iBAAiB,EAAE;AACzC,oBAAA,IAAI,CAAC,WAAW,KAAK,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,GAAG,IAAI,CAAC,qBAAqB,EAAE;oBACpF;;qBACK;oBACL;;AAGJ,YAAA,KAAK,IAAI;AACP,gBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,iBAAiB,EAAE;oBACzC,IAAI,CAAC,kBAAkB,EAAE;oBACzB;;qBACK;oBACL;;AAGJ,YAAA,KAAK,GAAG;AACN,gBAAA,IAAI,IAAI,CAAC,WAAW,IAAI,iBAAiB,EAAE;oBACzC,IAAI,CAAC,iBAAiB,EAAE;oBACxB;;qBACK;oBACL;;AAGJ,YAAA,KAAK,OAAO;gBACV,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,iBAAiB,EAAE;AACpD,oBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;AACvE,oBAAA,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;oBAChE;;qBACK;oBACL;;AAGJ,YAAA,KAAK,SAAS;gBACZ,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,iBAAiB,EAAE;AACpD,oBAAA,MAAM,WAAW,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;oBACvE,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM;oBAChD,IAAI,CAAC,qBAAqB,CAAC,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;oBACzF;;qBACK;oBACL;;AAGJ,YAAA;gBACE,IAAI,iBAAiB,IAAI,cAAc,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE;AAC1D,oBAAA,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,KAAK,CAAC;;;;gBAKnC;;AAGJ,QAAA,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE;QACxB,KAAK,CAAC,cAAc,EAAE;;;AAIxB,IAAA,IAAI,eAAe,GAAA;AACjB,QAAA,OAAO,IAAI,CAAC,gBAAgB,EAAE;;;AAIhC,IAAA,IAAI,UAAU,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE;;;IAI3B,QAAQ,GAAA;AACN,QAAA,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE;;;IAIxD,kBAAkB,GAAA;AAChB,QAAA,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,CAAC;;;IAIlC,iBAAiB,GAAA;AACf,QAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;;;IAIlE,iBAAiB,GAAA;QACf,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC;;;IAIzF,qBAAqB,GAAA;QACnB,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC;AAClC,cAAE,IAAI,CAAC,iBAAiB;cACtB,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;;AAepC,IAAA,gBAAgB,CAAC,IAAS,EAAA;AACxB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,EAAE;AACvC,QAAA,MAAM,KAAK,GAAG,OAAO,IAAI,KAAK,QAAQ,GAAG,IAAI,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;AACvE,QAAA,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC;;AAGnC,QAAA,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,UAAU,IAAI,IAAI,GAAG,IAAI,GAAG,UAAU,CAAC;AAC5D,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,KAAK,CAAC;AAChC,QAAA,IAAI,CAAC,UAAU,EAAE,2BAA2B,CAAC,KAAK,CAAC;;;IAIrD,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE;AACzC,QAAA,IAAI,CAAC,wBAAwB,EAAE,WAAW,EAAE;AAC5C,QAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE;AAC1B,QAAA,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE;AAC1B,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AACtB,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;;AAGxB;;;;AAIG;AACK,IAAA,qBAAqB,CAAC,KAAa,EAAA;QACzC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,CAAC;;AAGrF;;;;AAIG;AACK,IAAA,oBAAoB,CAAC,KAAa,EAAA;AACxC,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE;AAEnC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM;AACjF,YAAA,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;YAEzB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;AAChC,gBAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;gBACzB;;;;AAKN;;;;AAIG;AACK,IAAA,uBAAuB,CAAC,KAAa,EAAA;AAC3C,QAAA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,KAAK,EAAE,KAAK,CAAC;;AAGpE;;;;AAIG;IACK,qBAAqB,CAAC,KAAa,EAAE,aAAqB,EAAA;AAChE,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAE;AAEnC,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;YACjB;;QAGF,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;YAC1C,KAAK,IAAI,aAAa;AAEtB,YAAA,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;gBACjB;;;AAIJ,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;;;IAInB,cAAc,GAAA;AACpB,QAAA,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AACzB,YAAA,OAAO,IAAI,CAAC,MAAM,EAAE;;QAGtB,OAAO,IAAI,CAAC,MAAM,YAAY,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,MAAM;;;AAIvE,IAAA,aAAa,CAAC,QAA4B,EAAA;AAChD,QAAA,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,QAAQ,CAAC;AACnC,QAAA,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE;QACrC,IAAI,UAAU,EAAE;YACd,MAAM,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC;AAE7C,YAAA,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAI,QAAQ,KAAK,IAAI,CAAC,gBAAgB,EAAE,EAAE;AACzD,gBAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC;AACnC,gBAAA,IAAI,CAAC,UAAU,EAAE,2BAA2B,CAAC,QAAQ,CAAC;;;;AAI7D;;;;"}