@use '../core/style/elevation';
@use '../core/tokens/m2-utils';
@use 'sass:map';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);

  @return (
    base: (
      card-elevated-container-shape: 4px,
      card-outlined-container-shape: 4px,
      card-filled-container-shape: 4px,
      card-outlined-outline-width: 1px,
    ),
    color: (
      card-elevated-container-color: map.get($system, surface),
      card-elevated-container-elevation: elevation.get-box-shadow(1),
      card-outlined-container-color: map.get($system, surface),
      card-outlined-container-elevation: elevation.get-box-shadow(0),
      card-outlined-outline-color: map.get($system, outline),
      card-subtitle-text-color: map.get($system, on-surface-variant),
      card-filled-container-color: map.get($system, surface),
      card-filled-container-elevation: elevation.get-box-shadow(0)
    ),
    typography: (
      card-title-text-font: map.get($system, title-small-font),
      card-title-text-line-height: map.get($system, title-small-line-height),
      card-title-text-size: map.get($system, title-small-size),
      card-title-text-tracking: map.get($system, title-small-tracking),
      card-title-text-weight: map.get($system, title-small-weight),
      card-subtitle-text-font: map.get($system, label-medium-font),
      card-subtitle-text-line-height: map.get($system, label-medium-line-height),
      card-subtitle-text-size: map.get($system, label-medium-size),
      card-subtitle-text-tracking: map.get($system, label-medium-tracking),
      card-subtitle-text-weight: map.get($system, label-medium-weight),
    ),
    density: (),
  );
}
