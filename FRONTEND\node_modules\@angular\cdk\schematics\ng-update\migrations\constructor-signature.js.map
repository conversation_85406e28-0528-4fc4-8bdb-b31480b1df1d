{"version": 3, "file": "constructor-signature.js", "sourceRoot": "", "sources": ["constructor-signature.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAEH,iCAAiC;AACjC,2DAAsD;AACtD,uEAAgE;AAGhE;;;;;GAKG;AACH,MAAM,yBAAyB,GAAG;IAChC,wCAAwC;IACxC,IAAI;IACJ,kDAAkD;IAClD,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;CACvB,CAAC;AAEF;;;GAGG;AACH,MAAa,6BAA8B,SAAQ,qBAAsB;IAAzE;;QACE,4FAA4F;QAC5F,mFAAmF;QACnF,+EAA+E;QAC/E,SAAI,GAAG,IAAA,+BAAa,EAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,CAAC;QAEzD,2DAA2D;QAC3D,YAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;IA0EnC,CAAC;IAxEU,SAAS,CAAC,IAAa;QAC9B,IAAI,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;;;;;;;OAQG;IACK,gBAAgB,CAAC,UAAyB;QAChD,kEAAkE;QAClE,MAAM,WAAW,GAAG,EAAE;aACnB,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,CAAC;aAC/C,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,yBAAyB,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;aACzE,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,KAAK,SAAS,CAAC,CAAC;QAExD,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;YACrC,MAAM,IAAI,GAAG,mBAAmB,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;YAEzD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,SAAS;YACX,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtE,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;YAC5D,MAAM,eAAe,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAEjD,6FAA6F;YAC7F,2FAA2F;YAC3F,0FAA0F;YAC1F,uFAAuF;YACvF,MAAM,mBAAmB,GAAG,SAAS;iBAClC,sBAAsB,EAAE;iBACxB,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,8BAA8B,CAAC,SAAS,CAAC,CAAC;iBAC3D,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;iBACpF,MAAM,CAAC,OAAO,CAAC,CAAC;YAEnB,6FAA6F;YAC7F,wDAAwD;YACxD,IACE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;gBAC9B,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAK,CAAC,CAAC,EAC5D,CAAC;gBACD,SAAS;YACX,CAAC;YAED,MAAM,eAAe,GAAG,SAAS;iBAC9B,sBAAsB,EAAE;iBACxB,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,8BAA8B,CAAC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;YAEjF,MAAM,cAAc,GAAG,eAAe,CAAC,CAAC,CAAC,OAAO,SAAS,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;YACtE,MAAM,UAAU,GAAG,eAAe;iBAC/B,GAAG,CAAC,SAAS,CAAC,EAAE,CACf,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAC5E;iBACA,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,cAAc,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;iBAC9D,IAAI,CAAC,MAAM,CAAC,CAAC;YAEhB,IAAI,CAAC,mBAAmB,CACtB,IAAI,EACJ,UAAU,SAAS,qBAAqB;gBACtC,oDAAoD,cAAc,iBAAiB;gBACnF,0BAA0B,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,UAAU,EAAE,CACnF,CAAC;QACJ,CAAC;IACH,CAAC;CACF;AAjFD,sEAiFC;AAED,uEAAuE;AACvE,SAAS,8BAA8B,CACrC,SAAuB,EACvB,WAA2B;IAE3B,OAAO,SAAS;SACb,aAAa,EAAE;SACf,GAAG,CAAC,KAAK,CAAC,EAAE,CACX,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC,iBAAiB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CACjF,CAAC;AACN,CAAC;AAED;;;GAGG;AACH,SAAS,mBAAmB,CAC1B,UAAyB,EACzB,UAAyB;IAEzB,IAAI,YAAY,GAAmB,IAAI,CAAC;IAExC,MAAM,UAAU,GAAG,CAAC,IAAa,EAAE,EAAE;QACnC,+FAA+F;QAC/F,iEAAiE;QACjE,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,UAAU,CAAC,KAAM,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,UAAU,CAAC,KAAM,EAAE,CAAC;YAC/E,IACE,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;gBACxB,CAAC,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,EAClF,CAAC;gBACD,YAAY,GAAG,IAAI,CAAC;YACtB,CAAC;YAED,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;QACpC,CAAC;IACH,CAAC,CAAC;IAEF,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IAExC,OAAO,YAAY,CAAC;AACtB,CAAC;AAED,6EAA6E;AAC7E,SAAS,8BAA8B,CAAC,SAAuB;IAC7D,IAAI,IAAI,GAAY,SAAS,CAAC,cAAc,EAAE,CAAC;IAC/C,oFAAoF;IACpF,mDAAmD;IACnD,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAC9C,IAAI,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC"}