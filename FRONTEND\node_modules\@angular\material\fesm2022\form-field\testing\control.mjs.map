{"version": 3, "file": "control.mjs", "sources": ["../../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/form-field/testing/control/form-field-control-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness} from '@angular/cdk/testing';\n\n/**\n * Base class for custom form-field control harnesses. Harnesses for\n * custom controls with form-fields need to implement this interface.\n */\nexport abstract class MatFormFieldControlHarness extends ComponentHarness {}\n\n/**\n * Shared behavior for `MatFormFieldControlHarness` implementations\n */\nexport abstract class MatFormFieldControlHarnessBase extends MatFormFieldControlHarness {\n  private readonly _floatingLabelSelector = '.mdc-floating-label';\n\n  /** Gets the text content of the floating label, if it exists. */\n  async getLabel(): Promise<string | null> {\n    const documentRootLocator = this.documentRootLocatorFactory();\n    const labelId = await (await this.host()).getAttribute('aria-labelledby');\n    const hostId = await (await this.host()).getAttribute('id');\n\n    if (labelId) {\n      // First option, try to fetch the label using the `aria-labelledby`\n      // attribute.\n      const labelEl = await documentRootLocator.locatorForOptional(\n        `${this._floatingLabelSelector}[id=\"${labelId}\"]`,\n      )();\n      return labelEl ? labelEl.text() : null;\n    } else if (hostId) {\n      // Fallback option, try to match the id of the input with the `for`\n      // attribute of the label.\n      const labelEl = await documentRootLocator.locatorForOptional(\n        `${this._floatingLabelSelector}[for=\"${hostId}\"]`,\n      )();\n      return labelEl ? labelEl.text() : null;\n    }\n    return null;\n  }\n}\n"], "names": [], "mappings": ";;AAUA;;;AAGG;AACG,MAAgB,0BAA2B,SAAQ,gBAAgB,CAAA;AAAG;AAE5E;;AAEG;AACG,MAAgB,8BAA+B,SAAQ,0BAA0B,CAAA;IACpE,sBAAsB,GAAG,qBAAqB;;AAG/D,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,EAAE;AAC7D,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC;AACzE,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC;QAE3D,IAAI,OAAO,EAAE;;;AAGX,YAAA,MAAM,OAAO,GAAG,MAAM,mBAAmB,CAAC,kBAAkB,CAC1D,CAAA,EAAG,IAAI,CAAC,sBAAsB,CAAQ,KAAA,EAAA,OAAO,CAAI,EAAA,CAAA,CAClD,EAAE;AACH,YAAA,OAAO,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI;;aACjC,IAAI,MAAM,EAAE;;;AAGjB,YAAA,MAAM,OAAO,GAAG,MAAM,mBAAmB,CAAC,kBAAkB,CAC1D,CAAA,EAAG,IAAI,CAAC,sBAAsB,CAAS,MAAA,EAAA,MAAM,CAAI,EAAA,CAAA,CAClD,EAAE;AACH,YAAA,OAAO,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI;;AAExC,QAAA,OAAO,IAAI;;AAEd;;;;"}