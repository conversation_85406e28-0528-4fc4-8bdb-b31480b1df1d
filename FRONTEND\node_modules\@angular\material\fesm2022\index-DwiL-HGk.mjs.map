{"version": 3, "file": "index-DwiL-HGk.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/option/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatRippleModule} from '../ripple/index';\nimport {MatPseudoCheckboxModule} from '../selection/index';\nimport {MatCommonModule} from '../common-behaviors/common-module';\nimport {MatOption} from './option';\nimport {MatOptgroup} from './optgroup';\n\n@NgModule({\n  imports: [MatRippleModule, MatCommonModule, MatPseudoCheckboxModule, MatOption, MatOptgroup],\n  exports: [MatOption, MatOptgroup],\n})\nexport class MatOptionModule {}\n\nexport * from './option';\nexport * from './optgroup';\nexport * from './option-parent';\n"], "names": [], "mappings": ";;;;;;;MAmBa,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAHhB,OAAA,EAAA,CAAA,eAAe,EAAE,eAAe,EAAE,uBAAuB,EAAE,SAAS,EAAE,WAAW,CACjF,EAAA,OAAA,EAAA,CAAA,SAAS,EAAE,WAAW,CAAA,EAAA,CAAA;AAErB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAHhB,eAAe,EAAE,eAAe,EAAE,uBAAuB,EAAE,SAAS,CAAA,EAAA,CAAA;;2FAGnE,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,uBAAuB,EAAE,SAAS,EAAE,WAAW,CAAC;AAC5F,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC;AAClC,iBAAA;;;;;"}