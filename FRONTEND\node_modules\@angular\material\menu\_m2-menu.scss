@use '../core/style/elevation';
@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/tokens/m3-utils';

@function get-tokens($theme) {
  $system: m2-utils.get-system($theme);

  @return (
    base: (
      menu-container-shape: 4px,
      menu-divider-bottom-spacing: 0,
      menu-divider-top-spacing: 0,
      menu-item-spacing: 16px,
      menu-item-icon-size: 24px,
      menu-item-leading-spacing: 16px,
      menu-item-trailing-spacing: 16px,
      menu-item-with-icon-leading-spacing: 16px,
      menu-item-with-icon-trailing-spacing: 16px,
      menu-container-elevation-shadow: elevation.get-box-shadow(8),

      // Unused
      menu-base-elevation-level: null,
    ),
    color: (
      menu-item-label-text-color: map.get($system, on-surface),
      menu-item-icon-color: map.get($system, on-surface),
      menu-item-hover-state-layer-color: m3-utils.color-with-opacity(
          map.get($system, on-surface), map.get($system, hover-state-layer-opacity)),
      menu-item-focus-state-layer-color: m3-utils.color-with-opacity(
          map.get($system, on-surface), map.get($system, focus-state-layer-opacity)),
      menu-container-color: map.get($system, surface),
      menu-divider-color: map.get($system, outline),
    ),
    typography: (
      menu-item-label-text-font: map.get($system, body-large-font),
      menu-item-label-text-size: map.get($system, body-large-size),
      menu-item-label-text-tracking: map.get($system, body-large-tracking),
      menu-item-label-text-line-height: map.get($system, body-large-line-height),
      menu-item-label-text-weight: map.get($system, body-large-weight),
    ),
    density: (),
  );
}
