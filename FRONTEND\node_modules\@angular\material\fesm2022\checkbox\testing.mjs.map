{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/checkbox/testing/checkbox-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {CheckboxHarnessFilters} from './checkbox-harness-filters';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\n\n/** <PERSON><PERSON><PERSON> for interacting with a mat-checkbox in tests. */\nexport class MatCheckboxHarness extends ComponentHarness {\n  static hostSelector = '.mat-mdc-checkbox';\n\n  _input = this.locatorFor('input');\n  private _label = this.locatorFor('label');\n  private _inputContainer = this.locatorFor('.mdc-checkbox');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a checkbox with specific attributes.\n   * @param options Options for narrowing the search:\n   *   - `selector` finds a checkbox whose host element matches the given selector.\n   *   - `label` finds a checkbox with specific label text.\n   *   - `name` finds a checkbox with specific name.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatCheckboxHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: CheckboxHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return (\n      new HarnessPredicate(this, options)\n        .addOption('label', options.label, (harness, label) =>\n          HarnessPredicate.stringMatches(harness.getLabelText(), label),\n        )\n        // We want to provide a filter option for \"name\" because the name of the checkbox is\n        // only set on the underlying input. This means that it's not possible for developers\n        // to retrieve the harness of a specific checkbox with name through a CSS selector.\n        .addOption(\n          'name',\n          options.name,\n          async (harness, name) => (await harness.getName()) === name,\n        )\n        .addOption(\n          'checked',\n          options.checked,\n          async (harness, checked) => (await harness.isChecked()) == checked,\n        )\n        .addOption('disabled', options.disabled, async (harness, disabled) => {\n          return (await harness.isDisabled()) === disabled;\n        })\n    );\n  }\n\n  /** Whether the checkbox is checked. */\n  async isChecked(): Promise<boolean> {\n    const checked = (await this._input()).getProperty<boolean>('checked');\n    return coerceBooleanProperty(await checked);\n  }\n\n  /** Whether the checkbox is in an indeterminate state. */\n  async isIndeterminate(): Promise<boolean> {\n    const indeterminate = (await this._input()).getProperty<string>('indeterminate');\n    return coerceBooleanProperty(await indeterminate);\n  }\n\n  /** Whether the checkbox is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const input = await this._input();\n    const disabled = await input.getAttribute('disabled');\n\n    if (disabled !== null) {\n      return coerceBooleanProperty(disabled);\n    }\n\n    return (await input.getAttribute('aria-disabled')) === 'true';\n  }\n\n  /** Whether the checkbox is required. */\n  async isRequired(): Promise<boolean> {\n    const required = (await this._input()).getProperty<boolean>('required');\n    return coerceBooleanProperty(await required);\n  }\n\n  /** Whether the checkbox is valid. */\n  async isValid(): Promise<boolean> {\n    const invalid = (await this.host()).hasClass('ng-invalid');\n    return !(await invalid);\n  }\n\n  /** Gets the checkbox's name. */\n  async getName(): Promise<string | null> {\n    return (await this._input()).getAttribute('name');\n  }\n\n  /** Gets the checkbox's value. */\n  async getValue(): Promise<string | null> {\n    return (await this._input()).getProperty<string | null>('value');\n  }\n\n  /** Gets the checkbox's aria-label. */\n  async getAriaLabel(): Promise<string | null> {\n    return (await this._input()).getAttribute('aria-label');\n  }\n\n  /** Gets the checkbox's aria-labelledby. */\n  async getAriaLabelledby(): Promise<string | null> {\n    return (await this._input()).getAttribute('aria-labelledby');\n  }\n\n  /** Gets the checkbox's label text. */\n  async getLabelText(): Promise<string> {\n    return (await this._label()).text();\n  }\n\n  /** Focuses the checkbox. */\n  async focus(): Promise<void> {\n    return (await this._input()).focus();\n  }\n\n  /** Blurs the checkbox. */\n  async blur(): Promise<void> {\n    return (await this._input()).blur();\n  }\n\n  /** Whether the checkbox is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this._input()).isFocused();\n  }\n\n  /**\n   * Toggles the checked state of the checkbox.\n   *\n   * Note: This attempts to toggle the checkbox as a user would, by clicking it. Therefore if you\n   * are using `MAT_CHECKBOX_DEFAULT_OPTIONS` to change the behavior on click, calling this method\n   * might not have the expected result.\n   */\n  async toggle(): Promise<void> {\n    const elToClick = await ((await this.isDisabled()) ? this._inputContainer() : this._input());\n    return elToClick.click();\n  }\n\n  /**\n   * Puts the checkbox in a checked state by toggling it if it is currently unchecked, or doing\n   * nothing if it is already checked.\n   *\n   * Note: This attempts to check the checkbox as a user would, by clicking it. Therefore if you\n   * are using `MAT_CHECKBOX_DEFAULT_OPTIONS` to change the behavior on click, calling this method\n   * might not have the expected result.\n   */\n  async check(): Promise<void> {\n    if (!(await this.isChecked())) {\n      await this.toggle();\n    }\n  }\n\n  /**\n   * Puts the checkbox in an unchecked state by toggling it if it is currently checked, or doing\n   * nothing if it is already unchecked.\n   *\n   * Note: This attempts to uncheck the checkbox as a user would, by clicking it. Therefore if you\n   * are using `MAT_CHECKBOX_DEFAULT_OPTIONS` to change the behavior on click, calling this method\n   * might not have the expected result.\n   */\n  async uncheck(): Promise<void> {\n    if (await this.isChecked()) {\n      await this.toggle();\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAgBA;AACM,MAAO,kBAAmB,SAAQ,gBAAgB,CAAA;AACtD,IAAA,OAAO,YAAY,GAAG,mBAAmB;AAEzC,IAAA,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AACzB,IAAA,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;AACjC,IAAA,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;AAE1D;;;;;;;AAOG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAkC,EAAE,EAAA;AAEpC,QAAA,QACE,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;aAC/B,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAChD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,KAAK,CAAC;;;;aAK9D,SAAS,CACR,MAAM,EACN,OAAO,CAAC,IAAI,EACZ,OAAO,OAAO,EAAE,IAAI,KAAK,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,MAAM,IAAI;aAE5D,SAAS,CACR,SAAS,EACT,OAAO,CAAC,OAAO,EACf,OAAO,OAAO,EAAE,OAAO,KAAK,CAAC,MAAM,OAAO,CAAC,SAAS,EAAE,KAAK,OAAO;AAEnE,aAAA,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAI;YACnE,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;SACjD,CAAC;;;AAKR,IAAA,MAAM,SAAS,GAAA;AACb,QAAA,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,CAAU,SAAS,CAAC;AACrE,QAAA,OAAO,qBAAqB,CAAC,MAAM,OAAO,CAAC;;;AAI7C,IAAA,MAAM,eAAe,GAAA;AACnB,QAAA,MAAM,aAAa,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,CAAS,eAAe,CAAC;AAChF,QAAA,OAAO,qBAAqB,CAAC,MAAM,aAAa,CAAC;;;AAInD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,EAAE;QACjC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC;AAErD,QAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;AACrB,YAAA,OAAO,qBAAqB,CAAC,QAAQ,CAAC;;QAGxC,OAAO,CAAC,MAAM,KAAK,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM;;;AAI/D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;AACvE,QAAA,OAAO,qBAAqB,CAAC,MAAM,QAAQ,CAAC;;;AAI9C,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,MAAM,OAAO,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,YAAY,CAAC;AAC1D,QAAA,OAAO,EAAE,MAAM,OAAO,CAAC;;;AAIzB,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,MAAM,CAAC;;;AAInD,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,WAAW,CAAgB,OAAO,CAAC;;;AAIlE,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,YAAY,CAAC;;;AAIzD,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC;;;AAI9D,IAAA,MAAM,YAAY,GAAA;QAChB,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE;;;AAIrC,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE;;;AAItC,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE;;;AAIrC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,EAAE,SAAS,EAAE;;AAG1C;;;;;;AAMG;AACH,IAAA,MAAM,MAAM,GAAA;QACV,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC5F,QAAA,OAAO,SAAS,CAAC,KAAK,EAAE;;AAG1B;;;;;;;AAOG;AACH,IAAA,MAAM,KAAK,GAAA;QACT,IAAI,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;AAC7B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE;;;AAIvB;;;;;;;AAOG;AACH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,IAAI,MAAM,IAAI,CAAC,SAAS,EAAE,EAAE;AAC1B,YAAA,MAAM,IAAI,CAAC,MAAM,EAAE;;;;;;;"}