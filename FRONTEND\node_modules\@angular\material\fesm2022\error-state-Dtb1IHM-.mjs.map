{"version": 3, "file": "error-state-Dtb1IHM-.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/common-behaviors/error-state.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {AbstractControl, FormGroupDirective, NgControl, NgForm} from '@angular/forms';\nimport {Subject} from 'rxjs';\nimport {ErrorStateMatcher as _ErrorStateMatcher} from '../error/error-options';\n\n// Declare ErrorStateMatcher as an interface to have compatibility with Closure Compiler.\ninterface ErrorStateMatcher extends _ErrorStateMatcher {}\n\n/**\n * Class that tracks the error state of a component.\n * @docs-private\n */\nexport class _ErrorStateTracker {\n  /** Whether the tracker is currently in an error state. */\n  errorState = false;\n\n  /** User-defined matcher for the error state. */\n  matcher: ErrorStateMatcher;\n\n  constructor(\n    private _defaultMatcher: ErrorStateMatcher | null,\n    public ngControl: NgControl | null,\n    private _parentFormGroup: FormGroupDirective | null,\n    private _parentForm: NgForm | null,\n    private _stateChanges: Subject<void>,\n  ) {}\n\n  /** Updates the error state based on the provided error state matcher. */\n  updateErrorState() {\n    const oldState = this.errorState;\n    const parent = this._parentFormGroup || this._parentForm;\n    const matcher = this.matcher || this._defaultMatcher;\n    const control = this.ngControl ? (this.ngControl.control as AbstractControl) : null;\n    const newState = matcher?.isErrorState(control, parent) ?? false;\n\n    if (newState !== oldState) {\n      this.errorState = newState;\n      this._stateChanges.next();\n    }\n  }\n}\n"], "names": [], "mappings": "AAeA;;;AAGG;MACU,kBAAkB,CAAA;AAQnB,IAAA,eAAA;AACD,IAAA,SAAA;AACC,IAAA,gBAAA;AACA,IAAA,WAAA;AACA,IAAA,aAAA;;IAVV,UAAU,GAAG,KAAK;;AAGlB,IAAA,OAAO;IAEP,WACU,CAAA,eAAyC,EAC1C,SAA2B,EAC1B,gBAA2C,EAC3C,WAA0B,EAC1B,aAA4B,EAAA;QAJ5B,IAAe,CAAA,eAAA,GAAf,eAAe;QAChB,IAAS,CAAA,SAAA,GAAT,SAAS;QACR,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;QAChB,IAAW,CAAA,WAAA,GAAX,WAAW;QACX,IAAa,CAAA,aAAA,GAAb,aAAa;;;IAIvB,gBAAgB,GAAA;AACd,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU;QAChC,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,WAAW;QACxD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,eAAe;AACpD,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,GAAI,IAAI,CAAC,SAAS,CAAC,OAA2B,GAAG,IAAI;AACnF,QAAA,MAAM,QAAQ,GAAG,OAAO,EAAE,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,KAAK;AAEhE,QAAA,IAAI,QAAQ,KAAK,QAAQ,EAAE;AACzB,YAAA,IAAI,CAAC,UAAU,GAAG,QAAQ;AAC1B,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;;;AAG9B;;;;"}