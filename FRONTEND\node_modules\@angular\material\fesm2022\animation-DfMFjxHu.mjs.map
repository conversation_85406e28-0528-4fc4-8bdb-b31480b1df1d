{"version": 3, "file": "animation-DfMFjxHu.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/animation/animation.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {MediaMatcher} from '@angular/cdk/layout';\nimport {ANIMATION_MODULE_TYPE, inject, InjectionToken} from '@angular/core';\n\n/** Object used to configure the animation in Angular Material. */\nexport interface AnimationsConfig {\n  /** Whether all animations should be disabled. */\n  animationsDisabled?: boolean;\n}\n\n/** Injection token used to configure the animations in Angular Material. */\nexport const MATERIAL_ANIMATIONS = new InjectionToken<AnimationsConfig>('MATERIAL_ANIMATIONS');\n\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nexport class AnimationCurves {\n  static STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\n  static DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\n  static ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\n  static SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n}\n\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nexport class AnimationDurations {\n  static COMPLEX = '375ms';\n  static ENTERING = '225ms';\n  static EXITING = '195ms';\n}\n\n/**\n * Returns whether animations have been disabled by DI. Must be called in a DI context.\n * @docs-private\n */\nexport function _animationsDisabled(): boolean {\n  if (\n    inject(MATERIAL_ANIMATIONS, {optional: true})?.animationsDisabled ||\n    inject(ANIMATION_MODULE_TYPE, {optional: true}) === 'NoopAnimations'\n  ) {\n    return true;\n  }\n\n  const mediaMatcher = inject(MediaMatcher);\n  return mediaMatcher.matchMedia('(prefers-reduced-motion)').matches;\n}\n"], "names": [], "mappings": ";;;AAiBA;MACa,mBAAmB,GAAG,IAAI,cAAc,CAAmB,qBAAqB;AAE7F;;;;AAIG;MACU,eAAe,CAAA;AAC1B,IAAA,OAAO,cAAc,GAAG,6BAA6B;AACrD,IAAA,OAAO,kBAAkB,GAAG,6BAA6B;AACzD,IAAA,OAAO,kBAAkB,GAAG,2BAA2B;AACvD,IAAA,OAAO,WAAW,GAAG,6BAA6B;;AAGpD;;;;AAIG;MACU,kBAAkB,CAAA;AAC7B,IAAA,OAAO,OAAO,GAAG,OAAO;AACxB,IAAA,OAAO,QAAQ,GAAG,OAAO;AACzB,IAAA,OAAO,OAAO,GAAG,OAAO;;AAG1B;;;AAGG;SACa,mBAAmB,GAAA;IACjC,IACE,MAAM,CAAC,mBAAmB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,EAAE,kBAAkB;AACjE,QAAA,MAAM,CAAC,qBAAqB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,KAAK,gBAAgB,EACpE;AACA,QAAA,OAAO,IAAI;;AAGb,IAAA,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;IACzC,OAAO,YAAY,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC,OAAO;AACpE;;;;"}