{"version": 3, "file": "icon-registry-CwOTJ7YM.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/icon/trusted-types.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/icon/icon-registry.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @fileoverview\n * A module to facilitate use of a Trusted Types policy internally within\n * Angular Material. It lazily constructs the Trusted Types policy, providing\n * helper utilities for promoting strings to Trusted Types. When Trusted Types\n * are not available, strings are used as a fallback.\n * @security All use of this module is security-sensitive and should go through\n * security review.\n */\n\nexport declare interface TrustedHTML {\n  __brand__: 'TrustedHTML';\n}\n\nexport declare interface TrustedTypePolicyFactory {\n  createPolicy(\n    policyName: string,\n    policyOptions: {\n      createHTML?: (input: string) => string;\n    },\n  ): TrustedTypePolicy;\n}\n\nexport declare interface TrustedTypePolicy {\n  createHTML(input: string): TrustedHTML;\n}\n\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\nlet policy: TrustedTypePolicy | null | undefined;\n\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\nfunction getPolicy(): TrustedTypePolicy | null {\n  if (policy === undefined) {\n    policy = null;\n    if (typeof window !== 'undefined') {\n      const ttWindow = window as unknown as {trustedTypes?: TrustedTypePolicyFactory};\n      if (ttWindow.trustedTypes !== undefined) {\n        policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n          createHTML: (s: string) => s,\n        });\n      }\n    }\n  }\n  return policy;\n}\n\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\nexport function trustedHTMLFromString(html: string): TrustedHTML {\n  return getPolicy()?.createHTML(html) || (html as unknown as TrustedHTML);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HttpClient, HttpErrorResponse} from '@angular/common/http';\nimport {\n  <PERSON>rror<PERSON><PERSON><PERSON>,\n  Inject,\n  Injectable,\n  InjectionToken,\n  OnDestroy,\n  Optional,\n  SecurityContext,\n  SkipSelf,\n  DOCUMENT,\n} from '@angular/core';\nimport {<PERSON>Sanitizer, SafeHtml, SafeResourceUrl} from '@angular/platform-browser';\nimport {forkJoin, Observable, of as observableOf, throwError as observableThrow} from 'rxjs';\nimport {catchError, finalize, map, share, tap} from 'rxjs/operators';\nimport {TrustedHTML, trustedHTMLFromString} from './trusted-types';\n\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nexport function getMatIconNameNotFoundError(iconName: string): Error {\n  return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nexport function getMatIconNoHttpProviderError(): Error {\n  return Error(\n    'Could not find HttpClient for use with Angular Material icons. ' +\n      'Please add provideHttpClient() to your providers.',\n  );\n}\n\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nexport function getMatIconFailedToSanitizeUrlError(url: SafeResourceUrl): Error {\n  return Error(\n    `The URL provided to MatIconRegistry was not trusted as a resource URL ` +\n      `via Angular's DomSanitizer. Attempted URL was \"${url}\".`,\n  );\n}\n\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nexport function getMatIconFailedToSanitizeLiteralError(literal: SafeHtml): Error {\n  return Error(\n    `The literal provided to MatIconRegistry was not trusted as safe HTML by ` +\n      `Angular's DomSanitizer. Attempted literal was \"${literal}\".`,\n  );\n}\n\n/** Options that can be used to configure how an icon or the icons in an icon set are presented. */\nexport interface IconOptions {\n  /** View box to set on the icon. */\n  viewBox?: string;\n\n  /** Whether or not to fetch the icon or icon set using HTTP credentials. */\n  withCredentials?: boolean;\n}\n\n/**\n * Function that will be invoked by the icon registry when trying to resolve the\n * URL from which to fetch an icon. The returned URL will be used to make a request for the icon.\n */\nexport type IconResolver = (\n  name: string,\n  namespace: string,\n) => SafeResourceUrl | SafeResourceUrlWithIconOptions | null;\n\n/** Object that specifies a URL from which to fetch an icon and the options to use for it. */\nexport interface SafeResourceUrlWithIconOptions {\n  url: SafeResourceUrl;\n  options: IconOptions;\n}\n\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n  svgElement: SVGElement | null;\n\n  constructor(\n    public url: SafeResourceUrl,\n    public svgText: TrustedHTML | null,\n    public options?: IconOptions,\n  ) {}\n}\n\n/** Icon configuration whose content has already been loaded. */\ntype LoadedSvgIconConfig = SvgIconConfig & {svgText: TrustedHTML};\n\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\n@Injectable({providedIn: 'root'})\nexport class MatIconRegistry implements OnDestroy {\n  private _document: Document;\n\n  /**\n   * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n   */\n  private _svgIconConfigs = new Map<string, SvgIconConfig>();\n\n  /**\n   * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n   * Multiple icon sets can be registered under the same namespace.\n   */\n  private _iconSetConfigs = new Map<string, SvgIconConfig[]>();\n\n  /** Cache for icons loaded by direct URLs. */\n  private _cachedIconsByUrl = new Map<string, SVGElement>();\n\n  /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n  private _inProgressUrlFetches = new Map<string, Observable<TrustedHTML>>();\n\n  /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n  private _fontCssClassesByAlias = new Map<string, string>();\n\n  /** Registered icon resolver functions. */\n  private _resolvers: IconResolver[] = [];\n\n  /**\n   * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n   * specified. The default 'material-icons' value assumes that the material icon font has been\n   * loaded as described at https://google.github.io/material-design-icons/#icon-font-for-the-web\n   */\n  private _defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n\n  constructor(\n    @Optional() private _httpClient: HttpClient,\n    private _sanitizer: DomSanitizer,\n    @Optional() @Inject(DOCUMENT) document: any,\n    private readonly _errorHandler: ErrorHandler,\n  ) {\n    this._document = document;\n  }\n\n  /**\n   * Registers an icon by URL in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIcon(iconName: string, url: SafeResourceUrl, options?: IconOptions): this {\n    return this.addSvgIconInNamespace('', iconName, url, options);\n  }\n\n  /**\n   * Registers an icon using an HTML string in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteral(iconName: string, literal: SafeHtml, options?: IconOptions): this {\n    return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n  }\n\n  /**\n   * Registers an icon by URL in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIconInNamespace(\n    namespace: string,\n    iconName: string,\n    url: SafeResourceUrl,\n    options?: IconOptions,\n  ): this {\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n  }\n\n  /**\n   * Registers an icon resolver function with the registry. The function will be invoked with the\n   * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n   * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n   * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n   * will be invoked in the order in which they have been registered.\n   * @param resolver Resolver function to be registered.\n   */\n  addSvgIconResolver(resolver: IconResolver): this {\n    this._resolvers.push(resolver);\n    return this;\n  }\n\n  /**\n   * Registers an icon using an HTML string in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteralInNamespace(\n    namespace: string,\n    iconName: string,\n    literal: SafeHtml,\n    options?: IconOptions,\n  ): this {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n\n    // TODO: add an ngDevMode check\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconConfig(\n      namespace,\n      iconName,\n      new SvgIconConfig('', trustedLiteral, options),\n    );\n  }\n\n  /**\n   * Registers an icon set by URL in the default namespace.\n   * @param url\n   */\n  addSvgIconSet(url: SafeResourceUrl, options?: IconOptions): this {\n    return this.addSvgIconSetInNamespace('', url, options);\n  }\n\n  /**\n   * Registers an icon set using an HTML string in the default namespace.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteral(literal: SafeHtml, options?: IconOptions): this {\n    return this.addSvgIconSetLiteralInNamespace('', literal, options);\n  }\n\n  /**\n   * Registers an icon set by URL in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param url\n   */\n  addSvgIconSetInNamespace(namespace: string, url: SafeResourceUrl, options?: IconOptions): this {\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n  }\n\n  /**\n   * Registers an icon set using an HTML string in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteralInNamespace(\n    namespace: string,\n    literal: SafeHtml,\n    options?: IconOptions,\n  ): this {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n  }\n\n  /**\n   * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n   * component with the alias as the fontSet input will cause the class name to be applied\n   * to the `<mat-icon>` element.\n   *\n   * If the registered font is a ligature font, then don't forget to also include the special\n   * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n   *\n   * ```ts\n   * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n   * ```\n   *\n   * And use like this:\n   *\n   * ```html\n   * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n   * ```\n   *\n   * @param alias Alias for the font.\n   * @param classNames Class names override to be used instead of the alias.\n   */\n  registerFontClassAlias(alias: string, classNames: string = alias): this {\n    this._fontCssClassesByAlias.set(alias, classNames);\n    return this;\n  }\n\n  /**\n   * Returns the CSS class name associated with the alias by a previous call to\n   * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n   */\n  classNameForFontAlias(alias: string): string {\n    return this._fontCssClassesByAlias.get(alias) || alias;\n  }\n\n  /**\n   * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  setDefaultFontSetClass(...classNames: string[]): this {\n    this._defaultFontSetClass = classNames;\n    return this;\n  }\n\n  /**\n   * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  getDefaultFontSetClass(): string[] {\n    return this._defaultFontSetClass;\n  }\n\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n   * The response from the URL may be cached so this will not always cause an HTTP request, but\n   * the produced element will always be a new copy of the originally fetched icon. (That is,\n   * it will not contain any modifications made to elements previously returned).\n   *\n   * @param safeUrl URL from which to fetch the SVG icon.\n   */\n  getSvgIconFromUrl(safeUrl: SafeResourceUrl): Observable<SVGElement> {\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n\n    const cachedIcon = this._cachedIconsByUrl.get(url);\n\n    if (cachedIcon) {\n      return observableOf(cloneSvg(cachedIcon));\n    }\n\n    return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(\n      tap(svg => this._cachedIconsByUrl.set(url!, svg)),\n      map(svg => cloneSvg(svg)),\n    );\n  }\n\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n   * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n   * if not, the Observable will throw an error.\n   *\n   * @param name Name of the icon to be retrieved.\n   * @param namespace Namespace in which to look for the icon.\n   */\n  getNamedSvgIcon(name: string, namespace: string = ''): Observable<SVGElement> {\n    const key = iconKey(namespace, name);\n    let config = this._svgIconConfigs.get(key);\n\n    // Return (copy of) cached icon if possible.\n    if (config) {\n      return this._getSvgFromConfig(config);\n    }\n\n    // Otherwise try to resolve the config from one of the resolver functions.\n    config = this._getIconConfigFromResolvers(namespace, name);\n\n    if (config) {\n      this._svgIconConfigs.set(key, config);\n      return this._getSvgFromConfig(config);\n    }\n\n    // See if we have any icon sets registered for the namespace.\n    const iconSetConfigs = this._iconSetConfigs.get(namespace);\n\n    if (iconSetConfigs) {\n      return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n    }\n\n    return observableThrow(getMatIconNameNotFoundError(key));\n  }\n\n  ngOnDestroy() {\n    this._resolvers = [];\n    this._svgIconConfigs.clear();\n    this._iconSetConfigs.clear();\n    this._cachedIconsByUrl.clear();\n  }\n\n  /**\n   * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n   */\n  private _getSvgFromConfig(config: SvgIconConfig): Observable<SVGElement> {\n    if (config.svgText) {\n      // We already have the SVG element for this icon, return a copy.\n      return observableOf(cloneSvg(this._svgElementFromConfig(config as LoadedSvgIconConfig)));\n    } else {\n      // Fetch the icon from the config's URL, cache it, and return a copy.\n      return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n    }\n  }\n\n  /**\n   * Attempts to find an icon with the specified name in any of the SVG icon sets.\n   * First searches the available cached icons for a nested element with a matching name, and\n   * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n   * that have not been cached, and searches again after all fetches are completed.\n   * The returned Observable produces the SVG element if possible, and throws\n   * an error if no icon with the specified name can be found.\n   */\n  private _getSvgFromIconSetConfigs(\n    name: string,\n    iconSetConfigs: SvgIconConfig[],\n  ): Observable<SVGElement> {\n    // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n    // requested name.\n    const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n\n    if (namedIcon) {\n      // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n      // time anyway, there's probably not much advantage compared to just always extracting\n      // it from the icon set.\n      return observableOf(namedIcon);\n    }\n\n    // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n    // fetched, fetch them now and look for iconName in the results.\n    const iconSetFetchRequests: Observable<TrustedHTML | null>[] = iconSetConfigs\n      .filter(iconSetConfig => !iconSetConfig.svgText)\n      .map(iconSetConfig => {\n        return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(\n          catchError((err: HttpErrorResponse) => {\n            const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n\n            // Swallow errors fetching individual URLs so the\n            // combined Observable won't necessarily fail.\n            const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n            this._errorHandler.handleError(new Error(errorMessage));\n            return observableOf(null);\n          }),\n        );\n      });\n\n    // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n    // cached SVG element (unless the request failed), and we can check again for the icon.\n    return forkJoin(iconSetFetchRequests).pipe(\n      map(() => {\n        const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n\n        // TODO: add an ngDevMode check\n        if (!foundIcon) {\n          throw getMatIconNameNotFoundError(name);\n        }\n\n        return foundIcon;\n      }),\n    );\n  }\n\n  /**\n   * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  private _extractIconWithNameFromAnySet(\n    iconName: string,\n    iconSetConfigs: SvgIconConfig[],\n  ): SVGElement | null {\n    // Iterate backwards, so icon sets added later have precedence.\n    for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n      const config = iconSetConfigs[i];\n\n      // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n      // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n      // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n      // some of the parsing.\n      if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n        const svg = this._svgElementFromConfig(config as LoadedSvgIconConfig);\n        const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n        if (foundIcon) {\n          return foundIcon;\n        }\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n   * from it.\n   */\n  private _loadSvgIconFromConfig(config: SvgIconConfig): Observable<SVGElement> {\n    return this._fetchIcon(config).pipe(\n      tap(svgText => (config.svgText = svgText)),\n      map(() => this._svgElementFromConfig(config as LoadedSvgIconConfig)),\n    );\n  }\n\n  /**\n   * Loads the content of the icon set URL specified in the\n   * SvgIconConfig and attaches it to the config.\n   */\n  private _loadSvgIconSetFromConfig(config: SvgIconConfig): Observable<TrustedHTML | null> {\n    if (config.svgText) {\n      return observableOf(null);\n    }\n\n    return this._fetchIcon(config).pipe(tap(svgText => (config.svgText = svgText)));\n  }\n\n  /**\n   * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  private _extractSvgIconFromSet(\n    iconSet: SVGElement,\n    iconName: string,\n    options?: IconOptions,\n  ): SVGElement | null {\n    // Use the `id=\"iconName\"` syntax in order to escape special\n    // characters in the ID (versus using the #iconName syntax).\n    const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n\n    if (!iconSource) {\n      return null;\n    }\n\n    // Clone the element and remove the ID to prevent multiple elements from being added\n    // to the page with the same ID.\n    const iconElement = iconSource.cloneNode(true) as Element;\n    iconElement.removeAttribute('id');\n\n    // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n    // the content of a new <svg> node.\n    if (iconElement.nodeName.toLowerCase() === 'svg') {\n      return this._setSvgAttributes(iconElement as SVGElement, options);\n    }\n\n    // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n    // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n    // tag is problematic on Firefox, because it needs to include the current page path.\n    if (iconElement.nodeName.toLowerCase() === 'symbol') {\n      return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n    }\n\n    // createElement('SVG') doesn't work as expected; the DOM ends up with\n    // the correct nodes, but the SVG content doesn't render. Instead we\n    // have to create an empty SVG node using innerHTML and append its content.\n    // Elements created using DOMParser.parseFromString have the same problem.\n    // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    // Clone the node so we don't remove it from the parent icon set element.\n    svg.appendChild(iconElement);\n\n    return this._setSvgAttributes(svg, options);\n  }\n\n  /**\n   * Creates a DOM element from the given SVG string.\n   */\n  private _svgElementFromString(str: TrustedHTML): SVGElement {\n    const div = this._document.createElement('DIV');\n    div.innerHTML = str as unknown as string;\n    const svg = div.querySelector('svg') as SVGElement;\n\n    // TODO: add an ngDevMode check\n    if (!svg) {\n      throw Error('<svg> tag not found');\n    }\n\n    return svg;\n  }\n\n  /**\n   * Converts an element into an SVG node by cloning all of its children.\n   */\n  private _toSvgElement(element: Element): SVGElement {\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    const attributes = element.attributes;\n\n    // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n    for (let i = 0; i < attributes.length; i++) {\n      const {name, value} = attributes[i];\n\n      if (name !== 'id') {\n        svg.setAttribute(name, value);\n      }\n    }\n\n    for (let i = 0; i < element.childNodes.length; i++) {\n      if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n        svg.appendChild(element.childNodes[i].cloneNode(true));\n      }\n    }\n\n    return svg;\n  }\n\n  /**\n   * Sets the default attributes for an SVG element to be used as an icon.\n   */\n  private _setSvgAttributes(svg: SVGElement, options?: IconOptions): SVGElement {\n    svg.setAttribute('fit', '');\n    svg.setAttribute('height', '100%');\n    svg.setAttribute('width', '100%');\n    svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n    svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n\n    if (options && options.viewBox) {\n      svg.setAttribute('viewBox', options.viewBox);\n    }\n\n    return svg;\n  }\n\n  /**\n   * Returns an Observable which produces the string contents of the given icon. Results may be\n   * cached, so future calls with the same URL may not cause another HTTP request.\n   */\n  private _fetchIcon(iconConfig: SvgIconConfig): Observable<TrustedHTML> {\n    const {url: safeUrl, options} = iconConfig;\n    const withCredentials = options?.withCredentials ?? false;\n\n    if (!this._httpClient) {\n      throw getMatIconNoHttpProviderError();\n    }\n\n    // TODO: add an ngDevMode check\n    if (safeUrl == null) {\n      throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n    }\n\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n\n    // TODO: add an ngDevMode check\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n\n    // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n    // already a request in progress for that URL. It's necessary to call share() on the\n    // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n    const inProgressFetch = this._inProgressUrlFetches.get(url);\n\n    if (inProgressFetch) {\n      return inProgressFetch;\n    }\n\n    const req = this._httpClient.get(url, {responseType: 'text', withCredentials}).pipe(\n      map(svg => {\n        // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n        // trusted HTML.\n        return trustedHTMLFromString(svg);\n      }),\n      finalize(() => this._inProgressUrlFetches.delete(url)),\n      share(),\n    );\n\n    this._inProgressUrlFetches.set(url, req);\n    return req;\n  }\n\n  /**\n   * Registers an icon config by name in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param iconName Name under which to register the config.\n   * @param config Config to be registered.\n   */\n  private _addSvgIconConfig(namespace: string, iconName: string, config: SvgIconConfig): this {\n    this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n    return this;\n  }\n\n  /**\n   * Registers an icon set config in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param config Config to be registered.\n   */\n  private _addSvgIconSetConfig(namespace: string, config: SvgIconConfig): this {\n    const configNamespace = this._iconSetConfigs.get(namespace);\n\n    if (configNamespace) {\n      configNamespace.push(config);\n    } else {\n      this._iconSetConfigs.set(namespace, [config]);\n    }\n\n    return this;\n  }\n\n  /** Parses a config's text into an SVG element. */\n  private _svgElementFromConfig(config: LoadedSvgIconConfig): SVGElement {\n    if (!config.svgElement) {\n      const svg = this._svgElementFromString(config.svgText);\n      this._setSvgAttributes(svg, config.options);\n      config.svgElement = svg;\n    }\n\n    return config.svgElement;\n  }\n\n  /** Tries to create an icon config through the registered resolver functions. */\n  private _getIconConfigFromResolvers(namespace: string, name: string): SvgIconConfig | undefined {\n    for (let i = 0; i < this._resolvers.length; i++) {\n      const result = this._resolvers[i](name, namespace);\n\n      if (result) {\n        return isSafeUrlWithOptions(result)\n          ? new SvgIconConfig(result.url, null, result.options)\n          : new SvgIconConfig(result, null);\n      }\n    }\n\n    return undefined;\n  }\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function ICON_REGISTRY_PROVIDER_FACTORY(\n  parentRegistry: MatIconRegistry,\n  httpClient: HttpClient,\n  sanitizer: DomSanitizer,\n  errorHandler: ErrorHandler,\n  document?: any,\n) {\n  return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const ICON_REGISTRY_PROVIDER = {\n  // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n  provide: MatIconRegistry,\n  deps: [\n    [new Optional(), new SkipSelf(), MatIconRegistry],\n    [new Optional(), HttpClient],\n    DomSanitizer,\n    ErrorHandler,\n    [new Optional(), DOCUMENT as InjectionToken<any>],\n  ],\n  useFactory: ICON_REGISTRY_PROVIDER_FACTORY,\n};\n\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg: SVGElement): SVGElement {\n  return svg.cloneNode(true) as SVGElement;\n}\n\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace: string, name: string) {\n  return namespace + ':' + name;\n}\n\nfunction isSafeUrlWithOptions(value: any): value is SafeResourceUrlWithIconOptions {\n  return !!(value.url && value.options);\n}\n"], "names": ["observableOf", "observableThrow"], "mappings": ";;;;;;;;;AAmCA;;;AAGG;AACH,IAAI,MAA4C;AAEhD;;;AAGG;AACH,SAAS,SAAS,GAAA;AAChB,IAAA,IAAI,MAAM,KAAK,SAAS,EAAE;QACxB,MAAM,GAAG,IAAI;AACb,QAAA,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;YACjC,MAAM,QAAQ,GAAG,MAA8D;AAC/E,YAAA,IAAI,QAAQ,CAAC,YAAY,KAAK,SAAS,EAAE;gBACvC,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,oBAAoB,EAAE;AAChE,oBAAA,UAAU,EAAE,CAAC,CAAS,KAAK,CAAC;AAC7B,iBAAA,CAAC;;;;AAIR,IAAA,OAAO,MAAM;AACf;AAEA;;;;;;;;AAQG;AACG,SAAU,qBAAqB,CAAC,IAAY,EAAA;IAChD,OAAO,SAAS,EAAE,EAAE,UAAU,CAAC,IAAI,CAAC,IAAK,IAA+B;AAC1E;;AC9CA;;;;AAIG;AACG,SAAU,2BAA2B,CAAC,QAAgB,EAAA;AAC1D,IAAA,OAAO,KAAK,CAAC,CAAA,mCAAA,EAAsC,QAAQ,CAAA,CAAA,CAAG,CAAC;AACjE;AAEA;;;;AAIG;SACa,6BAA6B,GAAA;IAC3C,OAAO,KAAK,CACV,iEAAiE;AAC/D,QAAA,mDAAmD,CACtD;AACH;AAEA;;;;AAIG;AACG,SAAU,kCAAkC,CAAC,GAAoB,EAAA;IACrE,OAAO,KAAK,CACV,CAAwE,sEAAA,CAAA;QACtE,CAAkD,+CAAA,EAAA,GAAG,CAAI,EAAA,CAAA,CAC5D;AACH;AAEA;;;;AAIG;AACG,SAAU,sCAAsC,CAAC,OAAiB,EAAA;IACtE,OAAO,KAAK,CACV,CAA0E,wEAAA,CAAA;QACxE,CAAkD,+CAAA,EAAA,OAAO,CAAI,EAAA,CAAA,CAChE;AACH;AA0BA;;;AAGG;AACH,MAAM,aAAa,CAAA;AAIR,IAAA,GAAA;AACA,IAAA,OAAA;AACA,IAAA,OAAA;AALT,IAAA,UAAU;AAEV,IAAA,WAAA,CACS,GAAoB,EACpB,OAA2B,EAC3B,OAAqB,EAAA;QAFrB,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAO,CAAA,OAAA,GAAP,OAAO;QACP,IAAO,CAAA,OAAA,GAAP,OAAO;;AAEjB;AAKD;;;;;;AAMG;MAEU,eAAe,CAAA;AAkCJ,IAAA,WAAA;AACZ,IAAA,UAAA;AAES,IAAA,aAAA;AApCX,IAAA,SAAS;AAEjB;;AAEG;AACK,IAAA,eAAe,GAAG,IAAI,GAAG,EAAyB;AAE1D;;;AAGG;AACK,IAAA,eAAe,GAAG,IAAI,GAAG,EAA2B;;AAGpD,IAAA,iBAAiB,GAAG,IAAI,GAAG,EAAsB;;AAGjD,IAAA,qBAAqB,GAAG,IAAI,GAAG,EAAmC;;AAGlE,IAAA,sBAAsB,GAAG,IAAI,GAAG,EAAkB;;IAGlD,UAAU,GAAmB,EAAE;AAEvC;;;;AAIG;AACK,IAAA,oBAAoB,GAAG,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;AAEtE,IAAA,WAAA,CACsB,WAAuB,EACnC,UAAwB,EACF,QAAa,EAC1B,aAA2B,EAAA;QAHxB,IAAW,CAAA,WAAA,GAAX,WAAW;QACvB,IAAU,CAAA,UAAA,GAAV,UAAU;QAED,IAAa,CAAA,aAAA,GAAb,aAAa;AAE9B,QAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;;AAG3B;;;;AAIG;AACH,IAAA,UAAU,CAAC,QAAgB,EAAE,GAAoB,EAAE,OAAqB,EAAA;AACtE,QAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,CAAC;;AAG/D;;;;AAIG;AACH,IAAA,iBAAiB,CAAC,QAAgB,EAAE,OAAiB,EAAE,OAAqB,EAAA;AAC1E,QAAA,OAAO,IAAI,CAAC,4BAA4B,CAAC,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC;;AAG1E;;;;;AAKG;AACH,IAAA,qBAAqB,CACnB,SAAiB,EACjB,QAAgB,EAChB,GAAoB,EACpB,OAAqB,EAAA;AAErB,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;;AAG3F;;;;;;;AAOG;AACH,IAAA,kBAAkB,CAAC,QAAsB,EAAA;AACvC,QAAA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC9B,QAAA,OAAO,IAAI;;AAGb;;;;;AAKG;AACH,IAAA,4BAA4B,CAC1B,SAAiB,EACjB,QAAgB,EAChB,OAAiB,EACjB,OAAqB,EAAA;AAErB,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC;;QAG5E,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,MAAM,sCAAsC,CAAC,OAAO,CAAC;;;AAIvD,QAAA,MAAM,cAAc,GAAG,qBAAqB,CAAC,YAAY,CAAC;AAC1D,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAC3B,SAAS,EACT,QAAQ,EACR,IAAI,aAAa,CAAC,EAAE,EAAE,cAAc,EAAE,OAAO,CAAC,CAC/C;;AAGH;;;AAGG;IACH,aAAa,CAAC,GAAoB,EAAE,OAAqB,EAAA;QACvD,OAAO,IAAI,CAAC,wBAAwB,CAAC,EAAE,EAAE,GAAG,EAAE,OAAO,CAAC;;AAGxD;;;AAGG;IACH,oBAAoB,CAAC,OAAiB,EAAE,OAAqB,EAAA;QAC3D,OAAO,IAAI,CAAC,+BAA+B,CAAC,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC;;AAGnE;;;;AAIG;AACH,IAAA,wBAAwB,CAAC,SAAiB,EAAE,GAAoB,EAAE,OAAqB,EAAA;AACrF,QAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,aAAa,CAAC,GAAG,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;;AAGpF;;;;AAIG;AACH,IAAA,+BAA+B,CAC7B,SAAiB,EACjB,OAAiB,EACjB,OAAqB,EAAA;AAErB,QAAA,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC;QAE5E,IAAI,CAAC,YAAY,EAAE;AACjB,YAAA,MAAM,sCAAsC,CAAC,OAAO,CAAC;;;AAIvD,QAAA,MAAM,cAAc,GAAG,qBAAqB,CAAC,YAAY,CAAC;AAC1D,QAAA,OAAO,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,IAAI,aAAa,CAAC,EAAE,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;;AAG7F;;;;;;;;;;;;;;;;;;;;AAoBG;AACH,IAAA,sBAAsB,CAAC,KAAa,EAAE,UAAA,GAAqB,KAAK,EAAA;QAC9D,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC;AAClD,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACH,IAAA,qBAAqB,CAAC,KAAa,EAAA;QACjC,OAAO,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK;;AAGxD;;;AAGG;IACH,sBAAsB,CAAC,GAAG,UAAoB,EAAA;AAC5C,QAAA,IAAI,CAAC,oBAAoB,GAAG,UAAU;AACtC,QAAA,OAAO,IAAI;;AAGb;;;AAGG;IACH,sBAAsB,GAAA;QACpB,OAAO,IAAI,CAAC,oBAAoB;;AAGlC;;;;;;;AAOG;AACH,IAAA,iBAAiB,CAAC,OAAwB,EAAA;AACxC,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC;QAE3E,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,MAAM,kCAAkC,CAAC,OAAO,CAAC;;QAGnD,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC;QAElD,IAAI,UAAU,EAAE;AACd,YAAA,OAAOA,EAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;;QAG3C,OAAO,IAAI,CAAC,sBAAsB,CAAC,IAAI,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CACvE,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAI,EAAE,GAAG,CAAC,CAAC,EACjD,GAAG,CAAC,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAC1B;;AAGH;;;;;;;AAOG;AACH,IAAA,eAAe,CAAC,IAAY,EAAE,SAAA,GAAoB,EAAE,EAAA;QAClD,MAAM,GAAG,GAAG,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC;QACpC,IAAI,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC;;QAG1C,IAAI,MAAM,EAAE;AACV,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;;;QAIvC,MAAM,GAAG,IAAI,CAAC,2BAA2B,CAAC,SAAS,EAAE,IAAI,CAAC;QAE1D,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC;AACrC,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;;;QAIvC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC;QAE1D,IAAI,cAAc,EAAE;YAClB,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,cAAc,CAAC;;AAG7D,QAAA,OAAOC,UAAe,CAAC,2BAA2B,CAAC,GAAG,CAAC,CAAC;;IAG1D,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,UAAU,GAAG,EAAE;AACpB,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;AAC5B,QAAA,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;AAC5B,QAAA,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;;AAGhC;;AAEG;AACK,IAAA,iBAAiB,CAAC,MAAqB,EAAA;AAC7C,QAAA,IAAI,MAAM,CAAC,OAAO,EAAE;;AAElB,YAAA,OAAOD,EAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAA6B,CAAC,CAAC,CAAC;;aACnF;;YAEL,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;;;AAI9E;;;;;;;AAOG;IACK,yBAAyB,CAC/B,IAAY,EACZ,cAA+B,EAAA;;;QAI/B,MAAM,SAAS,GAAG,IAAI,CAAC,8BAA8B,CAAC,IAAI,EAAE,cAAc,CAAC;QAE3E,IAAI,SAAS,EAAE;;;;AAIb,YAAA,OAAOA,EAAY,CAAC,SAAS,CAAC;;;;QAKhC,MAAM,oBAAoB,GAAqC;aAC5D,MAAM,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,OAAO;aAC9C,GAAG,CAAC,aAAa,IAAG;AACnB,YAAA,OAAO,IAAI,CAAC,yBAAyB,CAAC,aAAa,CAAC,CAAC,IAAI,CACvD,UAAU,CAAC,CAAC,GAAsB,KAAI;AACpC,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,CAAC;;;gBAIrF,MAAM,YAAY,GAAG,CAAyB,sBAAA,EAAA,GAAG,YAAY,GAAG,CAAC,OAAO,CAAA,CAAE;gBAC1E,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC;AACvD,gBAAA,OAAOA,EAAY,CAAC,IAAI,CAAC;aAC1B,CAAC,CACH;AACH,SAAC,CAAC;;;QAIJ,OAAO,QAAQ,CAAC,oBAAoB,CAAC,CAAC,IAAI,CACxC,GAAG,CAAC,MAAK;YACP,MAAM,SAAS,GAAG,IAAI,CAAC,8BAA8B,CAAC,IAAI,EAAE,cAAc,CAAC;;YAG3E,IAAI,CAAC,SAAS,EAAE;AACd,gBAAA,MAAM,2BAA2B,CAAC,IAAI,CAAC;;AAGzC,YAAA,OAAO,SAAS;SACjB,CAAC,CACH;;AAGH;;;;AAIG;IACK,8BAA8B,CACpC,QAAgB,EAChB,cAA+B,EAAA;;AAG/B,QAAA,KAAK,IAAI,CAAC,GAAG,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACnD,YAAA,MAAM,MAAM,GAAG,cAAc,CAAC,CAAC,CAAC;;;;;AAMhC,YAAA,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;gBACtE,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAA6B,CAAC;AACrE,gBAAA,MAAM,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,GAAG,EAAE,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC;gBAC5E,IAAI,SAAS,EAAE;AACb,oBAAA,OAAO,SAAS;;;;AAItB,QAAA,OAAO,IAAI;;AAGb;;;AAGG;AACK,IAAA,sBAAsB,CAAC,MAAqB,EAAA;AAClD,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CACjC,GAAG,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,EAC1C,GAAG,CAAC,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAA6B,CAAC,CAAC,CACrE;;AAGH;;;AAGG;AACK,IAAA,yBAAyB,CAAC,MAAqB,EAAA;AACrD,QAAA,IAAI,MAAM,CAAC,OAAO,EAAE;AAClB,YAAA,OAAOA,EAAY,CAAC,IAAI,CAAC;;QAG3B,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,KAAK,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC;;AAGjF;;;;AAIG;AACK,IAAA,sBAAsB,CAC5B,OAAmB,EACnB,QAAgB,EAChB,OAAqB,EAAA;;;QAIrB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,CAAC,CAAQ,KAAA,EAAA,QAAQ,CAAI,EAAA,CAAA,CAAC;QAE9D,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,OAAO,IAAI;;;;QAKb,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAY;AACzD,QAAA,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC;;;QAIjC,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK,EAAE;YAChD,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAyB,EAAE,OAAO,CAAC;;;;;QAMnE,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,QAAQ,EAAE;AACnD,YAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC;;;;;;;QAQzE,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;;AAE5E,QAAA,GAAG,CAAC,WAAW,CAAC,WAAW,CAAC;QAE5B,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,OAAO,CAAC;;AAG7C;;AAEG;AACK,IAAA,qBAAqB,CAAC,GAAgB,EAAA;QAC5C,MAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;AAC/C,QAAA,GAAG,CAAC,SAAS,GAAG,GAAwB;QACxC,MAAM,GAAG,GAAG,GAAG,CAAC,aAAa,CAAC,KAAK,CAAe;;QAGlD,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,MAAM,KAAK,CAAC,qBAAqB,CAAC;;AAGpC,QAAA,OAAO,GAAG;;AAGZ;;AAEG;AACK,IAAA,aAAa,CAAC,OAAgB,EAAA;QACpC,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC;AAC5E,QAAA,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU;;AAGrC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,MAAM,EAAC,IAAI,EAAE,KAAK,EAAC,GAAG,UAAU,CAAC,CAAC,CAAC;AAEnC,YAAA,IAAI,IAAI,KAAK,IAAI,EAAE;AACjB,gBAAA,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC;;;AAIjC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAClD,YAAA,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE;AAClE,gBAAA,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;;;AAI1D,QAAA,OAAO,GAAG;;AAGZ;;AAEG;IACK,iBAAiB,CAAC,GAAe,EAAE,OAAqB,EAAA;AAC9D,QAAA,GAAG,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC;AAC3B,QAAA,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;AAClC,QAAA,GAAG,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC;AACjC,QAAA,GAAG,CAAC,YAAY,CAAC,qBAAqB,EAAE,eAAe,CAAC;QACxD,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAEvC,QAAA,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO,EAAE;YAC9B,GAAG,CAAC,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,CAAC;;AAG9C,QAAA,OAAO,GAAG;;AAGZ;;;AAGG;AACK,IAAA,UAAU,CAAC,UAAyB,EAAA;QAC1C,MAAM,EAAC,GAAG,EAAE,OAAO,EAAE,OAAO,EAAC,GAAG,UAAU;AAC1C,QAAA,MAAM,eAAe,GAAG,OAAO,EAAE,eAAe,IAAI,KAAK;AAEzD,QAAA,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,MAAM,6BAA6B,EAAE;;;AAIvC,QAAA,IAAI,OAAO,IAAI,IAAI,EAAE;AACnB,YAAA,MAAM,KAAK,CAAC,CAAA,4BAAA,EAA+B,OAAO,CAAA,EAAA,CAAI,CAAC;;AAGzD,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,OAAO,CAAC;;QAG3E,IAAI,CAAC,GAAG,EAAE;AACR,YAAA,MAAM,kCAAkC,CAAC,OAAO,CAAC;;;;;QAMnD,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,CAAC;QAE3D,IAAI,eAAe,EAAE;AACnB,YAAA,OAAO,eAAe;;QAGxB,MAAM,GAAG,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,EAAC,YAAY,EAAE,MAAM,EAAE,eAAe,EAAC,CAAC,CAAC,IAAI,CACjF,GAAG,CAAC,GAAG,IAAG;;;AAGR,YAAA,OAAO,qBAAqB,CAAC,GAAG,CAAC;SAClC,CAAC,EACF,QAAQ,CAAC,MAAM,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EACtD,KAAK,EAAE,CACR;QAED,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC;AACxC,QAAA,OAAO,GAAG;;AAGZ;;;;;AAKG;AACK,IAAA,iBAAiB,CAAC,SAAiB,EAAE,QAAgB,EAAE,MAAqB,EAAA;AAClF,QAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,SAAS,EAAE,QAAQ,CAAC,EAAE,MAAM,CAAC;AAC9D,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;IACK,oBAAoB,CAAC,SAAiB,EAAE,MAAqB,EAAA;QACnE,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,CAAC;QAE3D,IAAI,eAAe,EAAE;AACnB,YAAA,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC;;aACvB;YACL,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,MAAM,CAAC,CAAC;;AAG/C,QAAA,OAAO,IAAI;;;AAIL,IAAA,qBAAqB,CAAC,MAA2B,EAAA;AACvD,QAAA,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;YACtB,MAAM,GAAG,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,OAAO,CAAC;YACtD,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,MAAM,CAAC,OAAO,CAAC;AAC3C,YAAA,MAAM,CAAC,UAAU,GAAG,GAAG;;QAGzB,OAAO,MAAM,CAAC,UAAU;;;IAIlB,2BAA2B,CAAC,SAAiB,EAAE,IAAY,EAAA;AACjE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,SAAS,CAAC;YAElD,IAAI,MAAM,EAAE;gBACV,OAAO,oBAAoB,CAAC,MAAM;AAChC,sBAAE,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,CAAC,OAAO;sBAClD,IAAI,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC;;;AAIvC,QAAA,OAAO,SAAS;;AA/lBP,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,wFAoCJ,QAAQ,EAAA,QAAA,EAAA,IAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,YAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AApCnB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,cADH,MAAM,EAAA,CAAA;;2FAClB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;0BAmC3B;;0BAEA;;0BAAY,MAAM;2BAAC,QAAQ;;AA+jBhC;;;;AAIG;AACG,SAAU,8BAA8B,CAC5C,cAA+B,EAC/B,UAAsB,EACtB,SAAuB,EACvB,YAA0B,EAC1B,QAAc,EAAA;AAEd,IAAA,OAAO,cAAc,IAAI,IAAI,eAAe,CAAC,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,YAAY,CAAC;AAC7F;AAEA;;;;AAIG;AACU,MAAA,sBAAsB,GAAG;;AAEpC,IAAA,OAAO,EAAE,eAAe;AACxB,IAAA,IAAI,EAAE;QACJ,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,eAAe,CAAC;AACjD,QAAA,CAAC,IAAI,QAAQ,EAAE,EAAE,UAAU,CAAC;QAC5B,YAAY;QACZ,YAAY;AACZ,QAAA,CAAC,IAAI,QAAQ,EAAE,EAAE,QAA+B,CAAC;AAClD,KAAA;AACD,IAAA,UAAU,EAAE,8BAA8B;;AAG5C;AACA,SAAS,QAAQ,CAAC,GAAe,EAAA;AAC/B,IAAA,OAAO,GAAG,CAAC,SAAS,CAAC,IAAI,CAAe;AAC1C;AAEA;AACA,SAAS,OAAO,CAAC,SAAiB,EAAE,IAAY,EAAA;AAC9C,IAAA,OAAO,SAAS,GAAG,GAAG,GAAG,IAAI;AAC/B;AAEA,SAAS,oBAAoB,CAAC,KAAU,EAAA;IACtC,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC;AACvC;;;;"}