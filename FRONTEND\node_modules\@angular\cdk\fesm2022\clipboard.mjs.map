{"version": 3, "file": "clipboard.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/clipboard/pending-copy.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/clipboard/clipboard.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/clipboard/copy-to-clipboard.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/clipboard/clipboard-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * A pending copy-to-clipboard operation.\n *\n * The implementation of copying text to the clipboard modifies the DOM and\n * forces a re-layout. This re-layout can take too long if the string is large,\n * causing the execCommand('copy') to happen too long after the user clicked.\n * This results in the browser refusing to copy. This object lets the\n * re-layout happen in a separate tick from copying by providing a copy function\n * that can be called later.\n *\n * Destroy must be called when no longer in use, regardless of whether `copy` is\n * called.\n */\nexport class PendingCopy {\n  private _textarea: HTMLTextAreaElement | undefined;\n\n  constructor(\n    text: string,\n    private readonly _document: Document,\n  ) {\n    const textarea = (this._textarea = this._document.createElement('textarea'));\n    const styles = textarea.style;\n\n    // Hide the element for display and accessibility. Set a fixed position so the page layout\n    // isn't affected. We use `fixed` with `top: 0`, because focus is moved into the textarea\n    // for a split second and if it's off-screen, some browsers will attempt to scroll it into view.\n    styles.position = 'fixed';\n    styles.top = styles.opacity = '0';\n    styles.left = '-999em';\n    textarea.setAttribute('aria-hidden', 'true');\n    textarea.value = text;\n    // Making the textarea `readonly` prevents the screen from jumping on iOS Safari (see #25169).\n    textarea.readOnly = true;\n    // The element needs to be inserted into the fullscreen container, if the page\n    // is in fullscreen mode, otherwise the browser won't execute the copy command.\n    (this._document.fullscreenElement || this._document.body).appendChild(textarea);\n  }\n\n  /** Finishes copying the text. */\n  copy(): boolean {\n    const textarea = this._textarea;\n    let successful = false;\n\n    try {\n      // Older browsers could throw if copy is not supported.\n      if (textarea) {\n        const currentFocus = this._document.activeElement as HTMLOrSVGElement | null;\n\n        textarea.select();\n        textarea.setSelectionRange(0, textarea.value.length);\n        successful = this._document.execCommand('copy');\n\n        if (currentFocus) {\n          currentFocus.focus();\n        }\n      }\n    } catch {\n      // Discard error.\n      // Initial setting of {@code successful} will represent failure here.\n    }\n\n    return successful;\n  }\n\n  /** Cleans up DOM changes used to perform the copy operation. */\n  destroy() {\n    const textarea = this._textarea;\n\n    if (textarea) {\n      textarea.remove();\n      this._textarea = undefined;\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, inject, DOCUMENT} from '@angular/core';\nimport {PendingCopy} from './pending-copy';\n\n/**\n * A service for copying text to the clipboard.\n */\n@Injectable({providedIn: 'root'})\nexport class Clipboard {\n  private readonly _document = inject(DOCUMENT);\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /**\n   * Copies the provided text into the user's clipboard.\n   *\n   * @param text The string to copy.\n   * @returns Whether the operation was successful.\n   */\n  copy(text: string): boolean {\n    const pendingCopy = this.beginCopy(text);\n    const successful = pendingCopy.copy();\n    pendingCopy.destroy();\n\n    return successful;\n  }\n\n  /**\n   * Prepares a string to be copied later. This is useful for large strings\n   * which take too long to successfully render and be copied in the same tick.\n   *\n   * The caller must call `destroy` on the returned `PendingCopy`.\n   *\n   * @param text The string to copy.\n   * @returns the pending copy operation.\n   */\n  beginCopy(text: string): PendingCopy {\n    return new PendingCopy(text, this._document);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  EventEmitter,\n  Input,\n  Output,\n  NgZone,\n  InjectionToken,\n  OnDestroy,\n  inject,\n} from '@angular/core';\nimport {Clipboard} from './clipboard';\nimport {PendingCopy} from './pending-copy';\n\n/** Object that can be used to configure the default options for `CdkCopyToClipboard`. */\nexport interface CdkCopyToClipboardConfig {\n  /** Default number of attempts to make when copying text to the clipboard. */\n  attempts?: number;\n}\n\n/** Injection token that can be used to provide the default options to `CdkCopyToClipboard`. */\nexport const CDK_COPY_TO_CLIPBOARD_CONFIG = new InjectionToken<CdkCopyToClipboardConfig>(\n  'CDK_COPY_TO_CLIPBOARD_CONFIG',\n);\n\n/**\n * Provides behavior for a button that when clicked copies content into user's\n * clipboard.\n */\n@Directive({\n  selector: '[cdkCopyToClipboard]',\n  host: {\n    '(click)': 'copy()',\n  },\n})\nexport class CdkCopyToClipboard implements OnDestroy {\n  private _clipboard = inject(Clipboard);\n  private _ngZone = inject(NgZone);\n\n  /** Content to be copied. */\n  @Input('cdkCopyToClipboard') text: string = '';\n\n  /**\n   * How many times to attempt to copy the text. This may be necessary for longer text, because\n   * the browser needs time to fill an intermediate textarea element and copy the content.\n   */\n  @Input('cdkCopyToClipboardAttempts') attempts: number = 1;\n\n  /**\n   * Emits when some text is copied to the clipboard. The\n   * emitted value indicates whether copying was successful.\n   */\n  @Output('cdkCopyToClipboardCopied') readonly copied = new EventEmitter<boolean>();\n\n  /** Copies that are currently being attempted. */\n  private _pending = new Set<PendingCopy>();\n\n  /** Whether the directive has been destroyed. */\n  private _destroyed: boolean;\n\n  /** Timeout for the current copy attempt. */\n  private _currentTimeout: any;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const config = inject(CDK_COPY_TO_CLIPBOARD_CONFIG, {optional: true});\n\n    if (config && config.attempts != null) {\n      this.attempts = config.attempts;\n    }\n  }\n\n  /** Copies the current text to the clipboard. */\n  copy(attempts: number = this.attempts): void {\n    if (attempts > 1) {\n      let remainingAttempts = attempts;\n      const pending = this._clipboard.beginCopy(this.text);\n      this._pending.add(pending);\n\n      const attempt = () => {\n        const successful = pending.copy();\n        if (!successful && --remainingAttempts && !this._destroyed) {\n          // We use 1 for the timeout since it's more predictable when flushing in unit tests.\n          this._currentTimeout = this._ngZone.runOutsideAngular(() => setTimeout(attempt, 1));\n        } else {\n          this._currentTimeout = null;\n          this._pending.delete(pending);\n          pending.destroy();\n          this.copied.emit(successful);\n        }\n      };\n      attempt();\n    } else {\n      this.copied.emit(this._clipboard.copy(this.text));\n    }\n  }\n\n  ngOnDestroy() {\n    if (this._currentTimeout) {\n      clearTimeout(this._currentTimeout);\n    }\n\n    this._pending.forEach(copy => copy.destroy());\n    this._pending.clear();\n    this._destroyed = true;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\n\nimport {CdkCopyToClipboard} from './copy-to-clipboard';\n\n@NgModule({\n  imports: [CdkCopyToClipboard],\n  exports: [CdkCopyToClipboard],\n})\nexport class ClipboardModule {}\n"], "names": [], "mappings": ";;;AAQA;;;;;;;;;;;;AAYG;MACU,WAAW,CAAA;AAKH,IAAA,SAAA;AAJX,IAAA,SAAS;IAEjB,WACE,CAAA,IAAY,EACK,SAAmB,EAAA;QAAnB,IAAS,CAAA,SAAA,GAAT,SAAS;AAE1B,QAAA,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AAC5E,QAAA,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK;;;;AAK7B,QAAA,MAAM,CAAC,QAAQ,GAAG,OAAO;QACzB,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC,OAAO,GAAG,GAAG;AACjC,QAAA,MAAM,CAAC,IAAI,GAAG,QAAQ;AACtB,QAAA,QAAQ,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;AAC5C,QAAA,QAAQ,CAAC,KAAK,GAAG,IAAI;;AAErB,QAAA,QAAQ,CAAC,QAAQ,GAAG,IAAI;;;AAGxB,QAAA,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,WAAW,CAAC,QAAQ,CAAC;;;IAIjF,IAAI,GAAA;AACF,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS;QAC/B,IAAI,UAAU,GAAG,KAAK;AAEtB,QAAA,IAAI;;YAEF,IAAI,QAAQ,EAAE;AACZ,gBAAA,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,aAAwC;gBAE5E,QAAQ,CAAC,MAAM,EAAE;gBACjB,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC;gBACpD,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC;gBAE/C,IAAI,YAAY,EAAE;oBAChB,YAAY,CAAC,KAAK,EAAE;;;;AAGxB,QAAA,MAAM;;;;AAKR,QAAA,OAAO,UAAU;;;IAInB,OAAO,GAAA;AACL,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS;QAE/B,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,MAAM,EAAE;AACjB,YAAA,IAAI,CAAC,SAAS,GAAG,SAAS;;;AAG/B;;ACtED;;AAEG;MAEU,SAAS,CAAA;AACH,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAG7C,IAAA,WAAA,GAAA;AAEA;;;;;AAKG;AACH,IAAA,IAAI,CAAC,IAAY,EAAA;QACf,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AACxC,QAAA,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,EAAE;QACrC,WAAW,CAAC,OAAO,EAAE;AAErB,QAAA,OAAO,UAAU;;AAGnB;;;;;;;;AAQG;AACH,IAAA,SAAS,CAAC,IAAY,EAAA;QACpB,OAAO,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;;uGA9BnC,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAT,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,SAAS,cADG,MAAM,EAAA,CAAA;;2FAClB,SAAS,EAAA,UAAA,EAAA,CAAA;kBADrB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACahC;MACa,4BAA4B,GAAG,IAAI,cAAc,CAC5D,8BAA8B;AAGhC;;;AAGG;MAOU,kBAAkB,CAAA;AACrB,IAAA,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC;AAC9B,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;;IAGH,IAAI,GAAW,EAAE;AAE9C;;;AAGG;IACkC,QAAQ,GAAW,CAAC;AAEzD;;;AAGG;AAC0C,IAAA,MAAM,GAAG,IAAI,YAAY,EAAW;;AAGzE,IAAA,QAAQ,GAAG,IAAI,GAAG,EAAe;;AAGjC,IAAA,UAAU;;AAGV,IAAA,eAAe;AAIvB,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,4BAA4B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;QAErE,IAAI,MAAM,IAAI,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE;AACrC,YAAA,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ;;;;AAKnC,IAAA,IAAI,CAAC,QAAA,GAAmB,IAAI,CAAC,QAAQ,EAAA;AACnC,QAAA,IAAI,QAAQ,GAAG,CAAC,EAAE;YAChB,IAAI,iBAAiB,GAAG,QAAQ;AAChC,YAAA,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;AACpD,YAAA,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC;YAE1B,MAAM,OAAO,GAAG,MAAK;AACnB,gBAAA,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,EAAE;gBACjC,IAAI,CAAC,UAAU,IAAI,EAAE,iBAAiB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;;AAE1D,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;;qBAC9E;AACL,oBAAA,IAAI,CAAC,eAAe,GAAG,IAAI;AAC3B,oBAAA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;oBAC7B,OAAO,CAAC,OAAO,EAAE;AACjB,oBAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;;AAEhC,aAAC;AACD,YAAA,OAAO,EAAE;;aACJ;AACL,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;;;IAIrD,WAAW,GAAA;AACT,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE;AACxB,YAAA,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC;;AAGpC,QAAA,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;AAC7C,QAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;AACrB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI;;uGAtEb,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,CAAA,oBAAA,EAAA,MAAA,CAAA,EAAA,QAAA,EAAA,CAAA,4BAAA,EAAA,UAAA,CAAA,EAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,0BAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAlB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAN9B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sBAAsB;AAChC,oBAAA,IAAI,EAAE;AACJ,wBAAA,SAAS,EAAE,QAAQ;AACpB,qBAAA;AACF,iBAAA;wDAM8B,IAAI,EAAA,CAAA;sBAAhC,KAAK;uBAAC,oBAAoB;gBAMU,QAAQ,EAAA,CAAA;sBAA5C,KAAK;uBAAC,4BAA4B;gBAMU,MAAM,EAAA,CAAA;sBAAlD,MAAM;uBAAC,0BAA0B;;;MC3CvB,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAf,eAAe,EAAA,OAAA,EAAA,CAHhB,kBAAkB,CAAA,EAAA,OAAA,EAAA,CAClB,kBAAkB,CAAA,EAAA,CAAA;wGAEjB,eAAe,EAAA,CAAA;;2FAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,kBAAkB,CAAC;oBAC7B,OAAO,EAAE,CAAC,kBAAkB,CAAC;AAC9B,iBAAA;;;;;"}