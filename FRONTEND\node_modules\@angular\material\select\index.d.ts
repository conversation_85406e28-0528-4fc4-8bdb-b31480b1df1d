export { d as MAT_SELECT_CONFIG, a as MAT_SELECT_SCROLL_STRATEGY, e as MAT_SELECT_SCROLL_STRATEGY_PROVIDER, b as MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, f as MAT_SELECT_TRIGGER, h as MatSelect, g as MatSelectChange, c as MatSelectConfig, M as MatSelectModule, i as MatSelectTrigger } from '../module.d-bebo7gS5.js';
export { a as MatOptgroup, M as MatOption } from '../option.d-BcvS44bt.js';
export { M as MatLabel } from '../module.d-D1Ym5Wf2.js';
export { b as MatError, M as MatFormField, a as MatHint, c as MatPrefix, d as MatSuffix } from '../form-field.d-C6p5uYjG.js';
import '@angular/core';
import '@angular/cdk/overlay';
import '../index.d-DAhBYbjm.js';
import '../index.d-C5neTPvr.js';
import '../common-module.d-C8xzHJDr.js';
import '@angular/cdk/bidi';
import '../ripple.d-BT30YVLB.js';
import '@angular/cdk/platform';
import '../pseudo-checkbox-module.d-BHmTZ10P.js';
import '@angular/cdk/a11y';
import '@angular/cdk/collections';
import '@angular/cdk/scrolling';
import '@angular/forms';
import 'rxjs';
import '../error-options.d-CGdTZUYk.js';
import '../form-field-control.d-DvB4ZVlf.js';
import '@angular/cdk/observers';
import '@angular/cdk/coercion';
import '../palette.d-BSSFKjO6.js';

/**
 * The following are all the animations for the mat-select component, with each
 * const containing the metadata for one animation.
 *
 * The values below match the implementation of the AngularJS Material mat-select animation.
 * @docs-private
 * @deprecated No longer used, will be removed.
 * @breaking-change 21.0.0
 */
declare const matSelectAnimations: {
    readonly transformPanel: any;
};

export { matSelectAnimations };
