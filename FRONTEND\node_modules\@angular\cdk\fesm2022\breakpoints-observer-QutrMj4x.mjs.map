{"version": 3, "file": "breakpoints-observer-QutrMj4x.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/layout/media-matcher.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/layout/breakpoints-observer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {Injectable, CSP_NONCE, inject} from '@angular/core';\nimport {Platform} from '../platform';\n\n/** Global registry for all dynamically-created, injected media queries. */\nconst mediaQueriesForWebkitCompatibility: Set<string> = new Set<string>();\n\n/** Style tag that holds all of the dynamically-created media queries. */\nlet mediaQueryStyleNode: HTMLStyleElement | undefined;\n\n/** A utility for calling matchMedia queries. */\n@Injectable({providedIn: 'root'})\nexport class MediaMatcher {\n  private _platform = inject(Platform);\n  private _nonce = inject(CSP_NONCE, {optional: true});\n\n  /** The internal matchMedia method to return back a MediaQueryList like object. */\n  private _matchMedia: (query: string) => MediaQueryList;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    this._matchMedia =\n      this._platform.isBrowser && window.matchMedia\n        ? // matchMedia is bound to the window scope intentionally as it is an illegal invocation to\n          // call it from a different scope.\n          window.matchMedia.bind(window)\n        : noopMatchMedia;\n  }\n\n  /**\n   * Evaluates the given media query and returns the native MediaQueryList from which results\n   * can be retrieved.\n   * Confirms the layout engine will trigger for the selector query provided and returns the\n   * MediaQueryList for the query provided.\n   */\n  matchMedia(query: string): MediaQueryList {\n    if (this._platform.WEBKIT || this._platform.BLINK) {\n      createEmptyStyleRule(query, this._nonce);\n    }\n    return this._matchMedia(query);\n  }\n}\n\n/**\n * Creates an empty stylesheet that is used to work around browser inconsistencies related to\n * `matchMedia`. At the time of writing, it handles the following cases:\n * 1. On WebKit browsers, a media query has to have at least one rule in order for `matchMedia`\n * to fire. We work around it by declaring a dummy stylesheet with a `@media` declaration.\n * 2. In some cases Blink browsers will stop firing the `matchMedia` listener if none of the rules\n * inside the `@media` match existing elements on the page. We work around it by having one rule\n * targeting the `body`. See https://github.com/angular/components/issues/23546.\n */\nfunction createEmptyStyleRule(query: string, nonce: string | undefined | null) {\n  if (mediaQueriesForWebkitCompatibility.has(query)) {\n    return;\n  }\n\n  try {\n    if (!mediaQueryStyleNode) {\n      mediaQueryStyleNode = document.createElement('style');\n\n      if (nonce) {\n        mediaQueryStyleNode.setAttribute('nonce', nonce);\n      }\n\n      mediaQueryStyleNode.setAttribute('type', 'text/css');\n      document.head!.appendChild(mediaQueryStyleNode);\n    }\n\n    if (mediaQueryStyleNode.sheet) {\n      mediaQueryStyleNode.sheet.insertRule(`@media ${query} {body{ }}`, 0);\n      mediaQueriesForWebkitCompatibility.add(query);\n    }\n  } catch (e) {\n    console.error(e);\n  }\n}\n\n/** No-op matchMedia replacement for non-browser platforms. */\nfunction noopMatchMedia(query: string): MediaQueryList {\n  // Use `as any` here to avoid adding additional necessary properties for\n  // the noop matcher.\n  return {\n    matches: query === 'all' || query === '',\n    media: query,\n    addListener: () => {},\n    removeListener: () => {},\n  } as any;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {coerceArray} from '../coercion';\nimport {Injectable, NgZone, OnDestroy, inject} from '@angular/core';\nimport {combineLatest, concat, Observable, Observer, Subject} from 'rxjs';\nimport {debounceTime, map, skip, startWith, take, takeUntil} from 'rxjs/operators';\nimport {MediaMatcher} from './media-matcher';\n\n/** The current state of a layout breakpoint. */\nexport interface BreakpointState {\n  /** Whether the breakpoint is currently matching. */\n  matches: boolean;\n  /**\n   * A key boolean pair for each query provided to the observe method,\n   * with its current matched state.\n   */\n  breakpoints: {\n    [key: string]: boolean;\n  };\n}\n\n/** The current state of a layout breakpoint. */\ninterface InternalBreakpointState {\n  /** Whether the breakpoint is currently matching. */\n  matches: boolean;\n  /** The media query being to be matched */\n  query: string;\n}\n\ninterface Query {\n  observable: Observable<InternalBreakpointState>;\n  mql: MediaQueryList;\n}\n\n/** Utility for checking the matching state of `@media` queries. */\n@Injectable({providedIn: 'root'})\nexport class BreakpointObserver implements OnDestroy {\n  private _mediaMatcher = inject(MediaMatcher);\n  private _zone = inject(NgZone);\n\n  /**  A map of all media queries currently being listened for. */\n  private _queries = new Map<string, Query>();\n  /** A subject for all other observables to takeUntil based on. */\n  private readonly _destroySubject = new Subject<void>();\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  /** Completes the active subject, signalling to all other observables to complete. */\n  ngOnDestroy() {\n    this._destroySubject.next();\n    this._destroySubject.complete();\n  }\n\n  /**\n   * Whether one or more media queries match the current viewport size.\n   * @param value One or more media queries to check.\n   * @returns Whether any of the media queries match.\n   */\n  isMatched(value: string | readonly string[]): boolean {\n    const queries = splitQueries(coerceArray(value));\n    return queries.some(mediaQuery => this._registerQuery(mediaQuery).mql.matches);\n  }\n\n  /**\n   * Gets an observable of results for the given queries that will emit new results for any changes\n   * in matching of the given queries.\n   * @param value One or more media queries to check.\n   * @returns A stream of matches for the given queries.\n   */\n  observe(value: string | readonly string[]): Observable<BreakpointState> {\n    const queries = splitQueries(coerceArray(value));\n    const observables = queries.map(query => this._registerQuery(query).observable);\n\n    let stateObservable = combineLatest(observables);\n    // Emit the first state immediately, and then debounce the subsequent emissions.\n    stateObservable = concat(\n      stateObservable.pipe(take(1)),\n      stateObservable.pipe(skip(1), debounceTime(0)),\n    );\n    return stateObservable.pipe(\n      map(breakpointStates => {\n        const response: BreakpointState = {\n          matches: false,\n          breakpoints: {},\n        };\n        breakpointStates.forEach(({matches, query}) => {\n          response.matches = response.matches || matches;\n          response.breakpoints[query] = matches;\n        });\n        return response;\n      }),\n    );\n  }\n\n  /** Registers a specific query to be listened for. */\n  private _registerQuery(query: string): Query {\n    // Only set up a new MediaQueryList if it is not already being listened for.\n    if (this._queries.has(query)) {\n      return this._queries.get(query)!;\n    }\n\n    const mql = this._mediaMatcher.matchMedia(query);\n\n    // Create callback for match changes and add it is as a listener.\n    const queryObservable = new Observable((observer: Observer<MediaQueryListEvent>) => {\n      // Listener callback methods are wrapped to be placed back in ngZone. Callbacks must be placed\n      // back into the zone because matchMedia is only included in Zone.js by loading the\n      // webapis-media-query.js file alongside the zone.js file.  Additionally, some browsers do not\n      // have MediaQueryList inherit from EventTarget, which causes inconsistencies in how Zone.js\n      // patches it.\n      const handler = (e: MediaQueryListEvent): void => this._zone.run(() => observer.next(e));\n      mql.addListener(handler);\n\n      return () => {\n        mql.removeListener(handler);\n      };\n    }).pipe(\n      startWith(mql),\n      map(({matches}) => ({query, matches})),\n      takeUntil(this._destroySubject),\n    );\n\n    // Add the MediaQueryList to the set of queries.\n    const output = {observable: queryObservable, mql};\n    this._queries.set(query, output);\n    return output;\n  }\n}\n\n/**\n * Split each query string into separate query strings if two queries are provided as comma\n * separated.\n */\nfunction splitQueries(queries: readonly string[]): readonly string[] {\n  return queries\n    .map(query => query.split(','))\n    .reduce((a1, a2) => a1.concat(a2))\n    .map(query => query.trim());\n}\n"], "names": [], "mappings": ";;;;;;;AAUA;AACA,MAAM,kCAAkC,GAAgB,IAAI,GAAG,EAAU;AAEzE;AACA,IAAI,mBAAiD;AAErD;MAEa,YAAY,CAAA;AACf,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;IAC5B,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;AAG5C,IAAA,WAAW;AAInB,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,WAAW;AACd,YAAA,IAAI,CAAC,SAAS,CAAC,SAAS,IAAI,MAAM,CAAC;AACjC;;AAEE,oBAAA,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM;kBAC7B,cAAc;;AAGtB;;;;;AAKG;AACH,IAAA,UAAU,CAAC,KAAa,EAAA;AACtB,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;AACjD,YAAA,oBAAoB,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC;;AAE1C,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;;uGA5BrB,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAZ,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,cADA,MAAM,EAAA,CAAA;;2FAClB,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AAiChC;;;;;;;;AAQG;AACH,SAAS,oBAAoB,CAAC,KAAa,EAAE,KAAgC,EAAA;AAC3E,IAAA,IAAI,kCAAkC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QACjD;;AAGF,IAAA,IAAI;QACF,IAAI,CAAC,mBAAmB,EAAE;AACxB,YAAA,mBAAmB,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;YAErD,IAAI,KAAK,EAAE;AACT,gBAAA,mBAAmB,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;;AAGlD,YAAA,mBAAmB,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC;AACpD,YAAA,QAAQ,CAAC,IAAK,CAAC,WAAW,CAAC,mBAAmB,CAAC;;AAGjD,QAAA,IAAI,mBAAmB,CAAC,KAAK,EAAE;YAC7B,mBAAmB,CAAC,KAAK,CAAC,UAAU,CAAC,CAAU,OAAA,EAAA,KAAK,CAAY,UAAA,CAAA,EAAE,CAAC,CAAC;AACpE,YAAA,kCAAkC,CAAC,GAAG,CAAC,KAAK,CAAC;;;IAE/C,OAAO,CAAC,EAAE;AACV,QAAA,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;;AAEpB;AAEA;AACA,SAAS,cAAc,CAAC,KAAa,EAAA;;;IAGnC,OAAO;AACL,QAAA,OAAO,EAAE,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,EAAE;AACxC,QAAA,KAAK,EAAE,KAAK;AACZ,QAAA,WAAW,EAAE,MAAK,GAAG;AACrB,QAAA,cAAc,EAAE,MAAK,GAAG;KAClB;AACV;;ACvDA;MAEa,kBAAkB,CAAA;AACrB,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AACpC,IAAA,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;;AAGtB,IAAA,QAAQ,GAAG,IAAI,GAAG,EAAiB;;AAE1B,IAAA,eAAe,GAAG,IAAI,OAAO,EAAQ;AAGtD,IAAA,WAAA,GAAA;;IAGA,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;AAC3B,QAAA,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;;AAGjC;;;;AAIG;AACH,IAAA,SAAS,CAAC,KAAiC,EAAA;QACzC,MAAM,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAChD,QAAA,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;;AAGhF;;;;;AAKG;AACH,IAAA,OAAO,CAAC,KAAiC,EAAA;QACvC,MAAM,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AAChD,QAAA,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC;AAE/E,QAAA,IAAI,eAAe,GAAG,aAAa,CAAC,WAAW,CAAC;;AAEhD,QAAA,eAAe,GAAG,MAAM,CACtB,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAC7B,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,CAC/C;QACD,OAAO,eAAe,CAAC,IAAI,CACzB,GAAG,CAAC,gBAAgB,IAAG;AACrB,YAAA,MAAM,QAAQ,GAAoB;AAChC,gBAAA,OAAO,EAAE,KAAK;AACd,gBAAA,WAAW,EAAE,EAAE;aAChB;YACD,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAC,OAAO,EAAE,KAAK,EAAC,KAAI;gBAC5C,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,OAAO,IAAI,OAAO;AAC9C,gBAAA,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,OAAO;AACvC,aAAC,CAAC;AACF,YAAA,OAAO,QAAQ;SAChB,CAAC,CACH;;;AAIK,IAAA,cAAc,CAAC,KAAa,EAAA;;QAElC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAE;;QAGlC,MAAM,GAAG,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,KAAK,CAAC;;QAGhD,MAAM,eAAe,GAAG,IAAI,UAAU,CAAC,CAAC,QAAuC,KAAI;;;;;;YAMjF,MAAM,OAAO,GAAG,CAAC,CAAsB,KAAW,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACxF,YAAA,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC;AAExB,YAAA,OAAO,MAAK;AACV,gBAAA,GAAG,CAAC,cAAc,CAAC,OAAO,CAAC;AAC7B,aAAC;AACH,SAAC,CAAC,CAAC,IAAI,CACL,SAAS,CAAC,GAAG,CAAC,EACd,GAAG,CAAC,CAAC,EAAC,OAAO,EAAC,MAAM,EAAC,KAAK,EAAE,OAAO,EAAC,CAAC,CAAC,EACtC,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAChC;;QAGD,MAAM,MAAM,GAAG,EAAC,UAAU,EAAE,eAAe,EAAE,GAAG,EAAC;QACjD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC;AAChC,QAAA,OAAO,MAAM;;uGA1FJ,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,cADN,MAAM,EAAA,CAAA;;2FAClB,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAD9B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AA+FhC;;;AAGG;AACH,SAAS,YAAY,CAAC,OAA0B,EAAA;AAC9C,IAAA,OAAO;SACJ,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;AAC7B,SAAA,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC;SAChC,GAAG,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;AAC/B;;;;"}