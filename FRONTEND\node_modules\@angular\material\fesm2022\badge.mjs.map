{"version": 3, "file": "badge.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/badge/badge.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/badge/badge-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {_IdGenerator, AriaDescriber, InteractivityChecker} from '@angular/cdk/a11y';\n\nimport {\n  booleanAttribute,\n  ChangeDetectionStrategy,\n  Component,\n  Directive,\n  ElementRef,\n  inject,\n  Input,\n  NgZone,\n  OnDestroy,\n  OnInit,\n  Renderer2,\n  ViewEncapsulation,\n  HOST_TAG_NAME,\n  DOCUMENT,\n} from '@angular/core';\nimport {_animationsDisabled, ThemePalette} from '../core';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '@angular/cdk/private';\n\n/** Allowed position options for matBadgePosition */\nexport type MatBadgePosition =\n  | 'above after'\n  | 'above before'\n  | 'below before'\n  | 'below after'\n  | 'before'\n  | 'after'\n  | 'above'\n  | 'below';\n\n/** Allowed size options for matBadgeSize */\nexport type MatBadgeSize = 'small' | 'medium' | 'large';\n\nconst BADGE_CONTENT_CLASS = 'mat-badge-content';\n\n/**\n * Component used to load the structural styles of the badge.\n * @docs-private\n */\n@Component({\n  styleUrl: 'badge.css',\n  encapsulation: ViewEncapsulation.None,\n  template: '',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class _MatBadgeStyleLoader {}\n\n/** Directive to display a text badge. */\n@Directive({\n  selector: '[matBadge]',\n  host: {\n    'class': 'mat-badge',\n    '[class.mat-badge-overlap]': 'overlap',\n    '[class.mat-badge-above]': 'isAbove()',\n    '[class.mat-badge-below]': '!isAbove()',\n    '[class.mat-badge-before]': '!isAfter()',\n    '[class.mat-badge-after]': 'isAfter()',\n    '[class.mat-badge-small]': 'size === \"small\"',\n    '[class.mat-badge-medium]': 'size === \"medium\"',\n    '[class.mat-badge-large]': 'size === \"large\"',\n    '[class.mat-badge-hidden]': 'hidden || !content',\n    '[class.mat-badge-disabled]': 'disabled',\n  },\n})\nexport class MatBadge implements OnInit, OnDestroy {\n  private _ngZone = inject(NgZone);\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _ariaDescriber = inject(AriaDescriber);\n  private _renderer = inject(Renderer2);\n  private _animationsDisabled = _animationsDisabled();\n  private _idGenerator = inject(_IdGenerator);\n\n  /**\n   * Theme color of the badge. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/badge/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input('matBadgeColor')\n  get color(): ThemePalette {\n    return this._color;\n  }\n  set color(value: ThemePalette) {\n    this._setColor(value);\n    this._color = value;\n  }\n  private _color: ThemePalette = 'primary';\n\n  /** Whether the badge should overlap its contents or not */\n  @Input({alias: 'matBadgeOverlap', transform: booleanAttribute}) overlap: boolean = true;\n\n  /** Whether the badge is disabled. */\n  @Input({alias: 'matBadgeDisabled', transform: booleanAttribute}) disabled: boolean;\n\n  /**\n   * Position the badge should reside.\n   * Accepts any combination of 'above'|'below' and 'before'|'after'\n   */\n  @Input('matBadgePosition') position: MatBadgePosition = 'above after';\n\n  /** The content for the badge */\n  @Input('matBadge')\n  get content(): string | number | undefined | null {\n    return this._content;\n  }\n  set content(newContent: string | number | undefined | null) {\n    this._updateRenderedContent(newContent);\n  }\n  private _content: string | number | undefined | null;\n\n  /** Message used to describe the decorated element via aria-describedby */\n  @Input('matBadgeDescription')\n  get description(): string {\n    return this._description;\n  }\n  set description(newDescription: string) {\n    this._updateDescription(newDescription);\n  }\n  private _description: string;\n\n  /** Size of the badge. Can be 'small', 'medium', or 'large'. */\n  @Input('matBadgeSize') size: MatBadgeSize = 'medium';\n\n  /** Whether the badge is hidden. */\n  @Input({alias: 'matBadgeHidden', transform: booleanAttribute}) hidden: boolean;\n\n  /** Visible badge element. */\n  private _badgeElement: HTMLElement | undefined;\n\n  /** Inline badge description. Used when the badge is applied to non-interactive host elements. */\n  private _inlineBadgeDescription: HTMLElement | undefined;\n\n  /** Whether the OnInit lifecycle hook has run yet */\n  private _isInitialized = false;\n\n  /** InteractivityChecker to determine if the badge host is focusable. */\n  private _interactivityChecker = inject(InteractivityChecker);\n\n  private _document = inject(DOCUMENT);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_MatBadgeStyleLoader);\n    styleLoader.load(_VisuallyHiddenLoader);\n\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const nativeElement = this._elementRef.nativeElement;\n\n      if (nativeElement.nodeType !== nativeElement.ELEMENT_NODE) {\n        throw Error('matBadge must be attached to an element node.');\n      }\n\n      const tagName = inject(HOST_TAG_NAME);\n\n      // Heads-up for developers to avoid putting matBadge on <mat-icon>\n      // as it is aria-hidden by default docs mention this at:\n      // https://material.angular.dev/components/badge/overview#accessibility\n      if (\n        tagName.toLowerCase() === 'mat-icon' &&\n        nativeElement.getAttribute('aria-hidden') === 'true'\n      ) {\n        console.warn(\n          `Detected a matBadge on an \"aria-hidden\" \"<mat-icon>\". ` +\n            `Consider setting aria-hidden=\"false\" in order to surface the information assistive technology.` +\n            `\\n${nativeElement.outerHTML}`,\n        );\n      }\n    }\n  }\n\n  /** Whether the badge is above the host or not */\n  isAbove(): boolean {\n    return this.position.indexOf('below') === -1;\n  }\n\n  /** Whether the badge is after the host or not */\n  isAfter(): boolean {\n    return this.position.indexOf('before') === -1;\n  }\n\n  /**\n   * Gets the element into which the badge's content is being rendered. Undefined if the element\n   * hasn't been created (e.g. if the badge doesn't have content).\n   */\n  getBadgeElement(): HTMLElement | undefined {\n    return this._badgeElement;\n  }\n\n  ngOnInit() {\n    // We may have server-side rendered badge that we need to clear.\n    // We need to do this in ngOnInit because the full content of the component\n    // on which the badge is attached won't necessarily be in the DOM until this point.\n    this._clearExistingBadges();\n\n    if (this.content && !this._badgeElement) {\n      this._badgeElement = this._createBadgeElement();\n      this._updateRenderedContent(this.content);\n    }\n\n    this._isInitialized = true;\n  }\n\n  ngOnDestroy() {\n    // ViewEngine only: when creating a badge through the Renderer, Angular remembers its index.\n    // We have to destroy it ourselves, otherwise it'll be retained in memory.\n    if (this._renderer.destroyNode) {\n      this._renderer.destroyNode(this._badgeElement);\n      this._inlineBadgeDescription?.remove();\n    }\n\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n  }\n\n  /** Gets whether the badge's host element is interactive. */\n  private _isHostInteractive(): boolean {\n    // Ignore visibility since it requires an expensive style caluclation.\n    return this._interactivityChecker.isFocusable(this._elementRef.nativeElement, {\n      ignoreVisibility: true,\n    });\n  }\n\n  /** Creates the badge element */\n  private _createBadgeElement(): HTMLElement {\n    const badgeElement = this._renderer.createElement('span');\n    const activeClass = 'mat-badge-active';\n\n    badgeElement.setAttribute('id', this._idGenerator.getId('mat-badge-content-'));\n\n    // The badge is aria-hidden because we don't want it to appear in the page's navigation\n    // flow. Instead, we use the badge to describe the decorated element with aria-describedby.\n    badgeElement.setAttribute('aria-hidden', 'true');\n    badgeElement.classList.add(BADGE_CONTENT_CLASS);\n\n    if (this._animationsDisabled) {\n      badgeElement.classList.add('_mat-animation-noopable');\n    }\n\n    this._elementRef.nativeElement.appendChild(badgeElement);\n\n    // animate in after insertion\n    if (typeof requestAnimationFrame === 'function' && !this._animationsDisabled) {\n      this._ngZone.runOutsideAngular(() => {\n        requestAnimationFrame(() => {\n          badgeElement.classList.add(activeClass);\n        });\n      });\n    } else {\n      badgeElement.classList.add(activeClass);\n    }\n\n    return badgeElement;\n  }\n\n  /** Update the text content of the badge element in the DOM, creating the element if necessary. */\n  private _updateRenderedContent(newContent: string | number | undefined | null): void {\n    const newContentNormalized: string = `${newContent ?? ''}`.trim();\n\n    // Don't create the badge element if the directive isn't initialized because we want to\n    // append the badge element to the *end* of the host element's content for backwards\n    // compatibility.\n    if (this._isInitialized && newContentNormalized && !this._badgeElement) {\n      this._badgeElement = this._createBadgeElement();\n    }\n\n    if (this._badgeElement) {\n      this._badgeElement.textContent = newContentNormalized;\n    }\n\n    this._content = newContentNormalized;\n  }\n\n  /** Updates the host element's aria description via AriaDescriber. */\n  private _updateDescription(newDescription: string): void {\n    // Always start by removing the aria-describedby; we will add a new one if necessary.\n    this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n\n    // NOTE: We only check whether the host is interactive here, which happens during\n    // when then badge content changes. It is possible that the host changes\n    // interactivity status separate from one of these. However, watching the interactivity\n    // status of the host would require a `MutationObserver`, which is likely more code + overhead\n    // than it's worth; from usages inside Google, we see that the vats majority of badges either\n    // never change interactivity, or also set `matBadgeHidden` based on the same condition.\n\n    if (!newDescription || this._isHostInteractive()) {\n      this._removeInlineDescription();\n    }\n\n    this._description = newDescription;\n\n    // We don't add `aria-describedby` for non-interactive hosts elements because we\n    // instead insert the description inline.\n    if (this._isHostInteractive()) {\n      this._ariaDescriber.describe(this._elementRef.nativeElement, newDescription);\n    } else {\n      this._updateInlineDescription();\n    }\n  }\n\n  private _updateInlineDescription() {\n    // Create the inline description element if it doesn't exist\n    if (!this._inlineBadgeDescription) {\n      this._inlineBadgeDescription = this._document.createElement('span');\n      this._inlineBadgeDescription.classList.add('cdk-visually-hidden');\n    }\n\n    this._inlineBadgeDescription.textContent = this.description;\n    this._badgeElement?.appendChild(this._inlineBadgeDescription);\n  }\n\n  private _removeInlineDescription() {\n    this._inlineBadgeDescription?.remove();\n    this._inlineBadgeDescription = undefined;\n  }\n\n  /** Adds css theme class given the color to the component host */\n  private _setColor(colorPalette: ThemePalette) {\n    const classList = this._elementRef.nativeElement.classList;\n    classList.remove(`mat-badge-${this._color}`);\n    if (colorPalette) {\n      classList.add(`mat-badge-${colorPalette}`);\n    }\n  }\n\n  /** Clears any existing badges that might be left over from server-side rendering. */\n  private _clearExistingBadges() {\n    // Only check direct children of this host element in order to avoid deleting\n    // any badges that might exist in descendant elements.\n    const badges = this._elementRef.nativeElement.querySelectorAll(\n      `:scope > .${BADGE_CONTENT_CLASS}`,\n    );\n    for (const badgeElement of Array.from(badges)) {\n      if (badgeElement !== this._badgeElement) {\n        badgeElement.remove();\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {A11yModule} from '@angular/cdk/a11y';\nimport {MatBadge, _MatBadgeStyleLoader} from './badge';\n\n@NgModule({\n  // Note: we _shouldn't_ have to import `_MatBadgeStyleLoader`,\n  // but it seems to be necessary for tests.\n  imports: [A11yModule, MatCommonModule, MatBadge, _MatBadgeStyleLoader],\n  exports: [MatBadge, MatCommonModule],\n})\nexport class MatBadgeModule {}\n"], "names": [], "mappings": ";;;;;;;;;AA2CA,MAAM,mBAAmB,GAAG,mBAAmB;AAE/C;;;AAGG;MAOU,oBAAoB,CAAA;uGAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAApB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,oBAAoB,wEAHrB,EAAE,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,kyGAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAGD,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBANhC,SAAS;AAEO,YAAA,IAAA,EAAA,CAAA,EAAA,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,QAAA,EAC3B,EAAE,EACK,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,MAAA,EAAA,CAAA,kyGAAA,CAAA,EAAA;;AAIjD;MAiBa,QAAQ,CAAA;AACX,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC;AACtC,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IAC7B,mBAAmB,GAAG,mBAAmB,EAAE;AAC3C,IAAA,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AAE3C;;;;;;AAMG;AACH,IAAA,IACI,KAAK,GAAA;QACP,OAAO,IAAI,CAAC,MAAM;;IAEpB,IAAI,KAAK,CAAC,KAAmB,EAAA;AAC3B,QAAA,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;AACrB,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;IAEb,MAAM,GAAiB,SAAS;;IAGwB,OAAO,GAAY,IAAI;;AAGtB,IAAA,QAAQ;AAEzE;;;AAGG;IACwB,QAAQ,GAAqB,aAAa;;AAGrE,IAAA,IACI,OAAO,GAAA;QACT,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAI,OAAO,CAAC,UAA8C,EAAA;AACxD,QAAA,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC;;AAEjC,IAAA,QAAQ;;AAGhB,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY;;IAE1B,IAAI,WAAW,CAAC,cAAsB,EAAA;AACpC,QAAA,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC;;AAEjC,IAAA,YAAY;;IAGG,IAAI,GAAiB,QAAQ;;AAGW,IAAA,MAAM;;AAG7D,IAAA,aAAa;;AAGb,IAAA,uBAAuB;;IAGvB,cAAc,GAAG,KAAK;;AAGtB,IAAA,qBAAqB,GAAG,MAAM,CAAC,oBAAoB,CAAC;AAEpD,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAIpC,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,WAAW,GAAG,MAAM,CAAC,sBAAsB,CAAC;AAClD,QAAA,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC;AACtC,QAAA,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC;AAEvC,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,MAAM,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;YAEpD,IAAI,aAAa,CAAC,QAAQ,KAAK,aAAa,CAAC,YAAY,EAAE;AACzD,gBAAA,MAAM,KAAK,CAAC,+CAA+C,CAAC;;AAG9D,YAAA,MAAM,OAAO,GAAG,MAAM,CAAC,aAAa,CAAC;;;;AAKrC,YAAA,IACE,OAAO,CAAC,WAAW,EAAE,KAAK,UAAU;gBACpC,aAAa,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,EACpD;gBACA,OAAO,CAAC,IAAI,CACV,CAAwD,sDAAA,CAAA;oBACtD,CAAgG,8FAAA,CAAA;AAChG,oBAAA,CAAA,EAAA,EAAK,aAAa,CAAC,SAAS,CAAA,CAAE,CACjC;;;;;IAMP,OAAO,GAAA;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;;;IAI9C,OAAO,GAAA;QACL,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;;AAG/C;;;AAGG;IACH,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,aAAa;;IAG3B,QAAQ,GAAA;;;;QAIN,IAAI,CAAC,oBAAoB,EAAE;QAE3B,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACvC,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE;AAC/C,YAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,OAAO,CAAC;;AAG3C,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;;IAG5B,WAAW,GAAA;;;AAGT,QAAA,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;YAC9B,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC;AAC9C,YAAA,IAAI,CAAC,uBAAuB,EAAE,MAAM,EAAE;;AAGxC,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC;;;IAIjF,kBAAkB,GAAA;;QAExB,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE;AAC5E,YAAA,gBAAgB,EAAE,IAAI;AACvB,SAAA,CAAC;;;IAII,mBAAmB,GAAA;QACzB,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC;QACzD,MAAM,WAAW,GAAG,kBAAkB;AAEtC,QAAA,YAAY,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;;;AAI9E,QAAA,YAAY,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;AAChD,QAAA,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,mBAAmB,CAAC;AAE/C,QAAA,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAC5B,YAAA,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,yBAAyB,CAAC;;QAGvD,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,YAAY,CAAC;;QAGxD,IAAI,OAAO,qBAAqB,KAAK,UAAU,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AAC5E,YAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;gBAClC,qBAAqB,CAAC,MAAK;AACzB,oBAAA,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC;AACzC,iBAAC,CAAC;AACJ,aAAC,CAAC;;aACG;AACL,YAAA,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC;;AAGzC,QAAA,OAAO,YAAY;;;AAIb,IAAA,sBAAsB,CAAC,UAA8C,EAAA;QAC3E,MAAM,oBAAoB,GAAW,CAAA,EAAG,UAAU,IAAI,EAAE,CAAE,CAAA,CAAC,IAAI,EAAE;;;;QAKjE,IAAI,IAAI,CAAC,cAAc,IAAI,oBAAoB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;AACtE,YAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE;;AAGjD,QAAA,IAAI,IAAI,CAAC,aAAa,EAAE;AACtB,YAAA,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,oBAAoB;;AAGvD,QAAA,IAAI,CAAC,QAAQ,GAAG,oBAAoB;;;AAI9B,IAAA,kBAAkB,CAAC,cAAsB,EAAA;;AAE/C,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,IAAI,CAAC,WAAW,CAAC;;;;;;;QASvF,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;YAChD,IAAI,CAAC,wBAAwB,EAAE;;AAGjC,QAAA,IAAI,CAAC,YAAY,GAAG,cAAc;;;AAIlC,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE,EAAE;AAC7B,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,cAAc,CAAC;;aACvE;YACL,IAAI,CAAC,wBAAwB,EAAE;;;IAI3B,wBAAwB,GAAA;;AAE9B,QAAA,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,MAAM,CAAC;YACnE,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC;;QAGnE,IAAI,CAAC,uBAAuB,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW;QAC3D,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,IAAI,CAAC,uBAAuB,CAAC;;IAGvD,wBAAwB,GAAA;AAC9B,QAAA,IAAI,CAAC,uBAAuB,EAAE,MAAM,EAAE;AACtC,QAAA,IAAI,CAAC,uBAAuB,GAAG,SAAS;;;AAIlC,IAAA,SAAS,CAAC,YAA0B,EAAA;QAC1C,MAAM,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,SAAS;QAC1D,SAAS,CAAC,MAAM,CAAC,CAAA,UAAA,EAAa,IAAI,CAAC,MAAM,CAAE,CAAA,CAAC;QAC5C,IAAI,YAAY,EAAE;AAChB,YAAA,SAAS,CAAC,GAAG,CAAC,aAAa,YAAY,CAAA,CAAE,CAAC;;;;IAKtC,oBAAoB,GAAA;;;AAG1B,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,gBAAgB,CAC5D,CAAA,UAAA,EAAa,mBAAmB,CAAA,CAAE,CACnC;QACD,KAAK,MAAM,YAAY,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;AAC7C,YAAA,IAAI,YAAY,KAAK,IAAI,CAAC,aAAa,EAAE;gBACvC,YAAY,CAAC,MAAM,EAAE;;;;uGA/QhB,QAAQ,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAR,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,QAAQ,EA0B0B,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,CAAA,eAAA,EAAA,OAAA,CAAA,EAAA,OAAA,EAAA,CAAA,iBAAA,EAAA,SAAA,EAAA,gBAAgB,CAGf,EAAA,QAAA,EAAA,CAAA,kBAAA,EAAA,UAAA,EAAA,gBAAgB,2MAgClB,gBAAgB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,yBAAA,EAAA,SAAA,EAAA,uBAAA,EAAA,WAAA,EAAA,uBAAA,EAAA,YAAA,EAAA,wBAAA,EAAA,YAAA,EAAA,uBAAA,EAAA,WAAA,EAAA,uBAAA,EAAA,oBAAA,EAAA,wBAAA,EAAA,qBAAA,EAAA,uBAAA,EAAA,oBAAA,EAAA,wBAAA,EAAA,oBAAA,EAAA,0BAAA,EAAA,UAAA,EAAA,EAAA,cAAA,EAAA,WAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FA7DjD,QAAQ,EAAA,UAAA,EAAA,CAAA;kBAhBpB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,YAAY;AACtB,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,WAAW;AACpB,wBAAA,2BAA2B,EAAE,SAAS;AACtC,wBAAA,yBAAyB,EAAE,WAAW;AACtC,wBAAA,yBAAyB,EAAE,YAAY;AACvC,wBAAA,0BAA0B,EAAE,YAAY;AACxC,wBAAA,yBAAyB,EAAE,WAAW;AACtC,wBAAA,yBAAyB,EAAE,kBAAkB;AAC7C,wBAAA,0BAA0B,EAAE,mBAAmB;AAC/C,wBAAA,yBAAyB,EAAE,kBAAkB;AAC7C,wBAAA,0BAA0B,EAAE,oBAAoB;AAChD,wBAAA,4BAA4B,EAAE,UAAU;AACzC,qBAAA;AACF,iBAAA;wDAiBK,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,eAAe;gBAW0C,OAAO,EAAA,CAAA;sBAAtE,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,iBAAiB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAGG,QAAQ,EAAA,CAAA;sBAAxE,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,kBAAkB,EAAE,SAAS,EAAE,gBAAgB,EAAC;gBAMpC,QAAQ,EAAA,CAAA;sBAAlC,KAAK;uBAAC,kBAAkB;gBAIrB,OAAO,EAAA,CAAA;sBADV,KAAK;uBAAC,UAAU;gBAWb,WAAW,EAAA,CAAA;sBADd,KAAK;uBAAC,qBAAqB;gBAUL,IAAI,EAAA,CAAA;sBAA1B,KAAK;uBAAC,cAAc;gBAG0C,MAAM,EAAA,CAAA;sBAApE,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,gBAAgB,EAAE,SAAS,EAAE,gBAAgB,EAAC;;;MCpHlD,cAAc,CAAA;uGAAd,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAd,cAAc,EAAA,OAAA,EAAA,CAHf,UAAU,EAAE,eAAe,EAAE,QAAQ,EAAE,oBAAoB,CAAA,EAAA,OAAA,EAAA,CAC3D,QAAQ,EAAE,eAAe,CAAA,EAAA,CAAA;AAExB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,cAAc,EAHf,OAAA,EAAA,CAAA,UAAU,EAAE,eAAe,EACjB,eAAe,CAAA,EAAA,CAAA;;2FAExB,cAAc,EAAA,UAAA,EAAA,CAAA;kBAN1B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;;;oBAGR,OAAO,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,QAAQ,EAAE,oBAAoB,CAAC;AACtE,oBAAA,OAAO,EAAE,CAAC,QAAQ,EAAE,eAAe,CAAC;AACrC,iBAAA;;;;;"}