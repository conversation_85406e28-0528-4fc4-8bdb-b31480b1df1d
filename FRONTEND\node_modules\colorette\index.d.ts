declare module "colorette" {
  type Color = (text: string | number) => string

  interface Colorette {
    reset: Color
    bold: Color
    dim: Color
    italic: Color
    underline: Color
    inverse: Color
    hidden: Color
    strikethrough: Color
    black: Color
    red: Color
    green: Color
    yellow: Color
    blue: Color
    magenta: Color
    cyan: Color
    white: Color
    gray: Color
    bgBlack: Color
    bgRed: Color
    bgGreen: Color
    bgYellow: Color
    bgBlue: Color
    bgMagenta: Color
    bgCyan: Color
    bgWhite: Color
    blackBright: Color
    redBright: Color
    greenBright: Color
    yellowBright: Color
    blueBright: Color
    magentaBright: Color
    cyanBright: Color
    whiteBright: Color
    bgBlackBright: Color
    bgRedBright: Color
    bgGreenBright: Color
    bgYellowBright: Color
    bgBlueBright: Color
    bgMagentaBright: Color
    bgCyanBright: Color
    bgWhiteBright: Color
  }

  const reset: Color
  const bold: Color
  const dim: Color
  const italic: Color
  const underline: Color
  const inverse: Color
  const hidden: Color
  const strikethrough: Color
  const black: Color
  const red: Color
  const green: Color
  const yellow: Color
  const blue: Color
  const magenta: Color
  const cyan: Color
  const white: Color
  const gray: Color
  const bgBlack: Color
  const bgRed: Color
  const bgGreen: Color
  const bgYellow: Color
  const bgBlue: Color
  const bgMagenta: Color
  const bgCyan: Color
  const bgWhite: Color
  const blackBright: Color
  const redBright: Color
  const greenBright: Color
  const yellowBright: Color
  const blueBright: Color
  const magentaBright: Color
  const cyanBright: Color
  const whiteBright: Color
  const bgBlackBright: Color
  const bgRedBright: Color
  const bgGreenBright: Color
  const bgYellowBright: Color
  const bgBlueBright: Color
  const bgMagentaBright: Color
  const bgCyanBright: Color
  const bgWhiteBright: Color

  const isColorSupported: boolean

  function createColors(options?: { useColor: boolean }): Colorette
}
