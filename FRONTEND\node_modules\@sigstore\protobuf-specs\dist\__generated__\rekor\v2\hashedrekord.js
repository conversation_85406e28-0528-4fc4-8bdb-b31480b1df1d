"use strict";
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v6.30.2
// source: rekor/v2/hashedrekord.proto
Object.defineProperty(exports, "__esModule", { value: true });
exports.HashedRekordLogEntryV002 = exports.HashedRekordRequestV002 = void 0;
/* eslint-disable */
const sigstore_common_1 = require("../../sigstore_common");
const verifier_1 = require("./verifier");
exports.HashedRekordRequestV002 = {
    fromJSON(object) {
        return {
            digest: isSet(object.digest) ? Buffer.from(bytesFromBase64(object.digest)) : Buffer.alloc(0),
            signature: isSet(object.signature) ? verifier_1.Signature.fromJSON(object.signature) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.digest.length !== 0) {
            obj.digest = base64FromBytes(message.digest);
        }
        if (message.signature !== undefined) {
            obj.signature = verifier_1.Signature.toJSON(message.signature);
        }
        return obj;
    },
};
exports.HashedRekordLogEntryV002 = {
    fromJSON(object) {
        return {
            data: isSet(object.data) ? sigstore_common_1.HashOutput.fromJSON(object.data) : undefined,
            signature: isSet(object.signature) ? verifier_1.Signature.fromJSON(object.signature) : undefined,
        };
    },
    toJSON(message) {
        const obj = {};
        if (message.data !== undefined) {
            obj.data = sigstore_common_1.HashOutput.toJSON(message.data);
        }
        if (message.signature !== undefined) {
            obj.signature = verifier_1.Signature.toJSON(message.signature);
        }
        return obj;
    },
};
function bytesFromBase64(b64) {
    return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
}
function base64FromBytes(arr) {
    return globalThis.Buffer.from(arr).toString("base64");
}
function isSet(value) {
    return value !== null && value !== undefined;
}
