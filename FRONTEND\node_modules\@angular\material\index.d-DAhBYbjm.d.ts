import * as i0 from '@angular/core';
import { M as MatRippleModule } from './index.d-C5neTPvr.js';
import { M as MatCommonModule } from './common-module.d-C8xzHJDr.js';
import { M as MatPseudoCheckboxModule } from './pseudo-checkbox-module.d-BHmTZ10P.js';
import { M as MatOption, a as MatOptgroup } from './option.d-BcvS44bt.js';

declare class MatOptionModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MatOptionModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MatOptionModule, never, [typeof MatRippleModule, typeof MatCommonModule, typeof MatPseudoCheckboxModule, typeof MatOption, typeof MatOptgroup], [typeof MatOption, typeof MatOptgroup]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MatOptionModule>;
}

export { MatOptionModule as M };
