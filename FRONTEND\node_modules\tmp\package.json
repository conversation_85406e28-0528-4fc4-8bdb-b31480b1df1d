{"name": "tmp", "version": "0.0.33", "description": "Temporary file and directory creator", "author": "KARASZI István <<EMAIL>> (http://raszi.hu/)", "keywords": ["temporary", "tmp", "temp", "tempdir", "tempfile", "tmpdir", "tmpfile"], "license": "MIT", "repository": "raszi/node-tmp", "homepage": "http://github.com/raszi/node-tmp", "bugs": {"url": "http://github.com/raszi/node-tmp/issues"}, "engines": {"node": ">=0.6.0"}, "dependencies": {"os-tmpdir": "~1.0.2"}, "devDependencies": {"vows": "~0.7.0"}, "main": "lib/tmp.js", "files": ["lib/"], "scripts": {"test": "vows test/*-test.js", "doc": "jsdoc -c .jsdoc.json"}}