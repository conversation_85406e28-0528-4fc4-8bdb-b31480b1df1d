@use './theming/inspection';
@use './ripple/ripple-theme';
@use './option/option-theme';
@use './option/optgroup-theme';
@use './selection/pseudo-checkbox/pseudo-checkbox-theme';
@use './typography/typography';
@use './tokens/token-utils';
@use './m2-app';
@use './m3-app';
@use 'ripple/m3-ripple';
@use 'option/m3-option';
@use 'option/m3-optgroup';
@use 'selection/pseudo-checkbox/m3-pseudo-checkbox';
@use 'sass:map';

@mixin base($theme) {
  $tokens: map.get(m2-app.get-tokens($theme), base);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-app.get-tokens($theme), base);
  }

  @include token-utils.values($tokens);

  @include ripple-theme.base($theme);
  @include option-theme.base($theme);
  @include optgroup-theme.base($theme);
  @include pseudo-checkbox-theme.base($theme);
}

@mixin color($theme) {
  $tokens: map.get(m2-app.get-tokens($theme), color);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-app.get-tokens($theme), color);
  }

  @include token-utils.values($tokens);

  @include ripple-theme.color($theme);
  @include option-theme.color($theme);
  @include optgroup-theme.color($theme);
  @include pseudo-checkbox-theme.color($theme);
}

@mixin typography($theme) {
  $tokens: map.get(m2-app.get-tokens($theme), typography);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-app.get-tokens($theme), typography);
  }

  @include token-utils.values($tokens);

  @include ripple-theme.typography($theme);
  @include option-theme.typography($theme);
  @include optgroup-theme.typography($theme);
  @include pseudo-checkbox-theme.typography($theme);
}

@mixin density($theme) {
  $tokens: map.get(m2-app.get-tokens($theme), density);
  @if inspection.get-theme-version($theme) == 1 {
    $tokens: map.get(m3-app.get-tokens($theme), density);
  }

  @include token-utils.values($tokens);

  @include ripple-theme.density($theme);
  @include option-theme.density($theme);
  @include optgroup-theme.density($theme);
  @include pseudo-checkbox-theme.density($theme);
}

@function _define-overrides() {
  @return (
    (
      namespace: app,
      tokens: token-utils.get-overrides(m3-app.get-tokens(), app),
      prefix: 'app-'
    ),
    (
      namespace: ripple,
      tokens: token-utils.get-overrides(m3-ripple.get-tokens(), ripple),
      prefix: 'ripple-'
    ),
    (
      namespace: option,
      tokens: token-utils.get-overrides(m3-option.get-tokens(), option),
      prefix: 'option-'
    ),
    (
      namespace: optgroup,
      tokens: token-utils.get-overrides(m3-optgroup.get-tokens(), optgroup),
      prefix: 'optgroup-'
    ),
    (
      namespace: pseudo-checkbox,
      tokens: token-utils.get-overrides(m3-pseudo-checkbox.get-tokens(), pseudo-checkbox),
      prefix: 'pseudo-checkbox-'
    ),
  );
}

@mixin overrides($tokens: ()) {
  @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

// Mixin that renders all of the core styles that depend on the theme.
@mixin theme($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include base($theme);
    @include color($theme);
    @include density($theme);
    @include typography($theme);
  } @else {
    @include base($theme);
    @if inspection.theme-has($theme, color) {
      @include color($theme);
    }
    @if inspection.theme-has($theme, density) {
      @include density($theme);
    }
    @if inspection.theme-has($theme, typography) {
      @include typography($theme);
    }
  }
}
