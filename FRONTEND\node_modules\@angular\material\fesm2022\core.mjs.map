{"version": 3, "file": "core.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/version.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/datetime/native-date-adapter.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/datetime/native-date-formats.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/datetime/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Version} from '@angular/core';\n\n/** Current version of Angular Material. */\nexport const VERSION = new Version('20.1.0');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {inject, Injectable} from '@angular/core';\nimport {DateAdapter, MAT_DATE_LOCALE} from './date-adapter';\n\n/**\n * Matches strings that have the form of a valid RFC 3339 string\n * (https://tools.ietf.org/html/rfc3339). Note that the string may not actually be a valid date\n * because the regex will match strings with an out of bounds month, date, etc.\n */\nconst ISO_8601_REGEX =\n  /^\\d{4}-\\d{2}-\\d{2}(?:T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d+)?(?:Z|(?:(?:\\+|-)\\d{2}:\\d{2}))?)?$/;\n\n/**\n * Matches a time string. Supported formats:\n * - {{hours}}:{{minutes}}\n * - {{hours}}:{{minutes}}:{{seconds}}\n * - {{hours}}:{{minutes}} AM/PM\n * - {{hours}}:{{minutes}}:{{seconds}} AM/PM\n * - {{hours}}.{{minutes}}\n * - {{hours}}.{{minutes}}.{{seconds}}\n * - {{hours}}.{{minutes}} AM/PM\n * - {{hours}}.{{minutes}}.{{seconds}} AM/PM\n */\nconst TIME_REGEX = /^(\\d?\\d)[:.](\\d?\\d)(?:[:.](\\d?\\d))?\\s*(AM|PM)?$/i;\n\n/** Creates an array and fills it with values. */\nfunction range<T>(length: number, valueFunction: (index: number) => T): T[] {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n\n/** Adapts the native JS Date for use with cdk-based components that work with dates. */\n@Injectable()\nexport class NativeDateAdapter extends DateAdapter<Date> {\n  /**\n   * @deprecated No longer being used. To be removed.\n   * @breaking-change 14.0.0\n   */\n  useUtcForDisplay: boolean = false;\n\n  /** The injected locale. */\n  private readonly _matDateLocale = inject(MAT_DATE_LOCALE, {optional: true});\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n\n    const matDateLocale = inject(MAT_DATE_LOCALE, {optional: true});\n\n    if (matDateLocale !== undefined) {\n      this._matDateLocale = matDateLocale;\n    }\n\n    super.setLocale(this._matDateLocale);\n  }\n\n  getYear(date: Date): number {\n    return date.getFullYear();\n  }\n\n  getMonth(date: Date): number {\n    return date.getMonth();\n  }\n\n  getDate(date: Date): number {\n    return date.getDate();\n  }\n\n  getDayOfWeek(date: Date): number {\n    return date.getDay();\n  }\n\n  getMonthNames(style: 'long' | 'short' | 'narrow'): string[] {\n    const dtf = new Intl.DateTimeFormat(this.locale, {month: style, timeZone: 'utc'});\n    return range(12, i => this._format(dtf, new Date(2017, i, 1)));\n  }\n\n  getDateNames(): string[] {\n    const dtf = new Intl.DateTimeFormat(this.locale, {day: 'numeric', timeZone: 'utc'});\n    return range(31, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n\n  getDayOfWeekNames(style: 'long' | 'short' | 'narrow'): string[] {\n    const dtf = new Intl.DateTimeFormat(this.locale, {weekday: style, timeZone: 'utc'});\n    return range(7, i => this._format(dtf, new Date(2017, 0, i + 1)));\n  }\n\n  getYearName(date: Date): string {\n    const dtf = new Intl.DateTimeFormat(this.locale, {year: 'numeric', timeZone: 'utc'});\n    return this._format(dtf, date);\n  }\n\n  getFirstDayOfWeek(): number {\n    // At the time of writing `Intl.Locale` isn't available\n    // in the internal types so we need to cast to `any`.\n    if (typeof Intl !== 'undefined' && (Intl as any).Locale) {\n      const locale = new (Intl as any).Locale(this.locale) as {\n        getWeekInfo?: () => {firstDay: number};\n        weekInfo?: {firstDay: number};\n      };\n\n      // Some browsers implement a `getWeekInfo` method while others have a `weekInfo` getter.\n      // Note that this isn't supported in all browsers so we need to null check it.\n      const firstDay = (locale.getWeekInfo?.() || locale.weekInfo)?.firstDay ?? 0;\n\n      // `weekInfo.firstDay` is a number between 1 and 7 where, starting from Monday,\n      // whereas our representation is 0 to 6 where 0 is Sunday so we need to normalize it.\n      return firstDay === 7 ? 0 : firstDay;\n    }\n\n    // Default to Sunday if the browser doesn't provide the week information.\n    return 0;\n  }\n\n  getNumDaysInMonth(date: Date): number {\n    return this.getDate(\n      this._createDateWithOverflow(this.getYear(date), this.getMonth(date) + 1, 0),\n    );\n  }\n\n  clone(date: Date): Date {\n    return new Date(date.getTime());\n  }\n\n  createDate(year: number, month: number, date: number): Date {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      // Check for invalid month and date (except upper bound on date which we have to check after\n      // creating the Date).\n      if (month < 0 || month > 11) {\n        throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n      }\n\n      if (date < 1) {\n        throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n      }\n    }\n\n    let result = this._createDateWithOverflow(year, month, date);\n    // Check that the date wasn't above the upper bound for the month, causing the month to overflow\n    if (result.getMonth() != month && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid date \"${date}\" for month with index \"${month}\".`);\n    }\n\n    return result;\n  }\n\n  today(): Date {\n    return new Date();\n  }\n\n  parse(value: any, parseFormat?: any): Date | null {\n    // We have no way using the native JS Date to set the parse format or locale, so we ignore these\n    // parameters.\n    if (typeof value == 'number') {\n      return new Date(value);\n    }\n    return value ? new Date(Date.parse(value)) : null;\n  }\n\n  format(date: Date, displayFormat: Object): string {\n    if (!this.isValid(date)) {\n      throw Error('NativeDateAdapter: Cannot format invalid date.');\n    }\n\n    const dtf = new Intl.DateTimeFormat(this.locale, {...displayFormat, timeZone: 'utc'});\n    return this._format(dtf, date);\n  }\n\n  addCalendarYears(date: Date, years: number): Date {\n    return this.addCalendarMonths(date, years * 12);\n  }\n\n  addCalendarMonths(date: Date, months: number): Date {\n    let newDate = this._createDateWithOverflow(\n      this.getYear(date),\n      this.getMonth(date) + months,\n      this.getDate(date),\n    );\n\n    // It's possible to wind up in the wrong month if the original month has more days than the new\n    // month. In this case we want to go to the last day of the desired month.\n    // Note: the additional + 12 % 12 ensures we end up with a positive number, since JS % doesn't\n    // guarantee this.\n    if (this.getMonth(newDate) != (((this.getMonth(date) + months) % 12) + 12) % 12) {\n      newDate = this._createDateWithOverflow(this.getYear(newDate), this.getMonth(newDate), 0);\n    }\n\n    return newDate;\n  }\n\n  addCalendarDays(date: Date, days: number): Date {\n    return this._createDateWithOverflow(\n      this.getYear(date),\n      this.getMonth(date),\n      this.getDate(date) + days,\n    );\n  }\n\n  toIso8601(date: Date): string {\n    return [\n      date.getUTCFullYear(),\n      this._2digit(date.getUTCMonth() + 1),\n      this._2digit(date.getUTCDate()),\n    ].join('-');\n  }\n\n  /**\n   * Returns the given value if given a valid Date or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) into valid Dates and empty string into null. Returns an\n   * invalid date for all other values.\n   */\n  override deserialize(value: any): Date | null {\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      }\n      // The `Date` constructor accepts formats other than ISO 8601, so we need to make sure the\n      // string is the right format first.\n      if (ISO_8601_REGEX.test(value)) {\n        let date = new Date(value);\n        if (this.isValid(date)) {\n          return date;\n        }\n      }\n    }\n    return super.deserialize(value);\n  }\n\n  isDateInstance(obj: any) {\n    return obj instanceof Date;\n  }\n\n  isValid(date: Date) {\n    return !isNaN(date.getTime());\n  }\n\n  invalid(): Date {\n    return new Date(NaN);\n  }\n\n  override setTime(target: Date, hours: number, minutes: number, seconds: number): Date {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      if (!inRange(hours, 0, 23)) {\n        throw Error(`Invalid hours \"${hours}\". Hours value must be between 0 and 23.`);\n      }\n\n      if (!inRange(minutes, 0, 59)) {\n        throw Error(`Invalid minutes \"${minutes}\". Minutes value must be between 0 and 59.`);\n      }\n\n      if (!inRange(seconds, 0, 59)) {\n        throw Error(`Invalid seconds \"${seconds}\". Seconds value must be between 0 and 59.`);\n      }\n    }\n\n    const clone = this.clone(target);\n    clone.setHours(hours, minutes, seconds, 0);\n    return clone;\n  }\n\n  override getHours(date: Date): number {\n    return date.getHours();\n  }\n\n  override getMinutes(date: Date): number {\n    return date.getMinutes();\n  }\n\n  override getSeconds(date: Date): number {\n    return date.getSeconds();\n  }\n\n  override parseTime(userValue: any, parseFormat?: any): Date | null {\n    if (typeof userValue !== 'string') {\n      return userValue instanceof Date ? new Date(userValue.getTime()) : null;\n    }\n\n    const value = userValue.trim();\n\n    if (value.length === 0) {\n      return null;\n    }\n\n    // Attempt to parse the value directly.\n    let result = this._parseTimeString(value);\n\n    // Some locales add extra characters around the time, but are otherwise parseable\n    // (e.g. `00:05 ч.` in bg-BG). Try replacing all non-number and non-colon characters.\n    if (result === null) {\n      const withoutExtras = value.replace(/[^0-9:(AM|PM)]/gi, '').trim();\n\n      if (withoutExtras.length > 0) {\n        result = this._parseTimeString(withoutExtras);\n      }\n    }\n\n    return result || this.invalid();\n  }\n\n  override addSeconds(date: Date, amount: number): Date {\n    return new Date(date.getTime() + amount * 1000);\n  }\n\n  /** Creates a date but allows the month and date to overflow. */\n  private _createDateWithOverflow(year: number, month: number, date: number) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setFullYear` and `setHours` instead.\n    const d = new Date();\n    d.setFullYear(year, month, date);\n    d.setHours(0, 0, 0, 0);\n    return d;\n  }\n\n  /**\n   * Pads a number to make it two digits.\n   * @param n The number to pad.\n   * @returns The padded number.\n   */\n  private _2digit(n: number) {\n    return ('00' + n).slice(-2);\n  }\n\n  /**\n   * When converting Date object to string, javascript built-in functions may return wrong\n   * results because it applies its internal DST rules. The DST rules around the world change\n   * very frequently, and the current valid rule is not always valid in previous years though.\n   * We work around this problem building a new Date object which has its internal UTC\n   * representation with the local date and time.\n   * @param dtf Intl.DateTimeFormat object, containing the desired string format. It must have\n   *    timeZone set to 'utc' to work fine.\n   * @param date Date from which we want to get the string representation according to dtf\n   * @returns A Date object with its UTC representation based on the passed in date info\n   */\n  private _format(dtf: Intl.DateTimeFormat, date: Date) {\n    // Passing the year to the constructor causes year numbers <100 to be converted to 19xx.\n    // To work around this we use `setUTCFullYear` and `setUTCHours` instead.\n    const d = new Date();\n    d.setUTCFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n    d.setUTCHours(date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());\n    return dtf.format(d);\n  }\n\n  /**\n   * Attempts to parse a time string into a date object. Returns null if it cannot be parsed.\n   * @param value Time string to parse.\n   */\n  private _parseTimeString(value: string): Date | null {\n    // Note: we can technically rely on the browser for the time parsing by generating\n    // an ISO string and appending the string to the end of it. We don't do it, because\n    // browsers aren't consistent in what they support. Some examples:\n    // - Safari doesn't support AM/PM.\n    // - Firefox produces a valid date object if the time string has overflows (e.g. 12:75) while\n    //   other browsers produce an invalid date.\n    // - Safari doesn't allow padded numbers.\n    const parsed = value.toUpperCase().match(TIME_REGEX);\n\n    if (parsed) {\n      let hours = parseInt(parsed[1]);\n      const minutes = parseInt(parsed[2]);\n      let seconds: number | undefined = parsed[3] == null ? undefined : parseInt(parsed[3]);\n      const amPm = parsed[4] as 'AM' | 'PM' | undefined;\n\n      if (hours === 12) {\n        hours = amPm === 'AM' ? 0 : hours;\n      } else if (amPm === 'PM') {\n        hours += 12;\n      }\n\n      if (\n        inRange(hours, 0, 23) &&\n        inRange(minutes, 0, 59) &&\n        (seconds == null || inRange(seconds, 0, 59))\n      ) {\n        return this.setTime(this.today(), hours, minutes, seconds || 0);\n      }\n    }\n\n    return null;\n  }\n}\n\n/** Checks whether a number is within a certain range. */\nfunction inRange(value: number, min: number, max: number): boolean {\n  return !isNaN(value) && value >= min && value <= max;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {MatDateFormats} from './date-formats';\n\nexport const MAT_NATIVE_DATE_FORMATS: MatDateFormats = {\n  parse: {\n    dateInput: null,\n    timeInput: null,\n  },\n  display: {\n    dateInput: {year: 'numeric', month: 'numeric', day: 'numeric'},\n    timeInput: {hour: 'numeric', minute: 'numeric'},\n    monthYearLabel: {year: 'numeric', month: 'short'},\n    dateA11yLabel: {year: 'numeric', month: 'long', day: 'numeric'},\n    monthYearA11yLabel: {year: 'numeric', month: 'long'},\n    timeOptionLabel: {hour: 'numeric', minute: 'numeric'},\n  },\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule, Provider} from '@angular/core';\nimport {DateAdapter} from './date-adapter';\nimport {MAT_DATE_FORMATS, MatDateFormats} from './date-formats';\nimport {NativeDateAdapter} from './native-date-adapter';\nimport {MAT_NATIVE_DATE_FORMATS} from './native-date-formats';\n\nexport * from './date-adapter';\nexport * from './date-formats';\nexport * from './native-date-adapter';\nexport * from './native-date-formats';\n\n@NgModule({\n  providers: [{provide: DateAdapter, useClass: NativeDateAdapter}],\n})\nexport class NativeDateModule {}\n\n@NgModule({\n  providers: [provideNativeDateAdapter()],\n})\nexport class MatNativeDateModule {}\n\nexport function provideNativeDateAdapter(\n  formats: MatDateFormats = MAT_NATIVE_DATE_FORMATS,\n): Provider[] {\n  return [\n    {provide: DateAdapter, useClass: NativeDateAdapter},\n    {provide: MAT_DATE_FORMATS, useValue: formats},\n  ];\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA;MACa,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB;;ACAtD;;;;AAIG;AACH,MAAM,cAAc,GAClB,oFAAoF;AAEtF;;;;;;;;;;AAUG;AACH,MAAM,UAAU,GAAG,kDAAkD;AAErE;AACA,SAAS,KAAK,CAAI,MAAc,EAAE,aAAmC,EAAA;AACnE,IAAA,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC;AACjC,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QAC/B,WAAW,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC;;AAEnC,IAAA,OAAO,WAAW;AACpB;AAEA;AAEM,MAAO,iBAAkB,SAAQ,WAAiB,CAAA;AACtD;;;AAGG;IACH,gBAAgB,GAAY,KAAK;;IAGhB,cAAc,GAAG,MAAM,CAAC,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAI3E,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AAEP,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,eAAe,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AAE/D,QAAA,IAAI,aAAa,KAAK,SAAS,EAAE;AAC/B,YAAA,IAAI,CAAC,cAAc,GAAG,aAAa;;AAGrC,QAAA,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC;;AAGtC,IAAA,OAAO,CAAC,IAAU,EAAA;AAChB,QAAA,OAAO,IAAI,CAAC,WAAW,EAAE;;AAG3B,IAAA,QAAQ,CAAC,IAAU,EAAA;AACjB,QAAA,OAAO,IAAI,CAAC,QAAQ,EAAE;;AAGxB,IAAA,OAAO,CAAC,IAAU,EAAA;AAChB,QAAA,OAAO,IAAI,CAAC,OAAO,EAAE;;AAGvB,IAAA,YAAY,CAAC,IAAU,EAAA;AACrB,QAAA,OAAO,IAAI,CAAC,MAAM,EAAE;;AAGtB,IAAA,aAAa,CAAC,KAAkC,EAAA;QAC9C,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC;QACjF,OAAO,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAGhE,YAAY,GAAA;QACV,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC;QACnF,OAAO,KAAK,CAAC,EAAE,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAGpE,IAAA,iBAAiB,CAAC,KAAkC,EAAA;QAClD,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC;QACnF,OAAO,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;AAGnE,IAAA,WAAW,CAAC,IAAU,EAAA;QACpB,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC;QACpF,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC;;IAGhC,iBAAiB,GAAA;;;QAGf,IAAI,OAAO,IAAI,KAAK,WAAW,IAAK,IAAY,CAAC,MAAM,EAAE;YACvD,MAAM,MAAM,GAAG,IAAK,IAAY,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAGlD;;;AAID,YAAA,MAAM,QAAQ,GAAG,CAAC,MAAM,CAAC,WAAW,IAAI,IAAI,MAAM,CAAC,QAAQ,GAAG,QAAQ,IAAI,CAAC;;;YAI3E,OAAO,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAG,QAAQ;;;AAItC,QAAA,OAAO,CAAC;;AAGV,IAAA,iBAAiB,CAAC,IAAU,EAAA;QAC1B,OAAO,IAAI,CAAC,OAAO,CACjB,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAC7E;;AAGH,IAAA,KAAK,CAAC,IAAU,EAAA;QACd,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;;AAGjC,IAAA,UAAU,CAAC,IAAY,EAAE,KAAa,EAAE,IAAY,EAAA;AAClD,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;;;YAGjD,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,EAAE;AAC3B,gBAAA,MAAM,KAAK,CAAC,CAAA,qBAAA,EAAwB,KAAK,CAAA,0CAAA,CAA4C,CAAC;;AAGxF,YAAA,IAAI,IAAI,GAAG,CAAC,EAAE;AACZ,gBAAA,MAAM,KAAK,CAAC,CAAA,cAAA,EAAiB,IAAI,CAAA,iCAAA,CAAmC,CAAC;;;AAIzE,QAAA,IAAI,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;;AAE5D,QAAA,IAAI,MAAM,CAAC,QAAQ,EAAE,IAAI,KAAK,KAAK,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,EAAE;YACjF,MAAM,KAAK,CAAC,CAAiB,cAAA,EAAA,IAAI,2BAA2B,KAAK,CAAA,EAAA,CAAI,CAAC;;AAGxE,QAAA,OAAO,MAAM;;IAGf,KAAK,GAAA;QACH,OAAO,IAAI,IAAI,EAAE;;IAGnB,KAAK,CAAC,KAAU,EAAE,WAAiB,EAAA;;;AAGjC,QAAA,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;AAC5B,YAAA,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC;;AAExB,QAAA,OAAO,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI;;IAGnD,MAAM,CAAC,IAAU,EAAE,aAAqB,EAAA;QACtC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACvB,YAAA,MAAM,KAAK,CAAC,gDAAgD,CAAC;;QAG/D,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,EAAC,GAAG,aAAa,EAAE,QAAQ,EAAE,KAAK,EAAC,CAAC;QACrF,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC;;IAGhC,gBAAgB,CAAC,IAAU,EAAE,KAAa,EAAA;QACxC,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC;;IAGjD,iBAAiB,CAAC,IAAU,EAAE,MAAc,EAAA;AAC1C,QAAA,IAAI,OAAO,GAAG,IAAI,CAAC,uBAAuB,CACxC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAClB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,EAC5B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CACnB;;;;;AAMD,QAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;YAC/E,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;;AAG1F,QAAA,OAAO,OAAO;;IAGhB,eAAe,CAAC,IAAU,EAAE,IAAY,EAAA;QACtC,OAAO,IAAI,CAAC,uBAAuB,CACjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAClB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EACnB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAC1B;;AAGH,IAAA,SAAS,CAAC,IAAU,EAAA;QAClB,OAAO;YACL,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;AACpC,YAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;AAChC,SAAA,CAAC,IAAI,CAAC,GAAG,CAAC;;AAGb;;;;AAIG;AACM,IAAA,WAAW,CAAC,KAAU,EAAA;AAC7B,QAAA,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAI,CAAC,KAAK,EAAE;AACV,gBAAA,OAAO,IAAI;;;;AAIb,YAAA,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AAC9B,gBAAA,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC;AAC1B,gBAAA,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AACtB,oBAAA,OAAO,IAAI;;;;AAIjB,QAAA,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC;;AAGjC,IAAA,cAAc,CAAC,GAAQ,EAAA;QACrB,OAAO,GAAG,YAAY,IAAI;;AAG5B,IAAA,OAAO,CAAC,IAAU,EAAA;QAChB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;;IAG/B,OAAO,GAAA;AACL,QAAA,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC;;AAGb,IAAA,OAAO,CAAC,MAAY,EAAE,KAAa,EAAE,OAAe,EAAE,OAAe,EAAA;AAC5E,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;YACjD,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;AAC1B,gBAAA,MAAM,KAAK,CAAC,CAAA,eAAA,EAAkB,KAAK,CAAA,wCAAA,CAA0C,CAAC;;YAGhF,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;AAC5B,gBAAA,MAAM,KAAK,CAAC,CAAA,iBAAA,EAAoB,OAAO,CAAA,0CAAA,CAA4C,CAAC;;YAGtF,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;AAC5B,gBAAA,MAAM,KAAK,CAAC,CAAA,iBAAA,EAAoB,OAAO,CAAA,0CAAA,CAA4C,CAAC;;;QAIxF,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;QAChC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;AAC1C,QAAA,OAAO,KAAK;;AAGL,IAAA,QAAQ,CAAC,IAAU,EAAA;AAC1B,QAAA,OAAO,IAAI,CAAC,QAAQ,EAAE;;AAGf,IAAA,UAAU,CAAC,IAAU,EAAA;AAC5B,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE;;AAGjB,IAAA,UAAU,CAAC,IAAU,EAAA;AAC5B,QAAA,OAAO,IAAI,CAAC,UAAU,EAAE;;IAGjB,SAAS,CAAC,SAAc,EAAE,WAAiB,EAAA;AAClD,QAAA,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;AACjC,YAAA,OAAO,SAAS,YAAY,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,IAAI;;AAGzE,QAAA,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,EAAE;AAE9B,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;AACtB,YAAA,OAAO,IAAI;;;QAIb,IAAI,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;;;AAIzC,QAAA,IAAI,MAAM,KAAK,IAAI,EAAE;AACnB,YAAA,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE;AAElE,YAAA,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AAC5B,gBAAA,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC;;;AAIjD,QAAA,OAAO,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;;IAGxB,UAAU,CAAC,IAAU,EAAE,MAAc,EAAA;AAC5C,QAAA,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,MAAM,GAAG,IAAI,CAAC;;;AAIzC,IAAA,uBAAuB,CAAC,IAAY,EAAE,KAAa,EAAE,IAAY,EAAA;;;AAGvE,QAAA,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE;QACpB,CAAC,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;QAChC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACtB,QAAA,OAAO,CAAC;;AAGV;;;;AAIG;AACK,IAAA,OAAO,CAAC,CAAS,EAAA;QACvB,OAAO,CAAC,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;;AAG7B;;;;;;;;;;AAUG;IACK,OAAO,CAAC,GAAwB,EAAE,IAAU,EAAA;;;AAGlD,QAAA,MAAM,CAAC,GAAG,IAAI,IAAI,EAAE;AACpB,QAAA,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;QACrE,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE,CAAC;AAC5F,QAAA,OAAO,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;;AAGtB;;;AAGG;AACK,IAAA,gBAAgB,CAAC,KAAa,EAAA;;;;;;;;QAQpC,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;QAEpD,IAAI,MAAM,EAAE;YACV,IAAI,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YAC/B,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,OAAO,GAAuB,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACrF,YAAA,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAA4B;AAEjD,YAAA,IAAI,KAAK,KAAK,EAAE,EAAE;AAChB,gBAAA,KAAK,GAAG,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,KAAK;;AAC5B,iBAAA,IAAI,IAAI,KAAK,IAAI,EAAE;gBACxB,KAAK,IAAI,EAAE;;AAGb,YAAA,IACE,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;AACrB,gBAAA,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC;AACvB,iBAAC,OAAO,IAAI,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAC5C;AACA,gBAAA,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,IAAI,CAAC,CAAC;;;AAInE,QAAA,OAAO,IAAI;;uGAzVF,iBAAiB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAjB,iBAAiB,EAAA,CAAA;;2FAAjB,iBAAiB,EAAA,UAAA,EAAA,CAAA;kBAD7B;;AA8VD;AACA,SAAS,OAAO,CAAC,KAAa,EAAE,GAAW,EAAE,GAAW,EAAA;AACtD,IAAA,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG;AACtD;;ACjYa,MAAA,uBAAuB,GAAmB;AACrD,IAAA,KAAK,EAAE;AACL,QAAA,SAAS,EAAE,IAAI;AACf,QAAA,SAAS,EAAE,IAAI;AAChB,KAAA;AACD,IAAA,OAAO,EAAE;AACP,QAAA,SAAS,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,SAAS,EAAC;QAC9D,SAAS,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAC;QAC/C,cAAc,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAC;AACjD,QAAA,aAAa,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAC;QAC/D,kBAAkB,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAC;QACpD,eAAe,EAAE,EAAC,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAC;AACtD,KAAA;;;MCAU,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAhB,gBAAgB,EAAA,CAAA;wGAAhB,gBAAgB,EAAA,SAAA,EAFhB,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,iBAAiB,EAAC,CAAC,EAAA,CAAA;;2FAErD,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAH5B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,iBAAiB,EAAC,CAAC;AACjE,iBAAA;;MAMY,mBAAmB,CAAA;uGAAnB,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAnB,mBAAmB,EAAA,CAAA;AAAnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,mBAAmB,EAFnB,SAAA,EAAA,CAAC,wBAAwB,EAAE,CAAC,EAAA,CAAA;;2FAE5B,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAH/B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE,CAAC,wBAAwB,EAAE,CAAC;AACxC,iBAAA;;AAGe,SAAA,wBAAwB,CACtC,OAAA,GAA0B,uBAAuB,EAAA;IAEjD,OAAO;AACL,QAAA,EAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,iBAAiB,EAAC;AACnD,QAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAC;KAC/C;AACH;;;;"}