const MAC_ENTER = 3;
const BACKSPACE = 8;
const TAB = 9;
const NUM_CENTER = 12;
const ENTER = 13;
const SHIFT = 16;
const CONTROL = 17;
const ALT = 18;
const PAUSE = 19;
const CAPS_LOCK = 20;
const ESCAPE = 27;
const SPACE = 32;
const PAGE_UP = 33;
const PAGE_DOWN = 34;
const END = 35;
const HOME = 36;
const LEFT_ARROW = 37;
const UP_ARROW = 38;
const RIGHT_ARROW = 39;
const DOWN_ARROW = 40;
const PLUS_SIGN = 43;
const PRINT_SCREEN = 44;
const INSERT = 45;
const DELETE = 46;
const ZERO = 48;
const ONE = 49;
const TWO = 50;
const THREE = 51;
const FOUR = 52;
const FIVE = 53;
const SIX = 54;
const SEVEN = 55;
const EIGHT = 56;
const NINE = 57;
const FF_SEMICOLON = 59; // Firefox (Gecko) fires this for semicolon instead of 186
const FF_EQUALS = 61; // Firefox (Gecko) fires this for equals instead of 187
const QUESTION_MARK = 63;
const AT_SIGN = 64;
const A = 65;
const B = 66;
const C = 67;
const D = 68;
const E = 69;
const F = 70;
const G = 71;
const H = 72;
const I = 73;
const J = 74;
const K = 75;
const L = 76;
const M = 77;
const N = 78;
const O = 79;
const P = 80;
const Q = 81;
const R = 82;
const S = 83;
const T = 84;
const U = 85;
const V = 86;
const W = 87;
const X = 88;
const Y = 89;
const Z = 90;
const META = 91; // WIN_KEY_LEFT
const MAC_WK_CMD_LEFT = 91;
const MAC_WK_CMD_RIGHT = 93;
const CONTEXT_MENU = 93;
const NUMPAD_ZERO = 96;
const NUMPAD_ONE = 97;
const NUMPAD_TWO = 98;
const NUMPAD_THREE = 99;
const NUMPAD_FOUR = 100;
const NUMPAD_FIVE = 101;
const NUMPAD_SIX = 102;
const NUMPAD_SEVEN = 103;
const NUMPAD_EIGHT = 104;
const NUMPAD_NINE = 105;
const NUMPAD_MULTIPLY = 106;
const NUMPAD_PLUS = 107;
const NUMPAD_MINUS = 109;
const NUMPAD_PERIOD = 110;
const NUMPAD_DIVIDE = 111;
const F1 = 112;
const F2 = 113;
const F3 = 114;
const F4 = 115;
const F5 = 116;
const F6 = 117;
const F7 = 118;
const F8 = 119;
const F9 = 120;
const F10 = 121;
const F11 = 122;
const F12 = 123;
const NUM_LOCK = 144;
const SCROLL_LOCK = 145;
const FIRST_MEDIA = 166;
const FF_MINUS = 173;
const MUTE = 173; // Firefox (Gecko) fires 181 for MUTE
const VOLUME_DOWN = 174; // Firefox (Gecko) fires 182 for VOLUME_DOWN
const VOLUME_UP = 175; // Firefox (Gecko) fires 183 for VOLUME_UP
const FF_MUTE = 181;
const FF_VOLUME_DOWN = 182;
const LAST_MEDIA = 183;
const FF_VOLUME_UP = 183;
const SEMICOLON = 186; // Firefox (Gecko) fires 59 for SEMICOLON
const EQUALS = 187; // Firefox (Gecko) fires 61 for EQUALS
const COMMA = 188;
const DASH = 189; // Firefox (Gecko) fires 173 for DASH/MINUS
const PERIOD = 190;
const SLASH = 191;
const APOSTROPHE = 192;
const TILDE = 192;
const OPEN_SQUARE_BRACKET = 219;
const BACKSLASH = 220;
const CLOSE_SQUARE_BRACKET = 221;
const SINGLE_QUOTE = 222;
const MAC_META = 224;

export { FF_EQUALS as $, A, BACKSPACE as B, CONTROL as C, DOWN_ARROW as D, END as E, F1 as F, PRINT_SCREEN as G, HOME as H, INSERT as I, TWO as J, THREE as K, LEFT_ARROW as L, MAC_META as M, NINE as N, ONE as O, PAGE_DOWN as P, FOUR as Q, RIGHT_ARROW as R, SPACE as S, TAB as T, UP_ARROW as U, FIVE as V, SIX as W, SEVEN as X, EIGHT as Y, Z, FF_SEMICOLON as _, PAGE_UP as a, CLOSE_SQUARE_BRACKET as a$, QUESTION_MARK as a0, AT_SIGN as a1, B as a2, C as a3, D as a4, E as a5, F as a6, G as a7, H as a8, I as a9, NUMPAD_SEVEN as aA, NUMPAD_EIGHT as aB, NUMPAD_NINE as aC, NUMPAD_MULTIPLY as aD, NUMPAD_PLUS as aE, NUMPAD_MINUS as aF, NUMPAD_PERIOD as aG, NUMPAD_DIVIDE as aH, NUM_LOCK as aI, SCROLL_LOCK as aJ, FIRST_MEDIA as aK, FF_MINUS as aL, MUTE as aM, VOLUME_DOWN as aN, VOLUME_UP as aO, FF_MUTE as aP, FF_VOLUME_DOWN as aQ, LAST_MEDIA as aR, FF_VOLUME_UP as aS, SEMICOLON as aT, EQUALS as aU, DASH as aV, SLASH as aW, APOSTROPHE as aX, TILDE as aY, OPEN_SQUARE_BRACKET as aZ, BACKSLASH as a_, J as aa, K as ab, L as ac, M as ad, N as ae, O as af, P as ag, Q as ah, R as ai, S as aj, T as ak, U as al, V as am, W as an, X as ao, Y as ap, MAC_WK_CMD_LEFT as aq, MAC_WK_CMD_RIGHT as ar, CONTEXT_MENU as as, NUMPAD_ZERO as at, NUMPAD_ONE as au, NUMPAD_TWO as av, NUMPAD_THREE as aw, NUMPAD_FOUR as ax, NUMPAD_FIVE as ay, NUMPAD_SIX as az, ZERO as b, SINGLE_QUOTE as b0, ENTER as c, ALT as d, META as e, SHIFT as f, ESCAPE as g, PERIOD as h, DELETE as i, F2 as j, F3 as k, F4 as l, F5 as m, F6 as n, F7 as o, F8 as p, F9 as q, F10 as r, F11 as s, F12 as t, COMMA as u, MAC_ENTER as v, NUM_CENTER as w, PAUSE as x, CAPS_LOCK as y, PLUS_SIGN as z };
//# sourceMappingURL=keycodes-CpHkExLC.mjs.map
