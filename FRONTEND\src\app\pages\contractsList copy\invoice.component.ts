import { Component, OnInit } from '@angular/core';
import { PageEvent, MatPaginatorModule } from '@angular/material/paginator';
import { MatTableModule } from '@angular/material/table';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Layout } from '../../layout/layout';

interface Invoice {
  building: string;
  room: number;
  start: string;
  end: string;
  deposit: string;
  total: number;
  status: 'Active' | 'Expire' | 'Terminate' | 'Unsigned' | 'Unpaid';
}

@Component({
  selector: 'app-invoice',
  templateUrl: './invoice.component.html',
  styleUrls: ['./invoice.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatTableModule,
    MatPaginatorModule,
    Layout,
  ],
})
export class InvoiceComponent implements OnInit {

  invoices: Invoice[] = [
    { building: 'ABCHome', room: 201, start: '6/2025', end: '10/7/2025', deposit: '10/7/2025', total: 2000000, status: 'Active' },
    { building: 'QHome', room: 310, start: '5/2025', end: '10/6/2025', deposit: '10/6/2025', total: 1100000, status: 'Expire' },
    { building: 'QHome', room: 101, start: '7/2025', end: '10/8/2025', deposit: '10/8/2025', total: 3000000, status: 'Terminate' },
    { building: 'QHome', room: 102, start: '7/2025', end: '10/8/2025', deposit: '10/8/2025', total: 3000000, status: 'Unsigned' },
    { building: 'QHome', room: 102, start: '7/2025', end: '10/8/2025', deposit: '10/8/2025', total: 3000000, status: 'Unpaid' },
    // ... Thêm dữ liệu khác nếu muốn
  ];

  filteredInvoices: Invoice[] = [];
  pageSize = 5;
  pageIndex = 0;

  buildings = ['ABCHome', 'QHome'];
  rooms = [101, 102, 201, 310];
  statuses = ['Active', 'Expire', 'Terminate', 'Unsigned', 'Unpaid'];

  filters = {
    building: '',
    room: '',
    status: ''
  };

  ngOnInit(): void {
    this.applyFilters();
  }

  applyFilters() {
    this.filteredInvoices = this.invoices.filter(inv =>
      (!this.filters.building || inv.building === this.filters.building) &&
      (!this.filters.room || inv.room.toString() === this.filters.room) &&
      (!this.filters.status || inv.status === this.filters.status)
    );
  }

  onPageChange(event: PageEvent) {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
  }

}
