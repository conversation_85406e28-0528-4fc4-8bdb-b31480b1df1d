{"version": 3, "file": "internal-form-field-D5iFxU6d.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/internal-form-field/internal-form-field.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, Input, ViewEncapsulation} from '@angular/core';\n\n/**\n * Internal shared component used as a container in form field controls.\n * Not to be confused with `mat-form-field` which MDC calls a \"text field\".\n * @docs-private\n */\n@Component({\n  // Use a `div` selector to match the old markup closer.\n  selector: 'div[mat-internal-form-field]',\n  template: '<ng-content></ng-content>',\n  styleUrl: 'internal-form-field.css',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  host: {\n    'class': 'mdc-form-field mat-internal-form-field',\n    '[class.mdc-form-field--align-end]': 'labelPosition === \"before\"',\n  },\n})\nexport class _MatInternalFormField {\n  /** Position of the label relative to the content. */\n  @Input({required: true}) labelPosition: 'before' | 'after';\n}\n"], "names": [], "mappings": ";;;AAUA;;;;AAIG;MAaU,qBAAqB,CAAA;;AAEP,IAAA,aAAa;uGAF3B,qBAAqB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAArB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,qBAAqB,2RATtB,2BAA2B,EAAA,QAAA,EAAA,IAAA,EAAA,MAAA,EAAA,CAAA,mmBAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAS1B,qBAAqB,EAAA,UAAA,EAAA,CAAA;kBAZjC,SAAS;+BAEE,8BAA8B,EAAA,QAAA,EAC9B,2BAA2B,EAAA,aAAA,EAEtB,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EACzC,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,wCAAwC;AACjD,wBAAA,mCAAmC,EAAE,4BAA4B;AAClE,qBAAA,EAAA,MAAA,EAAA,CAAA,mmBAAA,CAAA,EAAA;8BAIwB,aAAa,EAAA,CAAA;sBAArC,KAAK;uBAAC,EAAC,QAAQ,EAAE,IAAI,EAAC;;;;;"}