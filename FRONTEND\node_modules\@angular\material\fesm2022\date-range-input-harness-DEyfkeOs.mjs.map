{"version": 3, "file": "date-range-input-harness-DEyfkeOs.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/datepicker/testing/datepicker-input-harness-base.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/datepicker/testing/calendar-cell-harness.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/datepicker/testing/calendar-harness.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/datepicker/testing/datepicker-trigger-harness-base.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/datepicker/testing/datepicker-input-harness.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/datepicker/testing/date-range-input-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarnessConstructor, HarnessPredicate} from '@angular/cdk/testing';\nimport {MatFormFieldControlHarnessBase} from '@angular/material/form-field/testing/control';\nimport {DatepickerInputHarnessFilters} from './datepicker-harness-filters';\n\n/** Sets up the filter predicates for a datepicker input harness. */\nexport function getInputPredicate<T extends MatDatepickerInputHarnessBase>(\n  type: ComponentHarnessConstructor<T>,\n  options: DatepickerInputHarnessFilters,\n): HarnessPredicate<T> {\n  return new HarnessPredicate(type, options)\n    .addOption('value', options.value, (harness, value) => {\n      return HarnessPredicate.stringMatches(harness.getValue(), value);\n    })\n    .addOption('placeholder', options.placeholder, (harness, placeholder) => {\n      return HarnessPredicate.stringMatches(harness.getPlaceholder(), placeholder);\n    })\n    .addOption('label', options.label, (harness, label) => {\n      return HarnessPredicate.stringMatches(harness.getLabel(), label);\n    });\n}\n\n/** Base class for datepicker input harnesses. */\nexport abstract class MatDatepickerInputHarnessBase extends MatFormFieldControlHarnessBase {\n  /** Whether the input is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('disabled');\n  }\n\n  /** Whether the input is required. */\n  async isRequired(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('required');\n  }\n\n  /** Gets the value of the input. */\n  async getValue(): Promise<string> {\n    // The \"value\" property of the native input is always defined.\n    return await (await this.host()).getProperty<string>('value');\n  }\n\n  /**\n   * Sets the value of the input. The value will be set by simulating\n   * keypresses that correspond to the given value.\n   */\n  async setValue(newValue: string): Promise<void> {\n    const inputEl = await this.host();\n    await inputEl.clear();\n\n    // We don't want to send keys for the value if the value is an empty\n    // string in order to clear the value. Sending keys with an empty string\n    // still results in unnecessary focus events.\n    if (newValue) {\n      await inputEl.sendKeys(newValue);\n    }\n\n    await inputEl.dispatchEvent('change');\n  }\n\n  /** Gets the placeholder of the input. */\n  async getPlaceholder(): Promise<string> {\n    return await (await this.host()).getProperty<string>('placeholder');\n  }\n\n  /**\n   * Focuses the input and returns a promise that indicates when the\n   * action is complete.\n   */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /**\n   * Blurs the input and returns a promise that indicates when the\n   * action is complete.\n   */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the input is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n\n  /** Gets the formatted minimum date for the input's value. */\n  async getMin(): Promise<string | null> {\n    return (await this.host()).getAttribute('min');\n  }\n\n  /** Gets the formatted maximum date for the input's value. */\n  async getMax(): Promise<string | null> {\n    return (await this.host()).getAttribute('max');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HarnessPredicate, ComponentHarness} from '@angular/cdk/testing';\nimport {CalendarCellHarnessFilters} from './datepicker-harness-filters';\n\n/** Harness for interacting with a standard Material calendar cell in tests. */\nexport class MatCalendarCellHarness extends ComponentHarness {\n  static hostSelector = '.mat-calendar-body-cell';\n\n  /** Reference to the inner content element inside the cell. */\n  private _content = this.locatorFor('.mat-calendar-body-cell-content');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatCalendarCellHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which cell instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: CalendarCellHarnessFilters = {}): HarnessPredicate<MatCalendarCellHarness> {\n    return new HarnessPredicate(MatCalendarCellHarness, options)\n      .addOption('text', options.text, (harness, text) => {\n        return HarnessPredicate.stringMatches(harness.getText(), text);\n      })\n      .addOption('selected', options.selected, async (harness, selected) => {\n        return (await harness.isSelected()) === selected;\n      })\n      .addOption('active', options.active, async (harness, active) => {\n        return (await harness.isActive()) === active;\n      })\n      .addOption('disabled', options.disabled, async (harness, disabled) => {\n        return (await harness.isDisabled()) === disabled;\n      })\n      .addOption('today', options.today, async (harness, today) => {\n        return (await harness.isToday()) === today;\n      })\n      .addOption('inRange', options.inRange, async (harness, inRange) => {\n        return (await harness.isInRange()) === inRange;\n      })\n      .addOption(\n        'inComparisonRange',\n        options.inComparisonRange,\n        async (harness, inComparisonRange) => {\n          return (await harness.isInComparisonRange()) === inComparisonRange;\n        },\n      )\n      .addOption('inPreviewRange', options.inPreviewRange, async (harness, inPreviewRange) => {\n        return (await harness.isInPreviewRange()) === inPreviewRange;\n      });\n  }\n\n  /** Gets the text of the calendar cell. */\n  async getText(): Promise<string> {\n    return (await this._content()).text();\n  }\n\n  /** Gets the aria-label of the calendar cell. */\n  async getAriaLabel(): Promise<string> {\n    // We're guaranteed for the `aria-label` to be defined\n    // since this is a private element that we control.\n    return (await this.host()).getAttribute('aria-label') as Promise<string>;\n  }\n\n  /** Whether the cell is selected. */\n  async isSelected(): Promise<boolean> {\n    const host = await this.host();\n    return (await host.getAttribute('aria-pressed')) === 'true';\n  }\n\n  /** Whether the cell is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return this._hasState('disabled');\n  }\n\n  /** Whether the cell is currently activated using keyboard navigation. */\n  async isActive(): Promise<boolean> {\n    return this._hasState('active');\n  }\n\n  /** Whether the cell represents today's date. */\n  async isToday(): Promise<boolean> {\n    return (await this._content()).hasClass('mat-calendar-body-today');\n  }\n\n  /** Selects the calendar cell. Won't do anything if the cell is disabled. */\n  async select(): Promise<void> {\n    return (await this.host()).click();\n  }\n\n  /** Hovers over the calendar cell. */\n  async hover(): Promise<void> {\n    return (await this.host()).hover();\n  }\n\n  /** Moves the mouse away from the calendar cell. */\n  async mouseAway(): Promise<void> {\n    return (await this.host()).mouseAway();\n  }\n\n  /** Focuses the calendar cell. */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /** Removes focus from the calendar cell. */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the cell is the start of the main range. */\n  async isRangeStart(): Promise<boolean> {\n    return this._hasState('range-start');\n  }\n\n  /** Whether the cell is the end of the main range. */\n  async isRangeEnd(): Promise<boolean> {\n    return this._hasState('range-end');\n  }\n\n  /** Whether the cell is part of the main range. */\n  async isInRange(): Promise<boolean> {\n    return this._hasState('in-range');\n  }\n\n  /** Whether the cell is the start of the comparison range. */\n  async isComparisonRangeStart(): Promise<boolean> {\n    return this._hasState('comparison-start');\n  }\n\n  /** Whether the cell is the end of the comparison range. */\n  async isComparisonRangeEnd(): Promise<boolean> {\n    return this._hasState('comparison-end');\n  }\n\n  /** Whether the cell is inside of the comparison range. */\n  async isInComparisonRange(): Promise<boolean> {\n    return this._hasState('in-comparison-range');\n  }\n\n  /** Whether the cell is the start of the preview range. */\n  async isPreviewRangeStart(): Promise<boolean> {\n    return this._hasState('preview-start');\n  }\n\n  /** Whether the cell is the end of the preview range. */\n  async isPreviewRangeEnd(): Promise<boolean> {\n    return this._hasState('preview-end');\n  }\n\n  /** Whether the cell is inside of the preview range. */\n  async isInPreviewRange(): Promise<boolean> {\n    return this._hasState('in-preview');\n  }\n\n  /** Returns whether the cell has a particular CSS class-based state. */\n  private async _hasState(name: string): Promise<boolean> {\n    return (await this.host()).hasClass(`mat-calendar-body-${name}`);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HarnessPredicate, ComponentHarness} from '@angular/cdk/testing';\nimport {CalendarHarnessFilters, CalendarCellHarnessFilters} from './datepicker-harness-filters';\nimport {MatCalendarCellHarness} from './calendar-cell-harness';\n\n/** Possible views of a `MatCalendarHarness`. */\nexport enum CalendarView {\n  MONTH,\n  YEAR,\n  MULTI_YEAR,\n}\n\n/** Harness for interacting with a standard Material calendar in tests. */\nexport class MatCalendarHarness extends ComponentHarness {\n  static hostSelector = '.mat-calendar';\n\n  /** Queries for the calendar's period toggle button. */\n  private _periodButton = this.locatorFor('.mat-calendar-period-button');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatCalendarHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which calendar instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: CalendarHarnessFilters = {}): HarnessPredicate<MatCalendarHarness> {\n    return new HarnessPredicate(MatCalendarHarness, options);\n  }\n\n  /**\n   * Gets a list of cells inside the calendar.\n   * @param filter Optionally filters which cells are included.\n   */\n  async getCells(filter: CalendarCellHarnessFilters = {}): Promise<MatCalendarCellHarness[]> {\n    return this.locatorForAll(MatCalendarCellHarness.with(filter))();\n  }\n\n  /** Gets the current view that is being shown inside the calendar. */\n  async getCurrentView(): Promise<CalendarView> {\n    if (await this.locatorForOptional('mat-multi-year-view')()) {\n      return CalendarView.MULTI_YEAR;\n    }\n\n    if (await this.locatorForOptional('mat-year-view')()) {\n      return CalendarView.YEAR;\n    }\n\n    return CalendarView.MONTH;\n  }\n\n  /** Gets the label of the current calendar view. */\n  async getCurrentViewLabel(): Promise<string> {\n    return (await this._periodButton()).text();\n  }\n\n  /** Changes the calendar view by clicking on the view toggle button. */\n  async changeView(): Promise<void> {\n    return (await this._periodButton()).click();\n  }\n\n  /** Goes to the next page of the current view (e.g. next month when inside the month view). */\n  async next(): Promise<void> {\n    return (await this.locatorFor('.mat-calendar-next-button')()).click();\n  }\n\n  /**\n   * Goes to the previous page of the current view\n   * (e.g. previous month when inside the month view).\n   */\n  async previous(): Promise<void> {\n    return (await this.locatorFor('.mat-calendar-previous-button')()).click();\n  }\n\n  /**\n   * Selects a cell in the current calendar view.\n   * @param filter An optional filter to apply to the cells. The first cell matching the filter\n   *     will be selected.\n   */\n  async selectCell(filter: CalendarCellHarnessFilters = {}): Promise<void> {\n    const cells = await this.getCells(filter);\n    if (!cells.length) {\n      throw Error(`Cannot find calendar cell matching filter ${JSON.stringify(filter)}`);\n    }\n    await cells[0].select();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, LocatorFactory, parallel, TestElement} from '@angular/cdk/testing';\nimport {CalendarHarnessFilters} from './datepicker-harness-filters';\nimport {MatCalendarHarness} from './calendar-harness';\n\n/** Interface for a test harness that can open and close a calendar. */\nexport interface DatepickerTrigger {\n  isCalendarOpen(): Promise<boolean>;\n  openCalendar(): Promise<void>;\n  closeCalendar(): Promise<void>;\n  hasCalendar(): Promise<boolean>;\n  getCalendar(filter?: CalendarHarnessFilters): Promise<MatCalendarHarness>;\n}\n\n/** Base class for harnesses that can trigger a calendar. */\nexport abstract class DatepickerTriggerHarnessBase\n  extends ComponentHarness\n  implements DatepickerTrigger\n{\n  /** Whether the trigger is disabled. */\n  abstract isDisabled(): Promise<boolean>;\n\n  /** Whether the calendar associated with the trigger is open. */\n  abstract isCalendarOpen(): Promise<boolean>;\n\n  /** Opens the calendar associated with the trigger. */\n  protected abstract _openCalendar(): Promise<void>;\n\n  /** Opens the calendar if the trigger is enabled and it has a calendar. */\n  async openCalendar(): Promise<void> {\n    const [isDisabled, hasCalendar] = await parallel(() => [this.isDisabled(), this.hasCalendar()]);\n\n    if (!isDisabled && hasCalendar) {\n      return this._openCalendar();\n    }\n  }\n\n  /** Closes the calendar if it is open. */\n  async closeCalendar(): Promise<void> {\n    if (await this.isCalendarOpen()) {\n      await closeCalendar(getCalendarId(this.host()), this.documentRootLocatorFactory());\n      // This is necessary so that we wait for the closing animation to finish in touch UI mode.\n      await this.forceStabilize();\n    }\n  }\n\n  /** Gets whether there is a calendar associated with the trigger. */\n  async hasCalendar(): Promise<boolean> {\n    return (await getCalendarId(this.host())) != null;\n  }\n\n  /**\n   * Gets the `MatCalendarHarness` that is associated with the trigger.\n   * @param filter Optionally filters which calendar is included.\n   */\n  async getCalendar(filter: CalendarHarnessFilters = {}): Promise<MatCalendarHarness> {\n    return getCalendar(filter, this.host(), this.documentRootLocatorFactory());\n  }\n}\n\n/** Gets the ID of the calendar that a particular test element can trigger. */\nexport async function getCalendarId(host: Promise<TestElement>): Promise<string | null> {\n  return (await host).getAttribute('data-mat-calendar');\n}\n\n/** Closes the calendar with a specific ID. */\nexport async function closeCalendar(\n  calendarId: Promise<string | null>,\n  documentLocator: LocatorFactory,\n) {\n  // We close the calendar by clicking on the backdrop, even though all datepicker variants\n  // have the ability to close by pressing escape. The backdrop is preferrable, because the\n  // escape key has multiple functions inside a range picker (either cancel the current range\n  // or close the calendar). Since we don't have access to set the ID on the backdrop in all\n  // cases, we set a unique class instead which is the same as the calendar's ID and suffixed\n  // with `-backdrop`.\n  const backdropSelector = `.${await calendarId}-backdrop`;\n  return (await documentLocator.locatorFor(backdropSelector)()).click();\n}\n\n/** Gets the test harness for a calendar associated with a particular host. */\nexport async function getCalendar(\n  filter: CalendarHarnessFilters,\n  host: Promise<TestElement>,\n  documentLocator: LocatorFactory,\n): Promise<MatCalendarHarness> {\n  const calendarId = await getCalendarId(host);\n\n  if (!calendarId) {\n    throw Error(`Element is not associated with a calendar`);\n  }\n\n  return documentLocator.locatorFor(\n    MatCalendarHarness.with({\n      ...filter,\n      selector: `#${calendarId}`,\n    }),\n  )();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HarnessPredicate, parallel, TestKey} from '@angular/cdk/testing';\nimport {DatepickerInputHarnessFilters, CalendarHarnessFilters} from './datepicker-harness-filters';\nimport {MatDatepickerInputHarnessBase, getInputPredicate} from './datepicker-input-harness-base';\nimport {MatCalendarHarness} from './calendar-harness';\nimport {\n  DatepickerTrigger,\n  closeCalendar,\n  getCalendarId,\n  getCalendar,\n} from './datepicker-trigger-harness-base';\n\n/** Harness for interacting with a standard Material datepicker inputs in tests. */\nexport class MatDatepickerInputHarness\n  extends MatDatepickerInputHarnessBase\n  implements DatepickerTrigger\n{\n  static hostSelector = '.mat-datepicker-input';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatDatepickerInputHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: DatepickerInputHarnessFilters = {},\n  ): HarnessPredicate<MatDatepickerInputHarness> {\n    return getInputPredicate(MatDatepickerInputHarness, options);\n  }\n\n  /** Gets whether the calendar associated with the input is open. */\n  async isCalendarOpen(): Promise<boolean> {\n    // `aria-owns` is set only if there's an open datepicker so we can use it as an indicator.\n    const host = await this.host();\n    return (await host.getAttribute('aria-owns')) != null;\n  }\n\n  /** Opens the calendar associated with the input. */\n  async openCalendar(): Promise<void> {\n    const [isDisabled, hasCalendar] = await parallel(() => [this.isDisabled(), this.hasCalendar()]);\n\n    if (!isDisabled && hasCalendar) {\n      // Alt + down arrow is the combination for opening the calendar with the keyboard.\n      const host = await this.host();\n      return host.sendKeys({alt: true}, TestKey.DOWN_ARROW);\n    }\n  }\n\n  /** Closes the calendar associated with the input. */\n  async closeCalendar(): Promise<void> {\n    if (await this.isCalendarOpen()) {\n      await closeCalendar(getCalendarId(this.host()), this.documentRootLocatorFactory());\n      // This is necessary so that we wait for the closing animation to finish in touch UI mode.\n      await this.forceStabilize();\n    }\n  }\n\n  /** Whether a calendar is associated with the input. */\n  async hasCalendar(): Promise<boolean> {\n    return (await getCalendarId(this.host())) != null;\n  }\n\n  /**\n   * Gets the `MatCalendarHarness` that is associated with the trigger.\n   * @param filter Optionally filters which calendar is included.\n   */\n  async getCalendar(filter: CalendarHarnessFilters = {}): Promise<MatCalendarHarness> {\n    return getCalendar(filter, this.host(), this.documentRootLocatorFactory());\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HarnessPredicate, parallel, TestKey} from '@angular/cdk/testing';\nimport {MatDatepickerInputHarnessBase, getInputPredicate} from './datepicker-input-harness-base';\nimport {DatepickerTriggerHarnessBase} from './datepicker-trigger-harness-base';\nimport {\n  DatepickerInputHarnessFilters,\n  DateRangeInputHarnessFilters,\n} from './datepicker-harness-filters';\n\n/** Harness for interacting with a standard Material date range start input in tests. */\nexport class MatStartDateHarness extends MatDatepickerInputHarnessBase {\n  static hostSelector = '.mat-start-date';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatStartDateHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: DatepickerInputHarnessFilters = {}): HarnessPredicate<MatStartDateHarness> {\n    return getInputPredicate(MatStartDateHarness, options);\n  }\n}\n\n/** Harness for interacting with a standard Material date range end input in tests. */\nexport class MatEndDateHarness extends MatDatepickerInputHarnessBase {\n  static hostSelector = '.mat-end-date';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatEndDateHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: DatepickerInputHarnessFilters = {}): HarnessPredicate<MatEndDateHarness> {\n    return getInputPredicate(MatEndDateHarness, options);\n  }\n}\n\n/** Harness for interacting with a standard Material date range input in tests. */\nexport class MatDateRangeInputHarness extends DatepickerTriggerHarnessBase {\n  static hostSelector = '.mat-date-range-input';\n\n  private readonly _floatingLabelSelector = '.mdc-floating-label';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatDateRangeInputHarness`\n   * that meets certain criteria.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: DateRangeInputHarnessFilters = {},\n  ): HarnessPredicate<MatDateRangeInputHarness> {\n    return new HarnessPredicate(MatDateRangeInputHarness, options)\n      .addOption('value', options.value, (harness, value) =>\n        HarnessPredicate.stringMatches(harness.getValue(), value),\n      )\n      .addOption('label', options.label, (harness, label) => {\n        return HarnessPredicate.stringMatches(harness.getLabel(), label);\n      });\n  }\n\n  /** Gets the combined value of the start and end inputs, including the separator. */\n  async getValue(): Promise<string> {\n    const [start, end, separator] = await parallel(() => [\n      this.getStartInput().then(input => input.getValue()),\n      this.getEndInput().then(input => input.getValue()),\n      this.getSeparator(),\n    ]);\n\n    return start + `${end ? ` ${separator} ${end}` : ''}`;\n  }\n\n  /** Gets the inner start date input inside the range input. */\n  async getStartInput(): Promise<MatStartDateHarness> {\n    // Don't pass in filters here since the start input is required and there can only be one.\n    return this.locatorFor(MatStartDateHarness)();\n  }\n\n  /** Gets the inner start date input inside the range input. */\n  async getEndInput(): Promise<MatEndDateHarness> {\n    // Don't pass in filters here since the end input is required and there can only be one.\n    return this.locatorFor(MatEndDateHarness)();\n  }\n\n  /** Gets the floating label text for the range input, if it exists. */\n  async getLabel(): Promise<string | null> {\n    // Copied from MatFormFieldControlHarnessBase since this class cannot extend two classes\n    const documentRootLocator = this.documentRootLocatorFactory();\n    const labelId = await (await this.host()).getAttribute('aria-labelledby');\n    const hostId = await (await this.host()).getAttribute('id');\n\n    if (labelId) {\n      // First option, try to fetch the label using the `aria-labelledby`\n      // attribute.\n      const labelEl = await documentRootLocator.locatorForOptional(\n        `${this._floatingLabelSelector}[id=\"${labelId}\"]`,\n      )();\n      return labelEl ? labelEl.text() : null;\n    } else if (hostId) {\n      // Fallback option, try to match the id of the input with the `for`\n      // attribute of the label.\n      const labelEl = await documentRootLocator.locatorForOptional(\n        `${this._floatingLabelSelector}[for=\"${hostId}\"]`,\n      )();\n      return labelEl ? labelEl.text() : null;\n    }\n    return null;\n  }\n\n  /** Gets the separator text between the values of the two inputs. */\n  async getSeparator(): Promise<string> {\n    return (await this.locatorFor('.mat-date-range-input-separator')()).text();\n  }\n\n  /** Gets whether the range input is disabled. */\n  async isDisabled(): Promise<boolean> {\n    // We consider the input as disabled if both of the sub-inputs are disabled.\n    const [startDisabled, endDisabled] = await parallel(() => [\n      this.getStartInput().then(input => input.isDisabled()),\n      this.getEndInput().then(input => input.isDisabled()),\n    ]);\n\n    return startDisabled && endDisabled;\n  }\n\n  /** Gets whether the range input is required. */\n  async isRequired(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-date-range-input-required');\n  }\n\n  /** Opens the calendar associated with the input. */\n  async isCalendarOpen(): Promise<boolean> {\n    // `aria-owns` is set on both inputs only if there's an\n    // open range picker so we can use it as an indicator.\n    const startHost = await (await this.getStartInput()).host();\n    return (await startHost.getAttribute('aria-owns')) != null;\n  }\n\n  protected async _openCalendar(): Promise<void> {\n    // Alt + down arrow is the combination for opening the calendar with the keyboard.\n    const startHost = await (await this.getStartInput()).host();\n    return startHost.sendKeys({alt: true}, TestKey.DOWN_ARROW);\n  }\n}\n"], "names": [], "mappings": ";;;AAYA;AACgB,SAAA,iBAAiB,CAC/B,IAAoC,EACpC,OAAsC,EAAA;AAEtC,IAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;AACtC,SAAA,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAAI;QACpD,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;AAClE,KAAC;AACA,SAAA,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,WAAW,KAAI;QACtE,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC;AAC9E,KAAC;AACA,SAAA,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAAI;QACpD,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;AAClE,KAAC,CAAC;AACN;AAEA;AACM,MAAgB,6BAA8B,SAAQ,8BAA8B,CAAA;;AAExF,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;AAI7D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;AAI7D,IAAA,MAAM,QAAQ,GAAA;;AAEZ,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,OAAO,CAAC;;AAG/D;;;AAGG;IACH,MAAM,QAAQ,CAAC,QAAgB,EAAA;AAC7B,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;AACjC,QAAA,MAAM,OAAO,CAAC,KAAK,EAAE;;;;QAKrB,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;;AAGlC,QAAA,MAAM,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;;;AAIvC,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,aAAa,CAAC;;AAGrE;;;AAGG;AACH,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;AAGpC;;;AAGG;AACH,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE;;;AAIxC,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,KAAK,CAAC;;;AAIhD,IAAA,MAAM,MAAM,GAAA;AACV,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,KAAK,CAAC;;AAEjD;;ACzFD;AACM,MAAO,sBAAuB,SAAQ,gBAAgB,CAAA;AAC1D,IAAA,OAAO,YAAY,GAAG,yBAAyB;;AAGvC,IAAA,QAAQ,GAAG,IAAI,CAAC,UAAU,CAAC,iCAAiC,CAAC;AAErE;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAsC,EAAE,EAAA;AAClD,QAAA,OAAO,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,OAAO;AACxD,aAAA,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAAI;YACjD,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;AAChE,SAAC;AACA,aAAA,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAI;YACnE,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;AAClD,SAAC;AACA,aAAA,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,OAAO,EAAE,MAAM,KAAI;YAC7D,OAAO,CAAC,MAAM,OAAO,CAAC,QAAQ,EAAE,MAAM,MAAM;AAC9C,SAAC;AACA,aAAA,SAAS,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,EAAE,OAAO,OAAO,EAAE,QAAQ,KAAI;YACnE,OAAO,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,QAAQ;AAClD,SAAC;AACA,aAAA,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,OAAO,EAAE,KAAK,KAAI;YAC1D,OAAO,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,MAAM,KAAK;AAC5C,SAAC;AACA,aAAA,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,OAAO,EAAE,OAAO,KAAI;YAChE,OAAO,CAAC,MAAM,OAAO,CAAC,SAAS,EAAE,MAAM,OAAO;AAChD,SAAC;AACA,aAAA,SAAS,CACR,mBAAmB,EACnB,OAAO,CAAC,iBAAiB,EACzB,OAAO,OAAO,EAAE,iBAAiB,KAAI;YACnC,OAAO,CAAC,MAAM,OAAO,CAAC,mBAAmB,EAAE,MAAM,iBAAiB;AACpE,SAAC;AAEF,aAAA,SAAS,CAAC,gBAAgB,EAAE,OAAO,CAAC,cAAc,EAAE,OAAO,OAAO,EAAE,cAAc,KAAI;YACrF,OAAO,CAAC,MAAM,OAAO,CAAC,gBAAgB,EAAE,MAAM,cAAc;AAC9D,SAAC,CAAC;;;AAIN,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE;;;AAIvC,IAAA,MAAM,YAAY,GAAA;;;AAGhB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,YAAY,CAAoB;;;AAI1E,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,OAAO,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,MAAM;;;AAI7D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;;;AAInC,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;;;AAIjC,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,yBAAyB,CAAC;;;AAIpE,IAAA,MAAM,MAAM,GAAA;QACV,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;AAIpC,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;AAIpC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE;;;AAIxC,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;AAIpC,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;;;AAItC,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;;;AAIpC,IAAA,MAAM,SAAS,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;;;AAInC,IAAA,MAAM,sBAAsB,GAAA;AAC1B,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;;;AAI3C,IAAA,MAAM,oBAAoB,GAAA;AACxB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC;;;AAIzC,IAAA,MAAM,mBAAmB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC;;;AAI9C,IAAA,MAAM,mBAAmB,GAAA;AACvB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;;;AAIxC,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;;;AAItC,IAAA,MAAM,gBAAgB,GAAA;AACpB,QAAA,OAAO,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC;;;IAI7B,MAAM,SAAS,CAAC,IAAY,EAAA;AAClC,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAA,kBAAA,EAAqB,IAAI,CAAA,CAAE,CAAC;;;;ACrJpE;IACY;AAAZ,CAAA,UAAY,YAAY,EAAA;AACtB,IAAA,YAAA,CAAA,YAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,OAAK;AACL,IAAA,YAAA,CAAA,YAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,YAAA,CAAA,YAAA,CAAA,YAAA,CAAA,GAAA,CAAA,CAAA,GAAA,YAAU;AACZ,CAAC,EAJW,YAAY,KAAZ,YAAY,GAIvB,EAAA,CAAA,CAAA;AAED;AACM,MAAO,kBAAmB,SAAQ,gBAAgB,CAAA;AACtD,IAAA,OAAO,YAAY,GAAG,eAAe;;AAG7B,IAAA,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,6BAA6B,CAAC;AAEtE;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAkC,EAAE,EAAA;AAC9C,QAAA,OAAO,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,OAAO,CAAC;;AAG1D;;;AAGG;AACH,IAAA,MAAM,QAAQ,CAAC,MAAA,GAAqC,EAAE,EAAA;AACpD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;;AAIlE,IAAA,MAAM,cAAc,GAAA;QAClB,IAAI,MAAM,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,EAAE,EAAE;YAC1D,OAAO,YAAY,CAAC,UAAU;;QAGhC,IAAI,MAAM,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,EAAE,EAAE;YACpD,OAAO,YAAY,CAAC,IAAI;;QAG1B,OAAO,YAAY,CAAC,KAAK;;;AAI3B,IAAA,MAAM,mBAAmB,GAAA;QACvB,OAAO,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE;;;AAI5C,IAAA,MAAM,UAAU,GAAA;QACd,OAAO,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE;;;AAI7C,IAAA,MAAM,IAAI,GAAA;AACR,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,2BAA2B,CAAC,EAAE,EAAE,KAAK,EAAE;;AAGvE;;;AAGG;AACH,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,+BAA+B,CAAC,EAAE,EAAE,KAAK,EAAE;;AAG3E;;;;AAIG;AACH,IAAA,MAAM,UAAU,CAAC,MAAA,GAAqC,EAAE,EAAA;QACtD,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AACzC,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACjB,MAAM,KAAK,CAAC,CAAA,0CAAA,EAA6C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAE,CAAA,CAAC;;AAEpF,QAAA,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;;;;ACrE3B;AACM,MAAgB,4BACpB,SAAQ,gBAAgB,CAAA;;AAaxB,IAAA,MAAM,YAAY,GAAA;QAChB,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAE/F,QAAA,IAAI,CAAC,UAAU,IAAI,WAAW,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC,aAAa,EAAE;;;;AAK/B,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,IAAI,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE;AAC/B,YAAA,MAAM,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,0BAA0B,EAAE,CAAC;;AAElF,YAAA,MAAM,IAAI,CAAC,cAAc,EAAE;;;;AAK/B,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,OAAO,CAAC,MAAM,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI;;AAGnD;;;AAGG;AACH,IAAA,MAAM,WAAW,CAAC,MAAA,GAAiC,EAAE,EAAA;AACnD,QAAA,OAAO,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,0BAA0B,EAAE,CAAC;;AAE7E;AAED;AACO,eAAe,aAAa,CAAC,IAA0B,EAAA;IAC5D,OAAO,CAAC,MAAM,IAAI,EAAE,YAAY,CAAC,mBAAmB,CAAC;AACvD;AAEA;AACO,eAAe,aAAa,CACjC,UAAkC,EAClC,eAA+B,EAAA;;;;;;;AAQ/B,IAAA,MAAM,gBAAgB,GAAG,CAAA,CAAA,EAAI,MAAM,UAAU,WAAW;AACxD,IAAA,OAAO,CAAC,MAAM,eAAe,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,EAAE,KAAK,EAAE;AACvE;AAEA;AACO,eAAe,WAAW,CAC/B,MAA8B,EAC9B,IAA0B,EAC1B,eAA+B,EAAA;AAE/B,IAAA,MAAM,UAAU,GAAG,MAAM,aAAa,CAAC,IAAI,CAAC;IAE5C,IAAI,CAAC,UAAU,EAAE;AACf,QAAA,MAAM,KAAK,CAAC,CAA2C,yCAAA,CAAA,CAAC;;AAG1D,IAAA,OAAO,eAAe,CAAC,UAAU,CAC/B,kBAAkB,CAAC,IAAI,CAAC;AACtB,QAAA,GAAG,MAAM;QACT,QAAQ,EAAE,CAAI,CAAA,EAAA,UAAU,CAAE,CAAA;KAC3B,CAAC,CACH,EAAE;AACL;;ACtFA;AACM,MAAO,yBACX,SAAQ,6BAA6B,CAAA;AAGrC,IAAA,OAAO,YAAY,GAAG,uBAAuB;AAE7C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAAyC,EAAE,EAAA;AAE3C,QAAA,OAAO,iBAAiB,CAAC,yBAAyB,EAAE,OAAO,CAAC;;;AAI9D,IAAA,MAAM,cAAc,GAAA;;AAElB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,OAAO,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,IAAI;;;AAIvD,IAAA,MAAM,YAAY,GAAA;QAChB,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;AAE/F,QAAA,IAAI,CAAC,UAAU,IAAI,WAAW,EAAE;;AAE9B,YAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;AAC9B,YAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,EAAC,GAAG,EAAE,IAAI,EAAC,EAAE,OAAO,CAAC,UAAU,CAAC;;;;AAKzD,IAAA,MAAM,aAAa,GAAA;AACjB,QAAA,IAAI,MAAM,IAAI,CAAC,cAAc,EAAE,EAAE;AAC/B,YAAA,MAAM,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,0BAA0B,EAAE,CAAC;;AAElF,YAAA,MAAM,IAAI,CAAC,cAAc,EAAE;;;;AAK/B,IAAA,MAAM,WAAW,GAAA;AACf,QAAA,OAAO,CAAC,MAAM,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,KAAK,IAAI;;AAGnD;;;AAGG;AACH,IAAA,MAAM,WAAW,CAAC,MAAA,GAAiC,EAAE,EAAA;AACnD,QAAA,OAAO,WAAW,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,0BAA0B,EAAE,CAAC;;;;AC3D9E;AACM,MAAO,mBAAoB,SAAQ,6BAA6B,CAAA;AACpE,IAAA,OAAO,YAAY,GAAG,iBAAiB;AAEvC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAyC,EAAE,EAAA;AACrD,QAAA,OAAO,iBAAiB,CAAC,mBAAmB,EAAE,OAAO,CAAC;;;AAI1D;AACM,MAAO,iBAAkB,SAAQ,6BAA6B,CAAA;AAClE,IAAA,OAAO,YAAY,GAAG,eAAe;AAErC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAyC,EAAE,EAAA;AACrD,QAAA,OAAO,iBAAiB,CAAC,iBAAiB,EAAE,OAAO,CAAC;;;AAIxD;AACM,MAAO,wBAAyB,SAAQ,4BAA4B,CAAA;AACxE,IAAA,OAAO,YAAY,GAAG,uBAAuB;IAE5B,sBAAsB,GAAG,qBAAqB;AAE/D;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAAwC,EAAE,EAAA;AAE1C,QAAA,OAAO,IAAI,gBAAgB,CAAC,wBAAwB,EAAE,OAAO;aAC1D,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAChD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;AAE1D,aAAA,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAAI;YACpD,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;AAClE,SAAC,CAAC;;;AAIN,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;AACnD,YAAA,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;AACpD,YAAA,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YAClD,IAAI,CAAC,YAAY,EAAE;AACpB,SAAA,CAAC;AAEF,QAAA,OAAO,KAAK,GAAG,CAAA,EAAG,GAAG,GAAG,CAAA,CAAA,EAAI,SAAS,CAAA,CAAA,EAAI,GAAG,CAAE,CAAA,GAAG,EAAE,EAAE;;;AAIvD,IAAA,MAAM,aAAa,GAAA;;AAEjB,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,mBAAmB,CAAC,EAAE;;;AAI/C,IAAA,MAAM,WAAW,GAAA;;AAEf,QAAA,OAAO,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,EAAE;;;AAI7C,IAAA,MAAM,QAAQ,GAAA;;AAEZ,QAAA,MAAM,mBAAmB,GAAG,IAAI,CAAC,0BAA0B,EAAE;AAC7D,QAAA,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC;AACzE,QAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC;QAE3D,IAAI,OAAO,EAAE;;;AAGX,YAAA,MAAM,OAAO,GAAG,MAAM,mBAAmB,CAAC,kBAAkB,CAC1D,CAAA,EAAG,IAAI,CAAC,sBAAsB,CAAQ,KAAA,EAAA,OAAO,CAAI,EAAA,CAAA,CAClD,EAAE;AACH,YAAA,OAAO,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI;;aACjC,IAAI,MAAM,EAAE;;;AAGjB,YAAA,MAAM,OAAO,GAAG,MAAM,mBAAmB,CAAC,kBAAkB,CAC1D,CAAA,EAAG,IAAI,CAAC,sBAAsB,CAAS,MAAA,EAAA,MAAM,CAAI,EAAA,CAAA,CAClD,EAAE;AACH,YAAA,OAAO,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,GAAG,IAAI;;AAExC,QAAA,OAAO,IAAI;;;AAIb,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,UAAU,CAAC,iCAAiC,CAAC,EAAE,EAAE,IAAI,EAAE;;;AAI5E,IAAA,MAAM,UAAU,GAAA;;QAEd,MAAM,CAAC,aAAa,EAAE,WAAW,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;AACxD,YAAA,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;AACtD,YAAA,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;AACrD,SAAA,CAAC;QAEF,OAAO,aAAa,IAAI,WAAW;;;AAIrC,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,+BAA+B,CAAC;;;AAItE,IAAA,MAAM,cAAc,GAAA;;;AAGlB,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE;QAC3D,OAAO,CAAC,MAAM,SAAS,CAAC,YAAY,CAAC,WAAW,CAAC,KAAK,IAAI;;AAGlD,IAAA,MAAM,aAAa,GAAA;;AAE3B,QAAA,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,aAAa,EAAE,EAAE,IAAI,EAAE;AAC3D,QAAA,OAAO,SAAS,CAAC,QAAQ,CAAC,EAAC,GAAG,EAAE,IAAI,EAAC,EAAE,OAAO,CAAC,UAAU,CAAC;;;;;;"}