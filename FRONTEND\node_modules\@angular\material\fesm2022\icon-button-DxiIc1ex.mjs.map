{"version": 3, "file": "icon-button-DxiIc1ex.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/button/button-base.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/button/icon-button.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/button/icon-button.html"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {FocusMonitor, FocusOrigin} from '@angular/cdk/a11y';\nimport {\n  AfterViewInit,\n  booleanAttribute,\n  Directive,\n  ElementRef,\n  inject,\n  InjectionToken,\n  Input,\n  NgZone,\n  numberAttribute,\n  OnDestroy,\n  Renderer2,\n} from '@angular/core';\nimport {_animationsDisabled, _StructuralStylesLoader, MatRippleLoader, ThemePalette} from '../core';\nimport {_CdkPrivateStyleLoader} from '@angular/cdk/private';\n\n/**\n * Possible appearances for a `MatButton`.\n * See https://m3.material.io/components/buttons/overview\n */\nexport type MatButtonAppearance = 'text' | 'filled' | 'elevated' | 'outlined' | 'tonal';\n\n/** Object that can be used to configure the default options for the button component. */\nexport interface MatButtonConfig {\n  /** Whether disabled buttons should be interactive. */\n  disabledInteractive?: boolean;\n\n  /** Default palette color to apply to buttons. */\n  color?: ThemePalette;\n\n  /** Default appearance for plain buttons (not icon buttons or FABs). */\n  defaultAppearance?: MatButtonAppearance;\n}\n\n/** Injection token that can be used to provide the default options the button component. */\nexport const MAT_BUTTON_CONFIG = new InjectionToken<MatButtonConfig>('MAT_BUTTON_CONFIG');\n\nfunction transformTabIndex(value: unknown): number | undefined {\n  return value == null ? undefined : numberAttribute(value);\n}\n\n/** Base class for all buttons. */\n@Directive({\n  host: {\n    // Add a class that applies to all buttons. This makes it easier to target if somebody\n    // wants to target all Material buttons.\n    'class': 'mat-mdc-button-base',\n    '[class]': 'color ? \"mat-\" + color : \"\"',\n    '[attr.disabled]': '_getDisabledAttribute()',\n    '[attr.aria-disabled]': '_getAriaDisabled()',\n    '[attr.tabindex]': '_getTabIndex()',\n    '[class.mat-mdc-button-disabled]': 'disabled',\n    '[class.mat-mdc-button-disabled-interactive]': 'disabledInteractive',\n    '[class.mat-unthemed]': '!color',\n    '[class._mat-animation-noopable]': '_animationsDisabled',\n  },\n})\nexport class MatButtonBase implements AfterViewInit, OnDestroy {\n  _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  protected _ngZone = inject(NgZone);\n  protected _animationsDisabled = _animationsDisabled();\n\n  protected readonly _config = inject(MAT_BUTTON_CONFIG, {optional: true});\n  private readonly _focusMonitor = inject(FocusMonitor);\n  private _cleanupClick: (() => void) | undefined;\n  private _renderer = inject(Renderer2);\n\n  /**\n   * Handles the lazy creation of the MatButton ripple.\n   * Used to improve initial load time of large applications.\n   */\n  protected _rippleLoader: MatRippleLoader = inject(MatRippleLoader);\n\n  /** Whether the button is set on an anchor node. */\n  protected _isAnchor: boolean;\n\n  /** Whether this button is a FAB. Used to apply the correct class on the ripple. */\n  protected _isFab = false;\n\n  /**\n   * Theme color of the button. This API is supported in M2 themes only, it has\n   * no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/button/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color?: string | null;\n\n  /** Whether the ripple effect is disabled or not. */\n  @Input({transform: booleanAttribute})\n  get disableRipple(): boolean {\n    return this._disableRipple;\n  }\n  set disableRipple(value: any) {\n    this._disableRipple = value;\n    this._updateRippleDisabled();\n  }\n  private _disableRipple: boolean = false;\n\n  /** Whether the button is disabled. */\n  @Input({transform: booleanAttribute})\n  get disabled(): boolean {\n    return this._disabled;\n  }\n  set disabled(value: any) {\n    this._disabled = value;\n    this._updateRippleDisabled();\n  }\n  private _disabled: boolean = false;\n\n  /** `aria-disabled` value of the button. */\n  @Input({transform: booleanAttribute, alias: 'aria-disabled'})\n  ariaDisabled: boolean | undefined;\n\n  /**\n   * Natively disabled buttons prevent focus and any pointer events from reaching the button.\n   * In some scenarios this might not be desirable, because it can prevent users from finding out\n   * why the button is disabled (e.g. via tooltip). This is also useful for buttons that may\n   * become disabled when activated, which would cause focus to be transferred to the document\n   * body instead of remaining on the button.\n   *\n   * Enabling this input will change the button so that it is styled to be disabled and will be\n   * marked as `aria-disabled`, but it will allow the button to receive events and focus.\n   *\n   * Note that by enabling this, you need to set the `tabindex` yourself if the button isn't\n   * meant to be tabbable and you have to prevent the button action (e.g. form submissions).\n   */\n  @Input({transform: booleanAttribute})\n  disabledInteractive: boolean;\n\n  /** Tab index for the button. */\n  @Input({transform: transformTabIndex})\n  tabIndex: number;\n\n  /**\n   * Backwards-compatibility input that handles pre-existing `[tabindex]` bindings.\n   * @docs-private\n   */\n  @Input({alias: 'tabindex', transform: transformTabIndex})\n  set _tabindex(value: number) {\n    this.tabIndex = value;\n  }\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    const element = this._elementRef.nativeElement;\n\n    this._isAnchor = element.tagName === 'A';\n    this.disabledInteractive = this._config?.disabledInteractive ?? false;\n    this.color = this._config?.color ?? null;\n    this._rippleLoader?.configureRipple(element, {className: 'mat-mdc-button-ripple'});\n  }\n\n  ngAfterViewInit() {\n    this._focusMonitor.monitor(this._elementRef, true);\n\n    // Some internal tests depend on the timing of this,\n    // otherwise we could bind it in the constructor.\n    if (this._isAnchor) {\n      this._setupAsAnchor();\n    }\n  }\n\n  ngOnDestroy() {\n    this._cleanupClick?.();\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n  }\n\n  /** Focuses the button. */\n  focus(origin: FocusOrigin = 'program', options?: FocusOptions): void {\n    if (origin) {\n      this._focusMonitor.focusVia(this._elementRef.nativeElement, origin, options);\n    } else {\n      this._elementRef.nativeElement.focus(options);\n    }\n  }\n\n  protected _getAriaDisabled() {\n    if (this.ariaDisabled != null) {\n      return this.ariaDisabled;\n    }\n\n    if (this._isAnchor) {\n      return this.disabled || null;\n    }\n\n    return this.disabled && this.disabledInteractive ? true : null;\n  }\n\n  protected _getDisabledAttribute() {\n    return this.disabledInteractive || !this.disabled ? null : true;\n  }\n\n  private _updateRippleDisabled(): void {\n    this._rippleLoader?.setDisabled(\n      this._elementRef.nativeElement,\n      this.disableRipple || this.disabled,\n    );\n  }\n\n  protected _getTabIndex() {\n    if (this._isAnchor) {\n      return this.disabled && !this.disabledInteractive ? -1 : this.tabIndex;\n    }\n    return this.tabIndex;\n  }\n\n  private _setupAsAnchor() {\n    this._cleanupClick = this._ngZone.runOutsideAngular(() =>\n      this._renderer.listen(this._elementRef.nativeElement, 'click', (event: Event) => {\n        if (this.disabled) {\n          event.preventDefault();\n          event.stopImmediatePropagation();\n        }\n      }),\n    );\n  }\n}\n\n// tslint:disable:variable-name\n/**\n * Anchor button base.\n */\nexport const MatAnchorBase = MatButtonBase;\nexport type MatAnchorBase = MatButtonBase;\n// tslint:enable:variable-name\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ChangeDetectionStrategy, Component, ViewEncapsulation} from '@angular/core';\nimport {MatButtonBase} from './button-base';\n\n/**\n * Material Design icon button component. This type of button displays a single interactive icon for\n * users to perform an action.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\n@Component({\n  selector: `button[mat-icon-button], a[mat-icon-button], button[matIconButton], a[matIconButton]`,\n  templateUrl: 'icon-button.html',\n  styleUrls: ['icon-button.css', 'button-high-contrast.css'],\n  host: {\n    'class': 'mdc-icon-button mat-mdc-icon-button',\n  },\n  exportAs: 'matButton, matAnchor',\n  encapsulation: ViewEncapsulation.None,\n  changeDetection: ChangeDetectionStrategy.OnPush,\n})\nexport class MatIconButton extends MatButtonBase {\n  constructor(...args: unknown[]);\n\n  constructor() {\n    super();\n    this._rippleLoader.configureRipple(this._elementRef.nativeElement, {centered: true});\n  }\n}\n\n// tslint:disable:variable-name\n/**\n * Material Design icon button component for anchor elements. This button displays a single\n * interaction icon that allows users to navigate across different routes or pages.\n * See https://material.io/develop/web/components/buttons/icon-buttons/\n */\nexport const MatIconAnchor = MatIconButton;\nexport type MatIconAnchor = MatIconButton;\n// tslint:enable:variable-name\n", "<span class=\"mat-mdc-button-persistent-ripple mdc-icon-button__ripple\"></span>\n\n<ng-content></ng-content>\n\n<!--\n  The indicator can't be directly on the button, because MDC uses ::before for high contrast\n  indication and it can't be on the ripple, because it has a border radius and overflow: hidden.\n-->\n<span class=\"mat-focus-indicator\"></span>\n\n<span class=\"mat-mdc-button-touch-target\"></span>\n"], "names": [], "mappings": ";;;;;;;;AA2CA;MACa,iBAAiB,GAAG,IAAI,cAAc,CAAkB,mBAAmB;AAExF,SAAS,iBAAiB,CAAC,KAAc,EAAA;AACvC,IAAA,OAAO,KAAK,IAAI,IAAI,GAAG,SAAS,GAAG,eAAe,CAAC,KAAK,CAAC;AAC3D;AAEA;MAgBa,aAAa,CAAA;AACxB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AAC/C,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IACxB,mBAAmB,GAAG,mBAAmB,EAAE;IAElC,OAAO,GAAG,MAAM,CAAC,iBAAiB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACvD,IAAA,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;AAC7C,IAAA,aAAa;AACb,IAAA,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;AAErC;;;AAGG;AACO,IAAA,aAAa,GAAoB,MAAM,CAAC,eAAe,CAAC;;AAGxD,IAAA,SAAS;;IAGT,MAAM,GAAG,KAAK;AAExB;;;;;;AAMG;AACM,IAAA,KAAK;;AAGd,IAAA,IACI,aAAa,GAAA;QACf,OAAO,IAAI,CAAC,cAAc;;IAE5B,IAAI,aAAa,CAAC,KAAU,EAAA;AAC1B,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK;QAC3B,IAAI,CAAC,qBAAqB,EAAE;;IAEtB,cAAc,GAAY,KAAK;;AAGvC,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAU,EAAA;AACrB,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC,qBAAqB,EAAE;;IAEtB,SAAS,GAAY,KAAK;;AAIlC,IAAA,YAAY;AAEZ;;;;;;;;;;;;AAYG;AAEH,IAAA,mBAAmB;;AAInB,IAAA,QAAQ;AAER;;;AAGG;IACH,IACI,SAAS,CAAC,KAAa,EAAA;AACzB,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;;AAKvB,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAC5D,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,aAAa;QAE9C,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,OAAO,KAAK,GAAG;QACxC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,OAAO,EAAE,mBAAmB,IAAI,KAAK;QACrE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,EAAE,KAAK,IAAI,IAAI;AACxC,QAAA,IAAI,CAAC,aAAa,EAAE,eAAe,CAAC,OAAO,EAAE,EAAC,SAAS,EAAE,uBAAuB,EAAC,CAAC;;IAGpF,eAAe,GAAA;QACb,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC;;;AAIlD,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,cAAc,EAAE;;;IAIzB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,aAAa,IAAI;QACtB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;QACnD,IAAI,CAAC,aAAa,EAAE,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;;;AAInE,IAAA,KAAK,CAAC,MAAA,GAAsB,SAAS,EAAE,OAAsB,EAAA;QAC3D,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,MAAM,EAAE,OAAO,CAAC;;aACvE;YACL,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,KAAK,CAAC,OAAO,CAAC;;;IAIvC,gBAAgB,GAAA;AACxB,QAAA,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,EAAE;YAC7B,OAAO,IAAI,CAAC,YAAY;;AAG1B,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI;;AAG9B,QAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,GAAG,IAAI;;IAGtD,qBAAqB,GAAA;AAC7B,QAAA,OAAO,IAAI,CAAC,mBAAmB,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI;;IAGzD,qBAAqB,GAAA;AAC3B,QAAA,IAAI,CAAC,aAAa,EAAE,WAAW,CAC7B,IAAI,CAAC,WAAW,CAAC,aAAa,EAC9B,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,QAAQ,CACpC;;IAGO,YAAY,GAAA;AACpB,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ;;QAExE,OAAO,IAAI,CAAC,QAAQ;;IAGd,cAAc,GAAA;AACpB,QAAA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAClD,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC,KAAY,KAAI;AAC9E,YAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,KAAK,CAAC,cAAc,EAAE;gBACtB,KAAK,CAAC,wBAAwB,EAAE;;SAEnC,CAAC,CACH;;uGAjKQ,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAb,aAAa,EAAA,YAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,aAAA,EAAA,CAAA,eAAA,EAAA,eAAA,EAgCL,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAWhB,gBAAgB,CAAA,EAAA,YAAA,EAAA,CAAA,eAAA,EAAA,cAAA,EAWhB,gBAAgB,CAAA,EAAA,mBAAA,EAAA,CAAA,qBAAA,EAAA,qBAAA,EAgBhB,gBAAgB,CAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EA1F5B,iBAAiB,CAAA,EAAA,SAAA,EAAA,CAAA,UAAA,EAAA,WAAA,EAAjB,iBAAiB,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,iCAAA,EAAA,eAAA,EAAA,yBAAA,EAAA,oBAAA,EAAA,oBAAA,EAAA,eAAA,EAAA,gBAAA,EAAA,+BAAA,EAAA,UAAA,EAAA,2CAAA,EAAA,qBAAA,EAAA,oBAAA,EAAA,QAAA,EAAA,+BAAA,EAAA,qBAAA,EAAA,EAAA,cAAA,EAAA,qBAAA,EAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAoBb,aAAa,EAAA,UAAA,EAAA,CAAA;kBAfzB,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,IAAI,EAAE;;;AAGJ,wBAAA,OAAO,EAAE,qBAAqB;AAC9B,wBAAA,SAAS,EAAE,6BAA6B;AACxC,wBAAA,iBAAiB,EAAE,yBAAyB;AAC5C,wBAAA,sBAAsB,EAAE,oBAAoB;AAC5C,wBAAA,iBAAiB,EAAE,gBAAgB;AACnC,wBAAA,iCAAiC,EAAE,UAAU;AAC7C,wBAAA,6CAA6C,EAAE,qBAAqB;AACpE,wBAAA,sBAAsB,EAAE,QAAQ;AAChC,wBAAA,iCAAiC,EAAE,qBAAqB;AACzD,qBAAA;AACF,iBAAA;wDA8BU,KAAK,EAAA,CAAA;sBAAb;gBAIG,aAAa,EAAA,CAAA;sBADhB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAYhC,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAYpC,YAAY,EAAA,CAAA;sBADX,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,SAAS,EAAE,gBAAgB,EAAE,KAAK,EAAE,eAAe,EAAC;gBAiB5D,mBAAmB,EAAA,CAAA;sBADlB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKpC,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,iBAAiB,EAAC;gBAQjC,SAAS,EAAA,CAAA;sBADZ,KAAK;AAAC,gBAAA,IAAA,EAAA,CAAA,EAAC,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,iBAAiB,EAAC;;;ACxI1D;;;;AAIG;AAYG,MAAO,aAAc,SAAQ,aAAa,CAAA;AAG9C,IAAA,WAAA,GAAA;AACE,QAAA,KAAK,EAAE;AACP,QAAA,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;uGAL3E,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;AAAb,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,IAAA,EAAA,aAAa,8QC3B1B,kaAWA,EAAA,MAAA,EAAA,CAAA,06HAAA,EAAA,wXAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FDgBa,aAAa,EAAA,UAAA,EAAA,CAAA;kBAXzB,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oFAAA,CAAsF,EAG1F,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,qCAAqC;qBAC/C,EACS,QAAA,EAAA,sBAAsB,iBACjB,iBAAiB,CAAC,IAAI,EACpB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAAA,QAAA,EAAA,kaAAA,EAAA,MAAA,EAAA,CAAA,06HAAA,EAAA,wXAAA,CAAA,EAAA;;AAWjD;AACA;;;;AAIG;AACI,MAAM,aAAa,GAAG;;;;"}