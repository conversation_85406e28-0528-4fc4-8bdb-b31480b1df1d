{"version": 3, "file": "css-pixel-value-C_HEqLhI.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/coercion/css-pixel-value.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Coerces a value to a CSS pixel value. */\nexport function coerceCssPixelValue(value: any): string {\n  if (value == null) {\n    return '';\n  }\n\n  return typeof value === 'string' ? value : `${value}px`;\n}\n"], "names": [], "mappings": "AAQA;AACM,SAAU,mBAAmB,CAAC,KAAU,EAAA;AAC5C,IAAA,IAAI,KAAK,IAAI,IAAI,EAAE;AACjB,QAAA,OAAO,EAAE;;AAGX,IAAA,OAAO,OAAO,KAAK,KAAK,QAAQ,GAAG,KAAK,GAAG,CAAG,EAAA,KAAK,IAAI;AACzD;;;;"}