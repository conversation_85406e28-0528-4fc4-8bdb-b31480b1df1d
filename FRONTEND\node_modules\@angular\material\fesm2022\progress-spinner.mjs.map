{"version": 3, "file": "progress-spinner.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/progress-spinner/progress-spinner.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/progress-spinner/progress-spinner.html", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/progress-spinner/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ElementRef,\n  InjectionToken,\n  Input,\n  ViewChild,\n  ViewEncapsulation,\n  numberAttribute,\n  inject,\n} from '@angular/core';\nimport {_animationsDisabled, ThemePalette} from '../core';\nimport {NgTemplateOutlet} from '@angular/common';\n\n/** Possible mode for a progress spinner. */\nexport type ProgressSpinnerMode = 'determinate' | 'indeterminate';\n\n/** Default `mat-progress-spinner` options that can be overridden. */\nexport interface MatProgressSpinnerDefaultOptions {\n  /**\n   * Default theme color of the progress spinner. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-spinner/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color?: ThemePalette;\n  /** Diameter of the spinner. */\n  diameter?: number;\n  /** Width of the spinner's stroke. */\n  strokeWidth?: number;\n  /**\n   * Whether the animations should be force to be enabled, ignoring if the current environment is\n   * using NoopAnimationsModule.\n   */\n  _forceAnimations?: boolean;\n}\n\n/** Injection token to be used to override the default options for `mat-progress-spinner`. */\nexport const MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS =\n  new InjectionToken<MatProgressSpinnerDefaultOptions>('mat-progress-spinner-default-options', {\n    providedIn: 'root',\n    factory: MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY,\n  });\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS_FACTORY(): MatProgressSpinnerDefaultOptions {\n  return {diameter: BASE_SIZE};\n}\n\n/**\n * Base reference size of the spinner.\n */\nconst BASE_SIZE = 100;\n\n/**\n * Base reference stroke width of the spinner.\n */\nconst BASE_STROKE_WIDTH = 10;\n\n@Component({\n  selector: 'mat-progress-spinner, mat-spinner',\n  exportAs: 'matProgressSpinner',\n  host: {\n    'role': 'progressbar',\n    'class': 'mat-mdc-progress-spinner mdc-circular-progress',\n    // set tab index to -1 so screen readers will read the aria-label\n    // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n    'tabindex': '-1',\n    '[class]': '\"mat-\" + color',\n    '[class._mat-animation-noopable]': `_noopAnimations`,\n    '[class.mdc-circular-progress--indeterminate]': 'mode === \"indeterminate\"',\n    '[style.width.px]': 'diameter',\n    '[style.height.px]': 'diameter',\n    '[style.--mat-progress-spinner-size]': 'diameter + \"px\"',\n    '[style.--mat-progress-spinner-active-indicator-width]': 'diameter + \"px\"',\n    '[attr.aria-valuemin]': '0',\n    '[attr.aria-valuemax]': '100',\n    '[attr.aria-valuenow]': 'mode === \"determinate\" ? value : null',\n    '[attr.mode]': 'mode',\n  },\n  templateUrl: 'progress-spinner.html',\n  styleUrl: 'progress-spinner.css',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  imports: [NgTemplateOutlet],\n})\nexport class MatProgressSpinner {\n  readonly _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n\n  /** Whether the _mat-animation-noopable class should be applied, disabling animations.  */\n  _noopAnimations: boolean;\n\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the progress spinner. This API is supported in M2 themes only, it\n   * has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/progress-spinner/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input()\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value: string | null | undefined) {\n    this._color = value;\n  }\n  private _color: string | null | undefined;\n  private _defaultColor: ThemePalette = 'primary';\n\n  /** The element of the determinate spinner. */\n  @ViewChild('determinateSpinner') _determinateCircle: ElementRef<HTMLElement>;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const defaults = inject<MatProgressSpinnerDefaultOptions>(MAT_PROGRESS_SPINNER_DEFAULT_OPTIONS);\n\n    this._noopAnimations = _animationsDisabled() && !!defaults && !defaults._forceAnimations;\n    this.mode =\n      this._elementRef.nativeElement.nodeName.toLowerCase() === 'mat-spinner'\n        ? 'indeterminate'\n        : 'determinate';\n\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n\n      if (defaults.diameter) {\n        this.diameter = defaults.diameter;\n      }\n\n      if (defaults.strokeWidth) {\n        this.strokeWidth = defaults.strokeWidth;\n      }\n    }\n  }\n\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  @Input() mode: ProgressSpinnerMode;\n\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  @Input({transform: numberAttribute})\n  get value(): number {\n    return this.mode === 'determinate' ? this._value : 0;\n  }\n  set value(v: number) {\n    this._value = Math.max(0, Math.min(100, v || 0));\n  }\n  private _value = 0;\n\n  /** The diameter of the progress spinner (will set width and height of svg). */\n  @Input({transform: numberAttribute})\n  get diameter(): number {\n    return this._diameter;\n  }\n  set diameter(size: number) {\n    this._diameter = size || 0;\n  }\n  private _diameter = BASE_SIZE;\n\n  /** Stroke width of the progress spinner. */\n  @Input({transform: numberAttribute})\n  get strokeWidth(): number {\n    return this._strokeWidth ?? this.diameter / 10;\n  }\n  set strokeWidth(value: number) {\n    this._strokeWidth = value || 0;\n  }\n  private _strokeWidth: number;\n\n  /** The radius of the spinner, adjusted for stroke width. */\n  _circleRadius(): number {\n    return (this.diameter - BASE_STROKE_WIDTH) / 2;\n  }\n\n  /** The view box of the spinner's svg element. */\n  _viewBox() {\n    const viewBox = this._circleRadius() * 2 + this.strokeWidth;\n    return `0 0 ${viewBox} ${viewBox}`;\n  }\n\n  /** The stroke circumference of the svg circle. */\n  _strokeCircumference(): number {\n    return 2 * Math.PI * this._circleRadius();\n  }\n\n  /** The dash offset of the svg circle. */\n  _strokeDashOffset() {\n    if (this.mode === 'determinate') {\n      return (this._strokeCircumference() * (100 - this._value)) / 100;\n    }\n    return null;\n  }\n\n  /** Stroke width of the circle in percent. */\n  _circleStrokeWidth() {\n    return (this.strokeWidth / this.diameter) * 100;\n  }\n}\n\n/**\n * @deprecated Import Progress Spinner instead. Note that the\n *    `mat-spinner` selector isn't deprecated.\n * @breaking-change 16.0.0\n */\n// tslint:disable-next-line:variable-name\nexport const MatSpinner = MatProgressSpinner;\n", "<ng-template #circle>\n  <svg [attr.viewBox]=\"_viewBox()\" class=\"mdc-circular-progress__indeterminate-circle-graphic\"\n       xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\">\n    <circle [attr.r]=\"_circleRadius()\"\n            [style.stroke-dasharray.px]=\"_strokeCircumference()\"\n            [style.stroke-dashoffset.px]=\"_strokeCircumference() / 2\"\n            [style.stroke-width.%]=\"_circleStrokeWidth()\"\n            cx=\"50%\" cy=\"50%\"/>\n  </svg>\n</ng-template>\n\n<!--\n  All children need to be hidden for screen readers in order to support ChromeVox.\n  More context in the issue: https://github.com/angular/components/issues/22165.\n-->\n<div class=\"mdc-circular-progress__determinate-container\" aria-hidden=\"true\" #determinateSpinner>\n  <svg [attr.viewBox]=\"_viewBox()\" class=\"mdc-circular-progress__determinate-circle-graphic\"\n       xmlns=\"http://www.w3.org/2000/svg\" focusable=\"false\">\n    <circle [attr.r]=\"_circleRadius()\"\n            [style.stroke-dasharray.px]=\"_strokeCircumference()\"\n            [style.stroke-dashoffset.px]=\"_strokeDashOffset()\"\n            [style.stroke-width.%]=\"_circleStrokeWidth()\"\n            class=\"mdc-circular-progress__determinate-circle\"\n            cx=\"50%\" cy=\"50%\"/>\n  </svg>\n</div>\n<!--TODO: figure out why there are 3 separate svgs-->\n<div class=\"mdc-circular-progress__indeterminate-container\" aria-hidden=\"true\">\n  <div class=\"mdc-circular-progress__spinner-layer\">\n    <div class=\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-left\">\n      <ng-container [ngTemplateOutlet]=\"circle\"></ng-container>\n    </div>\n    <div class=\"mdc-circular-progress__gap-patch\">\n      <ng-container [ngTemplateOutlet]=\"circle\"></ng-container>\n    </div>\n    <div class=\"mdc-circular-progress__circle-clipper mdc-circular-progress__circle-right\">\n      <ng-container [ngTemplateOutlet]=\"circle\"></ng-container>\n    </div>\n  </div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {<PERSON><PERSON><PERSON><PERSON><PERSON>pin<PERSON>, Mat<PERSON>pinner} from './progress-spinner';\n\n@NgModule({\n  imports: [Mat<PERSON>rogressSpinner, MatSpinner],\n  exports: [MatProgressSpinner, Mat<PERSON>pinner, MatCommonModule],\n})\nexport class MatProgressSpinnerModule {}\n"], "names": [], "mappings": ";;;;;;;;;AA8CA;MACa,oCAAoC,GAC/C,IAAI,cAAc,CAAmC,sCAAsC,EAAE;AAC3F,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,4CAA4C;AACtD,CAAA;AAEH;;;;AAIG;SACa,4CAA4C,GAAA;AAC1D,IAAA,OAAO,EAAC,QAAQ,EAAE,SAAS,EAAC;AAC9B;AAEA;;AAEG;AACH,MAAM,SAAS,GAAG,GAAG;AAErB;;AAEG;AACH,MAAM,iBAAiB,GAAG,EAAE;MA6Bf,kBAAkB,CAAA;AACpB,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;;AAGlE,IAAA,eAAe;;AAGf;;;;;;AAMG;AACH,IAAA,IACI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa;;IAE1C,IAAI,KAAK,CAAC,KAAgC,EAAA;AACxC,QAAA,IAAI,CAAC,MAAM,GAAG,KAAK;;AAEb,IAAA,MAAM;IACN,aAAa,GAAiB,SAAS;;AAGd,IAAA,kBAAkB;AAInD,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAmC,oCAAoC,CAAC;AAE/F,QAAA,IAAI,CAAC,eAAe,GAAG,mBAAmB,EAAE,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,gBAAgB;AACxF,QAAA,IAAI,CAAC,IAAI;YACP,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK;AACxD,kBAAE;kBACA,aAAa;QAEnB,IAAI,QAAQ,EAAE;AACZ,YAAA,IAAI,QAAQ,CAAC,KAAK,EAAE;gBAClB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,KAAK;;AAGlD,YAAA,IAAI,QAAQ,CAAC,QAAQ,EAAE;AACrB,gBAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ;;AAGnC,YAAA,IAAI,QAAQ,CAAC,WAAW,EAAE;AACxB,gBAAA,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW;;;;AAK7C;;;;;;AAMG;AACM,IAAA,IAAI;;AAGb,IAAA,IACI,KAAK,GAAA;AACP,QAAA,OAAO,IAAI,CAAC,IAAI,KAAK,aAAa,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC;;IAEtD,IAAI,KAAK,CAAC,CAAS,EAAA;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;;IAE1C,MAAM,GAAG,CAAC;;AAGlB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,IAAY,EAAA;AACvB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,IAAI,CAAC;;IAEpB,SAAS,GAAG,SAAS;;AAG7B,IAAA,IACI,WAAW,GAAA;QACb,OAAO,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE;;IAEhD,IAAI,WAAW,CAAC,KAAa,EAAA;AAC3B,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,IAAI,CAAC;;AAExB,IAAA,YAAY;;IAGpB,aAAa,GAAA;QACX,OAAO,CAAC,IAAI,CAAC,QAAQ,GAAG,iBAAiB,IAAI,CAAC;;;IAIhD,QAAQ,GAAA;AACN,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW;AAC3D,QAAA,OAAO,CAAO,IAAA,EAAA,OAAO,CAAI,CAAA,EAAA,OAAO,EAAE;;;IAIpC,oBAAoB,GAAA;QAClB,OAAO,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,aAAa,EAAE;;;IAI3C,iBAAiB,GAAA;AACf,QAAA,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE;AAC/B,YAAA,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG;;AAElE,QAAA,OAAO,IAAI;;;IAIb,kBAAkB,GAAA;QAChB,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,IAAI,GAAG;;uGAtHtC,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,mCAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,IAAA,EAAA,MAAA,EAAA,KAAA,EAAA,CAAA,OAAA,EAAA,OAAA,EA+DV,eAAe,CAUf,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAAA,eAAe,+CAUf,eAAe,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,aAAA,EAAA,UAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,OAAA,EAAA,kBAAA,EAAA,+BAAA,EAAA,iBAAA,EAAA,4CAAA,EAAA,4BAAA,EAAA,gBAAA,EAAA,UAAA,EAAA,iBAAA,EAAA,UAAA,EAAA,mCAAA,EAAA,mBAAA,EAAA,qDAAA,EAAA,mBAAA,EAAA,oBAAA,EAAA,GAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,oBAAA,EAAA,yCAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,cAAA,EAAA,gDAAA,EAAA,EAAA,WAAA,EAAA,CAAA,EAAA,YAAA,EAAA,oBAAA,EAAA,KAAA,EAAA,IAAA,EAAA,SAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,WAAA,EAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,CAAA,oBAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECtLpC,28DAwCA,EAAA,MAAA,EAAA,CAAA,irIAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EDyDY,gBAAgB,EAAA,QAAA,EAAA,oBAAA,EAAA,MAAA,EAAA,CAAA,yBAAA,EAAA,kBAAA,EAAA,0BAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAEf,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBA3B9B,SAAS;+BACE,mCAAmC,EAAA,QAAA,EACnC,oBAAoB,EACxB,IAAA,EAAA;AACJ,wBAAA,MAAM,EAAE,aAAa;AACrB,wBAAA,OAAO,EAAE,gDAAgD;;;AAGzD,wBAAA,UAAU,EAAE,IAAI;AAChB,wBAAA,SAAS,EAAE,gBAAgB;AAC3B,wBAAA,iCAAiC,EAAE,CAAiB,eAAA,CAAA;AACpD,wBAAA,8CAA8C,EAAE,0BAA0B;AAC1E,wBAAA,kBAAkB,EAAE,UAAU;AAC9B,wBAAA,mBAAmB,EAAE,UAAU;AAC/B,wBAAA,qCAAqC,EAAE,iBAAiB;AACxD,wBAAA,uDAAuD,EAAE,iBAAiB;AAC1E,wBAAA,sBAAsB,EAAE,GAAG;AAC3B,wBAAA,sBAAsB,EAAE,KAAK;AAC7B,wBAAA,sBAAsB,EAAE,uCAAuC;AAC/D,wBAAA,aAAa,EAAE,MAAM;qBACtB,EAGgB,eAAA,EAAA,uBAAuB,CAAC,MAAM,EAChC,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAAA,OAAA,EAC5B,CAAC,gBAAgB,CAAC,EAAA,QAAA,EAAA,28DAAA,EAAA,MAAA,EAAA,CAAA,irIAAA,CAAA,EAAA;wDAiBvB,KAAK,EAAA,CAAA;sBADR;gBAWgC,kBAAkB,EAAA,CAAA;sBAAlD,SAAS;uBAAC,oBAAoB;gBAmCtB,IAAI,EAAA,CAAA;sBAAZ;gBAIG,KAAK,EAAA,CAAA;sBADR,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAW/B,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAW/B,WAAW,EAAA,CAAA;sBADd,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;;AAuCrC;;;;AAIG;AACH;AACO,MAAM,UAAU,GAAG;;MEnNb,wBAAwB,CAAA;uGAAxB,wBAAwB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAxB,wBAAwB,EAAA,OAAA,EAAA,CAHzB,kBAAkB,EAAE,UAAU,aAC9B,kBAAkB,EAAE,UAAU,EAAE,eAAe,CAAA,EAAA,CAAA;AAE9C,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,wBAAwB,YAFO,eAAe,CAAA,EAAA,CAAA;;2FAE9C,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBAJpC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,kBAAkB,EAAE,UAAU,CAAC;AACzC,oBAAA,OAAO,EAAE,CAAC,kBAAkB,EAAE,UAAU,EAAE,eAAe,CAAC;AAC3D,iBAAA;;;;;"}