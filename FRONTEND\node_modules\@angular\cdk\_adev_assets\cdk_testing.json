{"repo": "angular/components", "moduleLabel": "@angular/cdk/testing", "moduleName": "@angular/cdk/testing", "normalizedModuleName": "angular_cdk_testing", "entries": [{"name": "getNoKeysSpecifiedError", "signatures": [{"name": "getNoKeysSpecifiedError", "entryType": "function", "description": "Returns an error which reports that no keys have been specified.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "docs-private", "comment": ""}], "params": [], "rawComment": "/**\n * Returns an error which reports that no keys have been specified.\n * @docs-private\n */", "returnType": "Error"}], "implementation": {"params": [], "isNewType": false, "returnType": "Error", "generics": [], "name": "getNoKeysSpecifiedError", "description": "Returns an error which reports that no keys have been specified.", "entryType": "function", "jsdocTags": [{"name": "docs-private", "comment": ""}], "rawComment": "/**\n * Returns an error which reports that no keys have been specified.\n * @docs-private\n */"}, "entryType": "function", "description": "Returns an error which reports that no keys have been specified.", "jsdocTags": [{"name": "docs-private", "comment": ""}], "rawComment": "/**\n * Returns an error which reports that no keys have been specified.\n * @docs-private\n */", "source": {"filePath": "/src/cdk/testing/test-element-errors.ts", "startLine": 13, "endLine": 15}}, {"name": "ElementDimensions", "isAbstract": false, "entryType": "interface", "members": [{"name": "top", "type": "number", "memberType": "property", "memberTags": [], "description": "The distance from the top of the viewport in pixels", "jsdocTags": []}, {"name": "left", "type": "number", "memberType": "property", "memberTags": [], "description": "The distance from the left of the viewport in pixels", "jsdocTags": []}, {"name": "width", "type": "number", "memberType": "property", "memberTags": [], "description": "The width of the element in pixels", "jsdocTags": []}, {"name": "height", "type": "number", "memberType": "property", "memberTags": [], "description": "The height of the element in pixels", "jsdocTags": []}], "generics": [], "description": "Dimensions for element size and its position relative to the viewport.", "jsdocTags": [], "rawComment": "/**\n * Dimensions for element size and its position relative to the viewport.\n */", "implements": [], "source": {"filePath": "/src/cdk/testing/element-dimensions.ts", "startLine": 12, "endLine": 21}}, {"name": "AutoChangeDetectionStatus", "isAbstract": false, "entryType": "interface", "members": [{"name": "isDisabled", "type": "boolean", "memberType": "property", "memberTags": [], "description": "Whether auto change detection is disabled.", "jsdocTags": []}, {"name": "onDetectChangesNow", "type": "(() => void) | undefined", "memberType": "property", "memberTags": ["optional"], "description": "An optional callback, if present it indicates that change detection should be run immediately,\nwhile handling the status change. The callback should then be called as soon as change\ndetection is done.", "jsdocTags": []}], "generics": [], "description": "The status of the test harness auto change detection. If not diabled test harnesses will\nautomatically trigger change detection after every action (such as a click) and before every read\n(such as getting the text of an element).", "jsdocTags": [], "rawComment": "/**\n * The status of the test harness auto change detection. If not diabled test harnesses will\n * automatically trigger change detection after every action (such as a click) and before every read\n * (such as getting the text of an element).\n */", "implements": [], "source": {"filePath": "src/cdk/testing/change-detection.ts", "startLine": 16, "endLine": 25}}, {"name": "Modifier<PERSON>eys", "isAbstract": false, "entryType": "interface", "members": [{"name": "control", "type": "boolean | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "alt", "type": "boolean | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "shift", "type": "boolean | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}, {"name": "meta", "type": "boolean | undefined", "memberType": "property", "memberTags": ["optional"], "description": "", "jsdocTags": []}], "generics": [], "description": "Modifier keys that may be held while typing.", "jsdocTags": [], "rawComment": "/** Modifier keys that may be held while typing. */", "implements": [], "source": {"filePath": "/src/cdk/testing/test-element.ts", "startLine": 12, "endLine": 17}}, {"name": "AsyncFactoryFn", "type": "() => Promise<T>", "entryType": "type_alias", "generics": [{"name": "T"}], "rawComment": "/**\n * An async function that returns a promise when called.\n * @deprecated This was just an alias for `() => Promise<T>`. Use that instead.\n * @breaking-change 21.0.0 Remove this alias.\n * @docs-private\n */", "description": "An async function that returns a promise when called.", "jsdocTags": [{"name": "deprecated", "comment": "This was just an alias for `() => Promise<T>`. Use that instead."}, {"name": "breaking-change", "comment": "21.0.0 Remove this alias."}, {"name": "docs-private", "comment": ""}], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 18, "endLine": 18}}, {"name": "EventData", "type": "| string\n  | number\n  | boolean\n  | Function\n  | undefined\n  | null\n  | EventData[]\n  | {[key: string]: EventData}", "entryType": "type_alias", "generics": [], "rawComment": "/** Data that can be attached to a custom event dispatched from a `TestElement`. */", "description": "Data that can be attached to a custom event dispatched from a `TestElement`.", "jsdocTags": [], "source": {"filePath": "/src/cdk/testing/test-element.ts", "startLine": 20, "endLine": 28}}, {"name": "AsyncPredicate", "type": "(item: T) => Promise<boolean>", "entryType": "type_alias", "generics": [{"name": "T"}], "rawComment": "/** An async function that takes an item and returns a boolean promise */", "description": "An async function that takes an item and returns a boolean promise", "jsdocTags": [], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 21, "endLine": 21}}, {"name": "<PERSON><PERSON><PERSON>", "entryType": "enum", "members": [{"name": "BACKSPACE", "type": "TestKey.BACKSPACE", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "TAB", "type": "TestKey.TAB", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "ENTER", "type": "TestKey.ENTER", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "SHIFT", "type": "TestKey.SHIFT", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "CONTROL", "type": "TestKey.CONTROL", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "ALT", "type": "TestKey.ALT", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "ESCAPE", "type": "TestKey.ESCAPE", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "PAGE_UP", "type": "TestKey.PAGE_UP", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "PAGE_DOWN", "type": "TestKey.PAGE_DOWN", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "END", "type": "TestKey.END", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "HOME", "type": "TestKey.HOME", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "LEFT_ARROW", "type": "TestKey.LEFT_ARROW", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "UP_ARROW", "type": "TestKey.UP_ARROW", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "RIGHT_ARROW", "type": "TestKey.RIGHT_ARROW", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "DOWN_ARROW", "type": "TestKey.DOWN_ARROW", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "INSERT", "type": "TestKey.INSERT", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "DELETE", "type": "TestKey.DELETE", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F1", "type": "TestKey.F1", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F2", "type": "TestKey.F2", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F3", "type": "TestKey.F3", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F4", "type": "TestKey.F4", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F5", "type": "TestKey.F5", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F6", "type": "TestKey.F6", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F7", "type": "TestKey.F7", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F8", "type": "TestKey.F8", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F9", "type": "TestKey.F9", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F10", "type": "TestKey.F10", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F11", "type": "TestKey.F11", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "F12", "type": "TestKey.F12", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "META", "type": "TestKey.META", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}, {"name": "COMMA", "type": "TestKey.COMMA", "value": "", "memberType": "enum_item", "jsdocTags": [], "description": "", "memberTags": []}], "rawComment": "/** An enum of non-text keys that can be used with the `sendKeys` method. */", "description": "An enum of non-text keys that can be used with the `sendKeys` method.", "jsdocTags": [], "source": {"filePath": "/src/cdk/testing/test-element.ts", "startLine": 37, "endLine": 69}}, {"name": "AsyncOptionPredicate", "type": "(item: T, option: O) => Promise<boolean>", "entryType": "type_alias", "generics": [{"name": "T"}, {"name": "O"}], "rawComment": "/** An async function that takes an item and an option value and returns a boolean promise. */", "description": "An async function that takes an item and an option value and returns a boolean promise.", "jsdocTags": [], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 24, "endLine": 24}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "| ComponentHarnessConstructor<T>\n  | HarnessPredicate<T>", "entryType": "type_alias", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "rawComment": "/**\n * A query for a `ComponentHarness`, which is expressed as either a `ComponentHarnessConstructor` or\n * a `HarnessPredicate`.\n */", "description": "A query for a `ComponentHarness`, which is expressed as either a `ComponentHarnessConstructor` or\na `HarnessPredicate`.", "jsdocTags": [], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 30, "endLine": 32}}, {"name": "LocatorFnResult", "type": "{\n  [I in keyof T]: T[I] extends new (...args: any[]) => infer C // Map `ComponentHarnessConstructor<C>` to `C`.\n    ? C\n    : // Map `HarnessPredicate<C>` to `C`.\n      T[I] extends {harnessType: new (...args: any[]) => infer C}\n      ? C\n      : // Map `string` to `TestElement`.\n        T[I] extends string\n        ? TestElement\n        : // Map everything else to `never` (should not happen due to the type constraint on `T`).\n          never;\n}[number]", "entryType": "type_alias", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "rawComment": "/**\n * The result type obtained when searching using a particular list of queries. This type depends on\n * the particular items being queried.\n * - If one of the queries is for a `ComponentHarnessConstructor<C1>`, it means that the result\n *   might be a harness of type `C1`\n * - If one of the queries is for a `HarnessPredicate<C2>`, it means that the result might be a\n *   harness of type `C2`\n * - If one of the queries is for a `string`, it means that the result might be a `TestElement`.\n *\n * Since we don't know for sure which query will match, the result type if the union of the types\n * for all possible results.\n *\n * @usageNotes\n * ### Example\n *\n * The type:\n * ```ts\n * LocatorFnResult<[\n *   ComponentHarnessConstructor<MyHarness>,\n *   HarnessPredicate<MyOtherHarness>,\n *   string\n * ]>\n * ```\n *\n * is equivalent to:\n *\n * ```ts\n * MyHarness | MyOtherHarness | TestElement\n * ```\n */", "description": "The result type obtained when searching using a particular list of queries. This type depends on\nthe particular items being queried.\n- If one of the queries is for a `ComponentHarnessConstructor<C1>`, it means that the result\n  might be a harness of type `C1`\n- If one of the queries is for a `HarnessPredicate<C2>`, it means that the result might be a\n  harness of type `C2`\n- If one of the queries is for a `string`, it means that the result might be a `TestElement`.\n\nSince we don't know for sure which query will match, the result type if the union of the types\nfor all possible results.", "jsdocTags": [{"name": "usageNotes", "comment": "### Example\n\nThe type:\n```ts\nLocatorFnResult<[\n  ComponentHarnessConstructor<MyHarness>,\n  <PERSON><PERSON>ssPredicate<MyOtherHarness>,\n  string\n]>\n```\n\nis equivalent to:\n\n```ts\nMyHarness | MyOtherHarness | TestElement\n```"}], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 64, "endLine": 75}}, {"name": "HarnessEnvironment", "isAbstract": true, "entryType": "undecorated_class", "members": [{"name": "constructor", "signatures": [], "implementation": {"params": [{"name": "rawRootElement", "description": "The native root element of this `HarnessEnvironment`.", "type": "E", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "HarnessEnvironment<E>", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": ["protected"]}, {"name": "rootElement", "type": "TestElement", "memberType": "getter", "memberTags": [], "description": "The root element of this `HarnessEnvironment` as a `TestElement`.", "jsdocTags": []}, {"name": "rootElement", "type": "TestElement", "memberType": "setter", "memberTags": [], "description": "", "jsdocTags": []}, {"name": "documentRootLocatorFactory", "signatures": [{"name": "documentRootLocatorFactory", "entryType": "function", "description": "Gets a locator factory rooted at the document root.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Gets a locator factory rooted at the document root. */", "returnType": "LocatorFactory"}], "implementation": {"params": [], "isNewType": false, "returnType": "LocatorFactory", "generics": [], "name": "documentRootLocatorFactory", "description": "Gets a locator factory rooted at the document root.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets a locator factory rooted at the document root. */"}, "entryType": "function", "description": "Gets a locator factory rooted at the document root.", "jsdocTags": [], "rawComment": "/** Gets a locator factory rooted at the document root. */", "memberType": "method", "memberTags": []}, {"name": "locatorFor", "signatures": [{"name": "locatorFor", "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the root element of this `HarnessEnvironment`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait lf.locatorFor('span')()            // Throws because the `Promise` rejects\n```", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\neach query."}], "params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `HarnessEnvironment`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */", "returnType": "() => Promise<LocatorFnResult<T>>"}], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T>>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorFor", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the root element of this `HarnessEnvironment`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait lf.locatorFor('span')()            // Throws because the `Promise` rejects\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\neach query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `HarnessEnvironment`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the root element of this `HarnessEnvironment`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait lf.locatorFor('span')()            // Throws because the `Promise` rejects\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\neach query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `HarnessEnvironment`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */", "memberType": "method", "memberTags": []}, {"name": "locatorForOptional", "signatures": [{"name": "locatorForOptional", "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the root element of this `HarnessEnvironmnet`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait lf.locatorForOptional('span')()            // Gets `null`\n```", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\nresult types for each query or null."}], "params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `HarnessEnvironmnet`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */", "returnType": "() => Promise<LocatorFnResult<T> | null>"}], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T> | null>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorForOptional", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the root element of this `HarnessEnvironmnet`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait lf.locatorForOptional('span')()            // Gets `null`\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\nresult types for each query or null."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `HarnessEnvironmnet`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the root element of this `HarnessEnvironmnet`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait lf.locatorForOptional('span')()            // Gets `null`\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\nresult types for each query or null."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `HarnessEnvironmnet`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */", "memberType": "method", "memberTags": []}, {"name": "locatorForAll", "signatures": [{"name": "locatorForAll", "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\nor elements under the root element of this `HarnessEnvironment`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n`IdIsD1Harness.hostSelector` is `'#d1'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\n// Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\nawait lf.locatorForAll(DivHarness, 'div')()\n// Gets [TestElement for #d1, TestElement for #d2]\nawait lf.locatorForAll('div', '#d1')()\n// Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\nawait lf.locatorForAll(DivHarness, IdIsD1Harness)()\n// Gets []\nawait lf.locatorForAll('span')()\n```", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for all\nelements and harnesses matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If an element matches more than\none `ComponentHarness` class, the locator gets an instance of each for the same element. If\nan element matches multiple `string` selectors, only one `TestElement` instance is returned\nfor that element. The type that the `Promise` resolves to is an array where each element is\nthe union of all result types for each query."}], "params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the root element of this `HarnessEnvironment`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await lf.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await lf.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await lf.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await lf.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */", "returnType": "() => Promise<LocatorFnResult<T>[]>"}], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T>[]>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorForAll", "description": "Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\nor elements under the root element of this `HarnessEnvironment`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n`IdIsD1Harness.hostSelector` is `'#d1'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\n// Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\nawait lf.locatorForAll(DivHarness, 'div')()\n// Gets [TestElement for #d1, TestElement for #d2]\nawait lf.locatorForAll('div', '#d1')()\n// Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\nawait lf.locatorForAll(DivHarness, IdIsD1Harness)()\n// Gets []\nawait lf.locatorForAll('span')()\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for all\nelements and harnesses matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If an element matches more than\none `ComponentHarness` class, the locator gets an instance of each for the same element. If\nan element matches multiple `string` selectors, only one `TestElement` instance is returned\nfor that element. The type that the `Promise` resolves to is an array where each element is\nthe union of all result types for each query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the root element of this `HarnessEnvironment`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await lf.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await lf.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await lf.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await lf.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\nor elements under the root element of this `HarnessEnvironment`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n`IdIsD1Harness.hostSelector` is `'#d1'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\n// Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\nawait lf.locatorForAll(DivHarness, 'div')()\n// Gets [TestElement for #d1, TestElement for #d2]\nawait lf.locatorForAll('div', '#d1')()\n// Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\nawait lf.locatorForAll(DivHarness, IdIsD1Harness)()\n// Gets []\nawait lf.locatorForAll('span')()\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for all\nelements and harnesses matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If an element matches more than\none `ComponentHarness` class, the locator gets an instance of each for the same element. If\nan element matches multiple `string` selectors, only one `TestElement` instance is returned\nfor that element. The type that the `Promise` resolves to is an array where each element is\nthe union of all result types for each query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the root element of this `HarnessEnvironment`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await lf.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await lf.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await lf.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await lf.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */", "memberType": "method", "memberTags": []}, {"name": "rootHarness<PERSON>oader", "signatures": [{"name": "rootHarness<PERSON>oader", "entryType": "function", "description": "", "generics": [], "isNewType": false, "jsdocTags": [{"name": "return", "comment": "A `HarnessLoader` rooted at the root element of this `HarnessEnvironment`."}], "params": [], "rawComment": "/** @return A `HarnessLoader` rooted at the root element of this `HarnessEnvironment`. */", "returnType": "Promise<HarnessLoader>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<HarnessLoader>", "generics": [], "name": "rootHarness<PERSON>oader", "description": "", "entryType": "function", "jsdocTags": [{"name": "return", "comment": "A `HarnessLoader` rooted at the root element of this `HarnessEnvironment`."}], "rawComment": "/** @return A `HarnessLoader` rooted at the root element of this `HarnessEnvironment`. */"}, "entryType": "function", "description": "", "jsdocTags": [{"name": "return", "comment": "A `HarnessLoader` rooted at the root element of this `HarnessEnvironment`."}], "rawComment": "/** @return A `HarnessLoader` rooted at the root element of this `HarnessEnvironment`. */", "memberType": "method", "memberTags": []}, {"name": "harnessLoaderFor", "signatures": [{"name": "harnessLoaderFor", "entryType": "function", "description": "Gets a `HarnessLoader` instance for an element under the root of this `HarnessEnvironment`.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A `HarnessLoader` rooted at the first element matching the given selector."}, {"name": "throws", "comment": "If no matching element is found for the given selector."}], "params": [{"name": "selector", "description": "The selector for the root element.", "type": "string", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Gets a `<PERSON><PERSON><PERSON><PERSON>oader` instance for an element under the root of this `HarnessEnvironment`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector.\n   * @throws If no matching element is found for the given selector.\n   */", "returnType": "Promise<HarnessLoader>"}], "implementation": {"params": [{"name": "selector", "description": "The selector for the root element.", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader>", "generics": [], "name": "harnessLoaderFor", "description": "Gets a `HarnessLoader` instance for an element under the root of this `HarnessEnvironment`.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A `HarnessLoader` rooted at the first element matching the given selector."}, {"name": "throws", "comment": "If no matching element is found for the given selector."}], "rawComment": "/**\n   * Gets a `<PERSON><PERSON><PERSON><PERSON>oader` instance for an element under the root of this `HarnessEnvironment`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector.\n   * @throws If no matching element is found for the given selector.\n   */"}, "entryType": "function", "description": "Gets a `HarnessLoader` instance for an element under the root of this `HarnessEnvironment`.", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A `HarnessLoader` rooted at the first element matching the given selector."}, {"name": "throws", "comment": "If no matching element is found for the given selector."}], "rawComment": "/**\n   * Gets a `<PERSON><PERSON><PERSON><PERSON>oader` instance for an element under the root of this `HarnessEnvironment`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector.\n   * @throws If no matching element is found for the given selector.\n   */", "memberType": "method", "memberTags": []}, {"name": "harnessLoaderForOptional", "signatures": [{"name": "harnessLoaderForOptional", "entryType": "function", "description": "Gets a `HarnessLoader` instance for an element under the root of this `HarnessEnvironment`.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A `HarnessLoader` rooted at the first element matching the given selector, or null if\nno matching element is found."}], "params": [{"name": "selector", "description": "The selector for the root element.", "type": "string", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Gets a `<PERSON><PERSON><PERSON>Loader` instance for an element under the root of this `HarnessEnvironment`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector, or null if\n   *     no matching element is found.\n   */", "returnType": "Promise<HarnessLoader | null>"}], "implementation": {"params": [{"name": "selector", "description": "The selector for the root element.", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader | null>", "generics": [], "name": "harnessLoaderForOptional", "description": "Gets a `HarnessLoader` instance for an element under the root of this `HarnessEnvironment`.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A `HarnessLoader` rooted at the first element matching the given selector, or null if\nno matching element is found."}], "rawComment": "/**\n   * Gets a `<PERSON><PERSON><PERSON>Loader` instance for an element under the root of this `HarnessEnvironment`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector, or null if\n   *     no matching element is found.\n   */"}, "entryType": "function", "description": "Gets a `HarnessLoader` instance for an element under the root of this `HarnessEnvironment`.", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A `HarnessLoader` rooted at the first element matching the given selector, or null if\nno matching element is found."}], "rawComment": "/**\n   * Gets a `<PERSON><PERSON><PERSON>Loader` instance for an element under the root of this `HarnessEnvironment`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector, or null if\n   *     no matching element is found.\n   */", "memberType": "method", "memberTags": []}, {"name": "harnessLoaderForAll", "signatures": [{"name": "harnessLoaderForAll", "entryType": "function", "description": "Gets a list of `HarnessLoader` instances, one for each matching element.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A list of `HarnessLoader`, one rooted at each element matching the given selector."}], "params": [{"name": "selector", "description": "The selector for the root element.", "type": "string", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Gets a list of `HarnessLoader` instances, one for each matching element.\n   * @param selector The selector for the root element.\n   * @return A list of `HarnessLoader`, one rooted at each element matching the given selector.\n   */", "returnType": "Promise<HarnessLoader[]>"}], "implementation": {"params": [{"name": "selector", "description": "The selector for the root element.", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader[]>", "generics": [], "name": "harnessLoaderForAll", "description": "Gets a list of `HarnessLoader` instances, one for each matching element.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A list of `HarnessLoader`, one rooted at each element matching the given selector."}], "rawComment": "/**\n   * Gets a list of `HarnessLoader` instances, one for each matching element.\n   * @param selector The selector for the root element.\n   * @return A list of `HarnessLoader`, one rooted at each element matching the given selector.\n   */"}, "entryType": "function", "description": "Gets a list of `HarnessLoader` instances, one for each matching element.", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A list of `HarnessLoader`, one rooted at each element matching the given selector."}], "rawComment": "/**\n   * Gets a list of `HarnessLoader` instances, one for each matching element.\n   * @param selector The selector for the root element.\n   * @return A list of `HarnessLoader`, one rooted at each element matching the given selector.\n   */", "memberType": "method", "memberTags": []}, {"name": "getHarness", "signatures": [{"name": "getHarness", "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\nmultiple matching components are found, a harness for the first one is returned. If no matching\ncomponent is found, an error is thrown.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An instance of the given harness type"}, {"name": "throws", "comment": "If a matching component instance can't be found."}], "params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\n   * multiple matching components are found, a harness for the first one is returned. If no matching\n   * component is found, an error is thrown.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type\n   * @throws If a matching component instance can't be found.\n   */", "returnType": "Promise<T>"}], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "getHarness", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\nmultiple matching components are found, a harness for the first one is returned. If no matching\ncomponent is found, an error is thrown.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An instance of the given harness type"}, {"name": "throws", "comment": "If a matching component instance can't be found."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\n   * multiple matching components are found, a harness for the first one is returned. If no matching\n   * component is found, an error is thrown.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type\n   * @throws If a matching component instance can't be found.\n   */"}, "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\nmultiple matching components are found, a harness for the first one is returned. If no matching\ncomponent is found, an error is thrown.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An instance of the given harness type"}, {"name": "throws", "comment": "If a matching component instance can't be found."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\n   * multiple matching components are found, a harness for the first one is returned. If no matching\n   * component is found, an error is thrown.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type\n   * @throws If a matching component instance can't be found.\n   */", "memberType": "method", "memberTags": []}, {"name": "getHarnessOrNull", "signatures": [{"name": "getHarnessOrNull", "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\nmultiple matching components are found, a harness for the first one is returned. If no matching\ncomponent is found, null is returned.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An instance of the given harness type (or null if not found)."}], "params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\n   * multiple matching components are found, a harness for the first one is returned. If no matching\n   * component is found, null is returned.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type (or null if not found).\n   */", "returnType": "Promise<T | null>"}], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T | null>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "getHarnessOrNull", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\nmultiple matching components are found, a harness for the first one is returned. If no matching\ncomponent is found, null is returned.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An instance of the given harness type (or null if not found)."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\n   * multiple matching components are found, a harness for the first one is returned. If no matching\n   * component is found, null is returned.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type (or null if not found).\n   */"}, "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\nmultiple matching components are found, a harness for the first one is returned. If no matching\ncomponent is found, null is returned.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An instance of the given harness type (or null if not found)."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that instance. If\n   * multiple matching components are found, a harness for the first one is returned. If no matching\n   * component is found, null is returned.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type (or null if not found).\n   */", "memberType": "method", "memberTags": []}, {"name": "getHarnessAtIndex", "signatures": [{"name": "getHarnessAtIndex", "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type and index\nunder the `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that\ninstance. The index specifies the offset of the component to find. If no matching\ncomponent is found at that index, an error is thrown.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "param", "comment": "The zero-indexed offset of the component to find"}, {"name": "return", "comment": "An instance of the given harness type"}, {"name": "throws", "comment": "If a matching component instance can't be found."}], "params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}, {"name": "offset", "description": "", "type": "number", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type and index\n   * under the `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that\n   * instance. The index specifies the offset of the component to find. If no matching\n   * component is found at that index, an error is thrown.\n   * @param query A query for a harness to create\n   * @param index The zero-indexed offset of the component to find\n   * @return An instance of the given harness type\n   * @throws If a matching component instance can't be found.\n   */", "returnType": "Promise<T>"}], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}, {"name": "offset", "description": "", "type": "number", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "getHarnessAtIndex", "description": "Searches for an instance of the component corresponding to the given harness type and index\nunder the `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that\ninstance. The index specifies the offset of the component to find. If no matching\ncomponent is found at that index, an error is thrown.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "param", "comment": "The zero-indexed offset of the component to find"}, {"name": "return", "comment": "An instance of the given harness type"}, {"name": "throws", "comment": "If a matching component instance can't be found."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type and index\n   * under the `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that\n   * instance. The index specifies the offset of the component to find. If no matching\n   * component is found at that index, an error is thrown.\n   * @param query A query for a harness to create\n   * @param index The zero-indexed offset of the component to find\n   * @return An instance of the given harness type\n   * @throws If a matching component instance can't be found.\n   */"}, "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type and index\nunder the `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that\ninstance. The index specifies the offset of the component to find. If no matching\ncomponent is found at that index, an error is thrown.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "param", "comment": "The zero-indexed offset of the component to find"}, {"name": "return", "comment": "An instance of the given harness type"}, {"name": "throws", "comment": "If a matching component instance can't be found."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type and index\n   * under the `HarnessEnvironment`'s root element, and returns a `ComponentHarness` for that\n   * instance. The index specifies the offset of the component to find. If no matching\n   * component is found at that index, an error is thrown.\n   * @param query A query for a harness to create\n   * @param index The zero-indexed offset of the component to find\n   * @return An instance of the given harness type\n   * @throws If a matching component instance can't be found.\n   */", "memberType": "method", "memberTags": []}, {"name": "get<PERSON>ll<PERSON><PERSON><PERSON><PERSON>", "signatures": [{"name": "get<PERSON>ll<PERSON><PERSON><PERSON><PERSON>", "entryType": "function", "description": "Searches for all instances of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a list `ComponentHarness` for each instance.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "A list instances of the given harness type."}], "params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Searches for all instances of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a list `ComponentHarness` for each instance.\n   * @param query A query for a harness to create\n   * @return A list instances of the given harness type.\n   */", "returnType": "Promise<T[]>"}], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T[]>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "get<PERSON>ll<PERSON><PERSON><PERSON><PERSON>", "description": "Searches for all instances of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a list `ComponentHarness` for each instance.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "A list instances of the given harness type."}], "rawComment": "/**\n   * Searches for all instances of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a list `ComponentHarness` for each instance.\n   * @param query A query for a harness to create\n   * @return A list instances of the given harness type.\n   */"}, "entryType": "function", "description": "Searches for all instances of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a list `ComponentHarness` for each instance.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "A list instances of the given harness type."}], "rawComment": "/**\n   * Searches for all instances of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a list `ComponentHarness` for each instance.\n   * @param query A query for a harness to create\n   * @return A list instances of the given harness type.\n   */", "memberType": "method", "memberTags": []}, {"name": "countHarnesses", "signatures": [{"name": "countHarnesses", "entryType": "function", "description": "Searches for all instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns the number that were found.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "The number of instances that were found."}], "params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Searches for all instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns the number that were found.\n   * @param query A query for a harness to create\n   * @return The number of instances that were found.\n   */", "returnType": "Promise<number>"}], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<number>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "countHarnesses", "description": "Searches for all instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns the number that were found.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "The number of instances that were found."}], "rawComment": "/**\n   * Searches for all instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns the number that were found.\n   * @param query A query for a harness to create\n   * @return The number of instances that were found.\n   */"}, "entryType": "function", "description": "Searches for all instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns the number that were found.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "The number of instances that were found."}], "rawComment": "/**\n   * Searches for all instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns the number that were found.\n   * @param query A query for a harness to create\n   * @return The number of instances that were found.\n   */", "memberType": "method", "memberTags": []}, {"name": "hasH<PERSON>ness", "signatures": [{"name": "hasH<PERSON>ness", "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a boolean indicating if any were found.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "A boolean indicating if an instance was found."}], "params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a boolean indicating if any were found.\n   * @param query A query for a harness to create\n   * @return A boolean indicating if an instance was found.\n   */", "returnType": "Promise<boolean>"}], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<boolean>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "hasH<PERSON>ness", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a boolean indicating if any were found.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "A boolean indicating if an instance was found."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a boolean indicating if any were found.\n   * @param query A query for a harness to create\n   * @return A boolean indicating if an instance was found.\n   */"}, "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessEnvironment`'s root element, and returns a boolean indicating if any were found.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "A boolean indicating if an instance was found."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `HarnessEnvironment`'s root element, and returns a boolean indicating if any were found.\n   * @param query A query for a harness to create\n   * @return A boolean indicating if an instance was found.\n   */", "memberType": "method", "memberTags": []}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signatures": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entryType": "function", "description": "Searches for an element with the given selector under the evironment's root element,\nand returns a `HarnessLoader` rooted at the matching element. If multiple elements match the\nselector, the first is used. If no elements match, an error is thrown.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The selector for the root element of the new `HarnessLoader`"}, {"name": "return", "comment": "A `HarnessLoader` rooted at the element matching the given selector."}, {"name": "throws", "comment": "If a matching element can't be found."}], "params": [{"name": "selector", "description": "The selector for the root element of the new `HarnessLoader`", "type": "string", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Searches for an element with the given selector under the evironment's root element,\n   * and returns a `<PERSON><PERSON><PERSON><PERSON>oader` rooted at the matching element. If multiple elements match the\n   * selector, the first is used. If no elements match, an error is thrown.\n   * @param selector The selector for the root element of the new `<PERSON><PERSON><PERSON><PERSON>oader`\n   * @return A `HarnessLoader` rooted at the element matching the given selector.\n   * @throws If a matching element can't be found.\n   */", "returnType": "Promise<HarnessLoader>"}], "implementation": {"params": [{"name": "selector", "description": "The selector for the root element of the new `HarnessLoader`", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader>", "generics": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Searches for an element with the given selector under the evironment's root element,\nand returns a `HarnessLoader` rooted at the matching element. If multiple elements match the\nselector, the first is used. If no elements match, an error is thrown.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for the root element of the new `HarnessLoader`"}, {"name": "return", "comment": "A `HarnessLoader` rooted at the element matching the given selector."}, {"name": "throws", "comment": "If a matching element can't be found."}], "rawComment": "/**\n   * Searches for an element with the given selector under the evironment's root element,\n   * and returns a `<PERSON><PERSON><PERSON><PERSON>oader` rooted at the matching element. If multiple elements match the\n   * selector, the first is used. If no elements match, an error is thrown.\n   * @param selector The selector for the root element of the new `<PERSON><PERSON><PERSON><PERSON>oader`\n   * @return A `HarnessLoader` rooted at the element matching the given selector.\n   * @throws If a matching element can't be found.\n   */"}, "entryType": "function", "description": "Searches for an element with the given selector under the evironment's root element,\nand returns a `HarnessLoader` rooted at the matching element. If multiple elements match the\nselector, the first is used. If no elements match, an error is thrown.", "jsdocTags": [{"name": "param", "comment": "The selector for the root element of the new `HarnessLoader`"}, {"name": "return", "comment": "A `HarnessLoader` rooted at the element matching the given selector."}, {"name": "throws", "comment": "If a matching element can't be found."}], "rawComment": "/**\n   * Searches for an element with the given selector under the evironment's root element,\n   * and returns a `<PERSON><PERSON><PERSON><PERSON>oader` rooted at the matching element. If multiple elements match the\n   * selector, the first is used. If no elements match, an error is thrown.\n   * @param selector The selector for the root element of the new `<PERSON><PERSON><PERSON><PERSON>oader`\n   * @return A `HarnessLoader` rooted at the element matching the given selector.\n   * @throws If a matching element can't be found.\n   */", "memberType": "method", "memberTags": []}, {"name": "getAllChildLoaders", "signatures": [{"name": "getAllChildLoaders", "entryType": "function", "description": "Searches for all elements with the given selector under the environment's root element,\nand returns an array of `HarnessLoader`s, one for each matching element, rooted at that\nelement.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The selector for the root element of the new `HarnessLoader`"}, {"name": "return", "comment": "A list of `HarnessLoader`s, one for each matching element, rooted at that element."}], "params": [{"name": "selector", "description": "The selector for the root element of the new `HarnessLoader`", "type": "string", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Searches for all elements with the given selector under the environment's root element,\n   * and returns an array of `HarnessLoader`s, one for each matching element, rooted at that\n   * element.\n   * @param selector The selector for the root element of the new `<PERSON><PERSON><PERSON><PERSON>oader`\n   * @return A list of `HarnessLoader`s, one for each matching element, rooted at that element.\n   */", "returnType": "Promise<HarnessLoader[]>"}], "implementation": {"params": [{"name": "selector", "description": "The selector for the root element of the new `HarnessLoader`", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader[]>", "generics": [], "name": "getAllChildLoaders", "description": "Searches for all elements with the given selector under the environment's root element,\nand returns an array of `HarnessLoader`s, one for each matching element, rooted at that\nelement.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for the root element of the new `HarnessLoader`"}, {"name": "return", "comment": "A list of `HarnessLoader`s, one for each matching element, rooted at that element."}], "rawComment": "/**\n   * Searches for all elements with the given selector under the environment's root element,\n   * and returns an array of `HarnessLoader`s, one for each matching element, rooted at that\n   * element.\n   * @param selector The selector for the root element of the new `<PERSON><PERSON><PERSON><PERSON>oader`\n   * @return A list of `HarnessLoader`s, one for each matching element, rooted at that element.\n   */"}, "entryType": "function", "description": "Searches for all elements with the given selector under the environment's root element,\nand returns an array of `HarnessLoader`s, one for each matching element, rooted at that\nelement.", "jsdocTags": [{"name": "param", "comment": "The selector for the root element of the new `HarnessLoader`"}, {"name": "return", "comment": "A list of `HarnessLoader`s, one for each matching element, rooted at that element."}], "rawComment": "/**\n   * Searches for all elements with the given selector under the environment's root element,\n   * and returns an array of `HarnessLoader`s, one for each matching element, rooted at that\n   * element.\n   * @param selector The selector for the root element of the new `<PERSON><PERSON><PERSON><PERSON>oader`\n   * @return A list of `HarnessLoader`s, one for each matching element, rooted at that element.\n   */", "memberType": "method", "memberTags": []}, {"name": "createComponentHarness", "signatures": [{"name": "createComponentHarness", "entryType": "function", "description": "Creates a `ComponentHarness` for the given harness type with the given raw host element.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [], "params": [{"name": "harnessType", "description": "", "type": "ComponentHarnessConstructor<T>", "isOptional": false, "isRestParam": false}, {"name": "element", "description": "", "type": "E", "isOptional": false, "isRestParam": false}], "rawComment": "/** Creates a `ComponentHarness` for the given harness type with the given raw host element. */", "returnType": "T"}], "implementation": {"params": [{"name": "harnessType", "description": "", "type": "ComponentHarnessConstructor<T>", "isOptional": false, "isRestParam": false}, {"name": "element", "description": "", "type": "E", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "T", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "createComponentHarness", "description": "Creates a `ComponentHarness` for the given harness type with the given raw host element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Creates a `ComponentHarness` for the given harness type with the given raw host element. */"}, "entryType": "function", "description": "Creates a `ComponentHarness` for the given harness type with the given raw host element.", "jsdocTags": [], "rawComment": "/** Creates a `ComponentHarness` for the given harness type with the given raw host element. */", "memberType": "method", "memberTags": ["protected"]}, {"name": "forceStabilize", "signatures": [{"name": "forceStabilize", "entryType": "function", "description": "Flushes change detection and async tasks captured in the Angular zone.\nIn most cases it should not be necessary to call this manually. However, there may be some edge\ncases where it is needed to fully flush animation events.\nThis is an abstrct method that must be implemented by subclasses.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Flushes change detection and async tasks captured in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   * This is an abstrct method that must be implemented by subclasses.\n   */", "returnType": "Promise<void>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "forceStabilize", "description": "Flushes change detection and async tasks captured in the Angular zone.\nIn most cases it should not be necessary to call this manually. However, there may be some edge\ncases where it is needed to fully flush animation events.\nThis is an abstrct method that must be implemented by subclasses.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Flushes change detection and async tasks captured in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   * This is an abstrct method that must be implemented by subclasses.\n   */"}, "entryType": "function", "description": "Flushes change detection and async tasks captured in the Angular zone.\nIn most cases it should not be necessary to call this manually. However, there may be some edge\ncases where it is needed to fully flush animation events.\nThis is an abstrct method that must be implemented by subclasses.", "jsdocTags": [], "rawComment": "/**\n   * Flushes change detection and async tasks captured in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   * This is an abstrct method that must be implemented by subclasses.\n   */", "memberType": "method", "memberTags": ["abstract"]}, {"name": "waitForTasksOutsideAngular", "signatures": [{"name": "waitForTasksOutsideAngular", "entryType": "function", "description": "Waits for all scheduled or running async tasks to complete. This allows harness\nauthors to wait for async tasks outside of the Angular zone.\nThis is an abstrct method that must be implemented by subclasses.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   * This is an abstrct method that must be implemented by subclasses.\n   */", "returnType": "Promise<void>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "waitForTasksOutsideAngular", "description": "Waits for all scheduled or running async tasks to complete. This allows harness\nauthors to wait for async tasks outside of the Angular zone.\nThis is an abstrct method that must be implemented by subclasses.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   * This is an abstrct method that must be implemented by subclasses.\n   */"}, "entryType": "function", "description": "Waits for all scheduled or running async tasks to complete. This allows harness\nauthors to wait for async tasks outside of the Angular zone.\nThis is an abstrct method that must be implemented by subclasses.", "jsdocTags": [], "rawComment": "/**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   * This is an abstrct method that must be implemented by subclasses.\n   */", "memberType": "method", "memberTags": ["abstract"]}, {"name": "getDocumentRoot", "signatures": [{"name": "getDocumentRoot", "entryType": "function", "description": "Gets the root element for the document.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Gets the root element for the document. */", "returnType": "E"}], "implementation": {"params": [], "isNewType": false, "returnType": "E", "generics": [], "name": "getDocumentRoot", "description": "Gets the root element for the document.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets the root element for the document. */"}, "entryType": "function", "description": "Gets the root element for the document.", "jsdocTags": [], "rawComment": "/** Gets the root element for the document. */", "memberType": "method", "memberTags": ["protected", "abstract"]}, {"name": "createTestElement", "signatures": [{"name": "createTestElement", "entryType": "function", "description": "Creates a `TestElement` from a raw element.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "element", "description": "", "type": "E", "isOptional": false, "isRestParam": false}], "rawComment": "/** Creates a `TestElement` from a raw element. */", "returnType": "TestElement"}], "implementation": {"params": [{"name": "element", "description": "", "type": "E", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "TestElement", "generics": [], "name": "createTestElement", "description": "Creates a `TestElement` from a raw element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Creates a `TestElement` from a raw element. */"}, "entryType": "function", "description": "Creates a `TestElement` from a raw element.", "jsdocTags": [], "rawComment": "/** Creates a `TestElement` from a raw element. */", "memberType": "method", "memberTags": ["protected", "abstract"]}, {"name": "createEnvironment", "signatures": [{"name": "createEnvironment", "entryType": "function", "description": "Creates a `HarnessEnvironment` rooted at the given raw element.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "element", "description": "", "type": "E", "isOptional": false, "isRestParam": false}], "rawComment": "/** Creates a `HarnessEnvironment` rooted at the given raw element. */", "returnType": "HarnessEnvironment<E>"}], "implementation": {"params": [{"name": "element", "description": "", "type": "E", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "HarnessEnvironment<E>", "generics": [], "name": "createEnvironment", "description": "Creates a `HarnessEnvironment` rooted at the given raw element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Creates a `HarnessEnvironment` rooted at the given raw element. */"}, "entryType": "function", "description": "Creates a `HarnessEnvironment` rooted at the given raw element.", "jsdocTags": [], "rawComment": "/** Creates a `HarnessEnvironment` rooted at the given raw element. */", "memberType": "method", "memberTags": ["protected", "abstract"]}, {"name": "getAllRawElements", "signatures": [{"name": "getAllRawElements", "entryType": "function", "description": "Gets a list of all elements matching the given selector under this environment's root element.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [{"name": "selector", "description": "", "type": "string", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Gets a list of all elements matching the given selector under this environment's root element.\n   */", "returnType": "Promise<E[]>"}], "implementation": {"params": [{"name": "selector", "description": "", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<E[]>", "generics": [], "name": "getAllRawElements", "description": "Gets a list of all elements matching the given selector under this environment's root element.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Gets a list of all elements matching the given selector under this environment's root element.\n   */"}, "entryType": "function", "description": "Gets a list of all elements matching the given selector under this environment's root element.", "jsdocTags": [], "rawComment": "/**\n   * Gets a list of all elements matching the given selector under this environment's root element.\n   */", "memberType": "method", "memberTags": ["protected", "abstract"]}], "generics": [{"name": "E"}], "description": "Base harness environment class that can be extended to allow `ComponentHarness`es to be used in\ndifferent test environments (e.g. testbed, protractor, etc.). This class implements the\nfunctionality of both a `HarnessLoader` and `LocatorFactory`. This class is generic on the raw\nelement type, `E`, used by the particular test environment.", "jsdocTags": [], "rawComment": "/**\n * Base harness environment class that can be extended to allow `ComponentHarness`es to be used in\n * different test environments (e.g. testbed, protractor, etc.). This class implements the\n * functionality of both a `HarnessLoader` and `LocatorFactory`. This class is generic on the raw\n * element type, `E`, used by the particular test environment.\n */", "implements": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LocatorFactory"], "source": {"filePath": "src/cdk/testing/harness-environment.ts", "startLine": 45, "endLine": 435}}, {"name": "handleAutoChangeDetectionStatus", "signatures": [{"name": "handleAutoChangeDetectionStatus", "entryType": "function", "description": "Allows a test `HarnessEnvironment` to install its own handler for auto change detection status\nchanges.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The handler for the auto change detection status."}], "params": [{"name": "handler", "description": "The handler for the auto change detection status.", "type": "(status: AutoChangeDetectionStatus) => void", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n * Allows a test `HarnessEnvironment` to install its own handler for auto change detection status\n * changes.\n * @param handler The handler for the auto change detection status.\n */", "returnType": "void"}], "implementation": {"params": [{"name": "handler", "description": "The handler for the auto change detection status.", "type": "(status: AutoChangeDetectionStatus) => void", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "void", "generics": [], "name": "handleAutoChangeDetectionStatus", "description": "Allows a test `HarnessEnvironment` to install its own handler for auto change detection status\nchanges.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The handler for the auto change detection status."}], "rawComment": "/**\n * Allows a test `HarnessEnvironment` to install its own handler for auto change detection status\n * changes.\n * @param handler The handler for the auto change detection status.\n */"}, "entryType": "function", "description": "Allows a test `HarnessEnvironment` to install its own handler for auto change detection status\nchanges.", "jsdocTags": [{"name": "param", "comment": "The handler for the auto change detection status."}], "rawComment": "/**\n * Allows a test `HarnessEnvironment` to install its own handler for auto change detection status\n * changes.\n * @param handler The handler for the auto change detection status.\n */", "source": {"filePath": "src/cdk/testing/change-detection.ts", "startLine": 49, "endLine": 54}}, {"name": "TestElement", "isAbstract": false, "entryType": "interface", "members": [{"name": "blur", "signatures": [], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "blur", "description": "Blur the element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Blur the element. */"}, "entryType": "function", "description": "Blur the element.", "jsdocTags": [], "rawComment": "/** Blur the element. */", "memberType": "method", "memberTags": []}, {"name": "clear", "signatures": [], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "clear", "description": "Clear the element's input (for input and textarea elements only).", "entryType": "function", "jsdocTags": [], "rawComment": "/** Clear the element's input (for input and textarea elements only). */"}, "entryType": "function", "description": "Clear the element's input (for input and textarea elements only).", "jsdocTags": [], "rawComment": "/** Clear the element's input (for input and textarea elements only). */", "memberType": "method", "memberTags": []}, {"name": "click", "signatures": [], "implementation": {"params": [{"name": "modifiers", "description": "", "type": "ModifierKeys | undefined", "isOptional": true, "isRestParam": false}], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "click", "description": "Click the element at the default location for the current environment. If you need to guarantee\nthe element is clicked at a specific location, consider using `click('center')` or\n`click(x, y)` instead.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Click the element at the default location for the current environment. If you need to guarantee\n   * the element is clicked at a specific location, consider using `click('center')` or\n   * `click(x, y)` instead.\n   */"}, "entryType": "function", "description": "Click the element at the default location for the current environment. If you need to guarantee\nthe element is clicked at a specific location, consider using `click('center')` or\n`click(x, y)` instead.", "jsdocTags": [], "rawComment": "/**\n   * Click the element at the default location for the current environment. If you need to guarantee\n   * the element is clicked at a specific location, consider using `click('center')` or\n   * `click(x, y)` instead.\n   */", "memberType": "method", "memberTags": []}, {"name": "click", "signatures": [], "implementation": {"params": [{"name": "location", "description": "", "type": "\"center\"", "isOptional": false, "isRestParam": false}, {"name": "modifiers", "description": "", "type": "ModifierKeys | undefined", "isOptional": true, "isRestParam": false}], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "click", "description": "Click the element at the element's center.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Click the element at the element's center. */"}, "entryType": "function", "description": "Click the element at the element's center.", "jsdocTags": [], "rawComment": "/** Click the element at the element's center. */", "memberType": "method", "memberTags": []}, {"name": "click", "signatures": [], "implementation": {"params": [{"name": "relativeX", "description": "Coordinate within the element, along the X-axis at which to click.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "relativeY", "description": "Coordinate within the element, along the Y-axis at which to click.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "modifiers", "description": "Modifier keys held while clicking", "type": "ModifierKeys | undefined", "isOptional": true, "isRestParam": false}], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "click", "description": "Click the element at the specified coordinates relative to the top-left of the element.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Coordinate within the element, along the X-axis at which to click."}, {"name": "param", "comment": "Coordinate within the element, along the Y-axis at which to click."}, {"name": "param", "comment": "Modifier keys held while clicking"}], "rawComment": "/**\n   * Click the element at the specified coordinates relative to the top-left of the element.\n   * @param relativeX Coordinate within the element, along the X-axis at which to click.\n   * @param relativeY Coordinate within the element, along the Y-axis at which to click.\n   * @param modifiers Modifier keys held while clicking\n   */"}, "entryType": "function", "description": "Click the element at the specified coordinates relative to the top-left of the element.", "jsdocTags": [{"name": "param", "comment": "Coordinate within the element, along the X-axis at which to click."}, {"name": "param", "comment": "Coordinate within the element, along the Y-axis at which to click."}, {"name": "param", "comment": "Modifier keys held while clicking"}], "rawComment": "/**\n   * Click the element at the specified coordinates relative to the top-left of the element.\n   * @param relativeX Coordinate within the element, along the X-axis at which to click.\n   * @param relativeY Coordinate within the element, along the Y-axis at which to click.\n   * @param modifiers Modifier keys held while clicking\n   */", "memberType": "method", "memberTags": []}, {"name": "rightClick", "signatures": [], "implementation": {"params": [{"name": "relativeX", "description": "Coordinate within the element, along the X-axis at which to click.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "relativeY", "description": "Coordinate within the element, along the Y-axis at which to click.", "type": "number", "isOptional": false, "isRestParam": false}, {"name": "modifiers", "description": "Modifier keys held while clicking", "type": "ModifierKeys | undefined", "isOptional": true, "isRestParam": false}], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "rightClick", "description": "Right clicks on the element at the specified coordinates relative to the top-left of it.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Coordinate within the element, along the X-axis at which to click."}, {"name": "param", "comment": "Coordinate within the element, along the Y-axis at which to click."}, {"name": "param", "comment": "Modifier keys held while clicking"}], "rawComment": "/**\n   * Right clicks on the element at the specified coordinates relative to the top-left of it.\n   * @param relativeX Coordinate within the element, along the X-axis at which to click.\n   * @param relativeY Coordinate within the element, along the Y-axis at which to click.\n   * @param modifiers Modifier keys held while clicking\n   */"}, "entryType": "function", "description": "Right clicks on the element at the specified coordinates relative to the top-left of it.", "jsdocTags": [{"name": "param", "comment": "Coordinate within the element, along the X-axis at which to click."}, {"name": "param", "comment": "Coordinate within the element, along the Y-axis at which to click."}, {"name": "param", "comment": "Modifier keys held while clicking"}], "rawComment": "/**\n   * Right clicks on the element at the specified coordinates relative to the top-left of it.\n   * @param relativeX Coordinate within the element, along the X-axis at which to click.\n   * @param relativeY Coordinate within the element, along the Y-axis at which to click.\n   * @param modifiers Modifier keys held while clicking\n   */", "memberType": "method", "memberTags": []}, {"name": "focus", "signatures": [], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "focus", "description": "Focus the element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Focus the element. */"}, "entryType": "function", "description": "Focus the element.", "jsdocTags": [], "rawComment": "/** Focus the element. */", "memberType": "method", "memberTags": []}, {"name": "getCssValue", "signatures": [], "implementation": {"params": [{"name": "property", "description": "", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<string>", "generics": [], "name": "getCssValue", "description": "Get the computed value of the given CSS property for the element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Get the computed value of the given CSS property for the element. */"}, "entryType": "function", "description": "Get the computed value of the given CSS property for the element.", "jsdocTags": [], "rawComment": "/** Get the computed value of the given CSS property for the element. */", "memberType": "method", "memberTags": []}, {"name": "hover", "signatures": [], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "hover", "description": "Hovers the mouse over the element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Hovers the mouse over the element. */"}, "entryType": "function", "description": "Hovers the mouse over the element.", "jsdocTags": [], "rawComment": "/** Hovers the mouse over the element. */", "memberType": "method", "memberTags": []}, {"name": "mouseAway", "signatures": [], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "mouseAway", "description": "Moves the mouse away from the element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Moves the mouse away from the element. */"}, "entryType": "function", "description": "Moves the mouse away from the element.", "jsdocTags": [], "rawComment": "/** Moves the mouse away from the element. */", "memberType": "method", "memberTags": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "signatures": [], "implementation": {"params": [{"name": "keys", "description": "", "type": "(string | TestKey)[]", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Sends the given string to the input as a series of key presses. Also fires input events\nand attempts to add the string to the Element's value. Note that some environments cannot\nreproduce native browser behavior for keyboard shortcuts such as Tab, Ctrl + A, etc.", "entryType": "function", "jsdocTags": [{"name": "throws", "comment": "An error if no keys have been specified."}], "rawComment": "/**\n   * Sends the given string to the input as a series of key presses. Also fires input events\n   * and attempts to add the string to the Element's value. Note that some environments cannot\n   * reproduce native browser behavior for keyboard shortcuts such as Tab, Ctrl + A, etc.\n   * @throws An error if no keys have been specified.\n   */"}, "entryType": "function", "description": "Sends the given string to the input as a series of key presses. Also fires input events\nand attempts to add the string to the Element's value. Note that some environments cannot\nreproduce native browser behavior for keyboard shortcuts such as Tab, Ctrl + A, etc.", "jsdocTags": [{"name": "throws", "comment": "An error if no keys have been specified."}], "rawComment": "/**\n   * Sends the given string to the input as a series of key presses. Also fires input events\n   * and attempts to add the string to the Element's value. Note that some environments cannot\n   * reproduce native browser behavior for keyboard shortcuts such as Tab, Ctrl + A, etc.\n   * @throws An error if no keys have been specified.\n   */", "memberType": "method", "memberTags": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "signatures": [], "implementation": {"params": [{"name": "modifiers", "description": "", "type": "Modifier<PERSON>eys", "isOptional": false, "isRestParam": false}, {"name": "keys", "description": "", "type": "(string | TestKey)[]", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Sends the given string to the input as a series of key presses. Also fires input\nevents and attempts to add the string to the Element's value.", "entryType": "function", "jsdocTags": [{"name": "throws", "comment": "An error if no keys have been specified."}], "rawComment": "/**\n   * Sends the given string to the input as a series of key presses. Also fires input\n   * events and attempts to add the string to the Element's value.\n   * @throws An error if no keys have been specified.\n   */"}, "entryType": "function", "description": "Sends the given string to the input as a series of key presses. Also fires input\nevents and attempts to add the string to the Element's value.", "jsdocTags": [{"name": "throws", "comment": "An error if no keys have been specified."}], "rawComment": "/**\n   * Sends the given string to the input as a series of key presses. Also fires input\n   * events and attempts to add the string to the Element's value.\n   * @throws An error if no keys have been specified.\n   */", "memberType": "method", "memberTags": []}, {"name": "text", "signatures": [], "implementation": {"params": [{"name": "options", "description": "Options that affect what text is included.", "type": "TextOptions | undefined", "isOptional": true, "isRestParam": false}], "isNewType": false, "returnType": "Promise<string>", "generics": [], "name": "text", "description": "Gets the text from the element.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Options that affect what text is included."}], "rawComment": "/**\n   * Gets the text from the element.\n   * @param options Options that affect what text is included.\n   */"}, "entryType": "function", "description": "Gets the text from the element.", "jsdocTags": [{"name": "param", "comment": "Options that affect what text is included."}], "rawComment": "/**\n   * Gets the text from the element.\n   * @param options Options that affect what text is included.\n   */", "memberType": "method", "memberTags": []}, {"name": "setContenteditableValue", "signatures": [], "implementation": {"params": [{"name": "value", "description": "Value to be set on the element.", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "setContenteditableValue", "description": "Sets the value of a `contenteditable` element.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Value to be set on the element."}, {"name": "breaking-change", "comment": "16.0.0 Will become a required method."}], "rawComment": "/**\n   * Sets the value of a `contenteditable` element.\n   * @param value Value to be set on the element.\n   * @breaking-change 16.0.0 Will become a required method.\n   */"}, "entryType": "function", "description": "Sets the value of a `contenteditable` element.", "jsdocTags": [{"name": "param", "comment": "Value to be set on the element."}, {"name": "breaking-change", "comment": "16.0.0 Will become a required method."}], "rawComment": "/**\n   * Sets the value of a `contenteditable` element.\n   * @param value Value to be set on the element.\n   * @breaking-change 16.0.0 Will become a required method.\n   */", "memberType": "method", "memberTags": ["optional"]}, {"name": "getAttribute", "signatures": [], "implementation": {"params": [{"name": "name", "description": "", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<string | null>", "generics": [], "name": "getAttribute", "description": "Gets the value for the given attribute from the element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets the value for the given attribute from the element. */"}, "entryType": "function", "description": "Gets the value for the given attribute from the element.", "jsdocTags": [], "rawComment": "/** Gets the value for the given attribute from the element. */", "memberType": "method", "memberTags": []}, {"name": "hasClass", "signatures": [], "implementation": {"params": [{"name": "name", "description": "", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<boolean>", "generics": [], "name": "hasClass", "description": "Checks whether the element has the given class.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Checks whether the element has the given class. */"}, "entryType": "function", "description": "Checks whether the element has the given class.", "jsdocTags": [], "rawComment": "/** Checks whether the element has the given class. */", "memberType": "method", "memberTags": []}, {"name": "getDimensions", "signatures": [], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<ElementDimensions>", "generics": [], "name": "getDimensions", "description": "Gets the dimensions of the element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets the dimensions of the element. */"}, "entryType": "function", "description": "Gets the dimensions of the element.", "jsdocTags": [], "rawComment": "/** Gets the dimensions of the element. */", "memberType": "method", "memberTags": []}, {"name": "getProperty", "signatures": [], "implementation": {"params": [{"name": "name", "description": "", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T>", "generics": [{"name": "T", "default": "any"}], "name": "getProperty", "description": "Gets the value of a property of an element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets the value of a property of an element. */"}, "entryType": "function", "description": "Gets the value of a property of an element.", "jsdocTags": [], "rawComment": "/** Gets the value of a property of an element. */", "memberType": "method", "memberTags": []}, {"name": "matchesSelector", "signatures": [], "implementation": {"params": [{"name": "selector", "description": "", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<boolean>", "generics": [], "name": "matchesSelector", "description": "Checks whether this element matches the given selector.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Checks whether this element matches the given selector. */"}, "entryType": "function", "description": "Checks whether this element matches the given selector.", "jsdocTags": [], "rawComment": "/** Checks whether this element matches the given selector. */", "memberType": "method", "memberTags": []}, {"name": "isFocused", "signatures": [], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<boolean>", "generics": [], "name": "isFocused", "description": "Checks whether the element is focused.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Checks whether the element is focused. */"}, "entryType": "function", "description": "Checks whether the element is focused.", "jsdocTags": [], "rawComment": "/** Checks whether the element is focused. */", "memberType": "method", "memberTags": []}, {"name": "setInputValue", "signatures": [], "implementation": {"params": [{"name": "value", "description": "", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "setInputValue", "description": "Sets the value of a property of an input.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Sets the value of a property of an input. */"}, "entryType": "function", "description": "Sets the value of a property of an input.", "jsdocTags": [], "rawComment": "/** Sets the value of a property of an input. */", "memberType": "method", "memberTags": []}, {"name": "selectOptions", "signatures": [], "implementation": {"params": [{"name": "optionIndexes", "description": "", "type": "number[]", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "selectOptions", "description": "Selects the options at the specified indexes inside of a native `select` element.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Selects the options at the specified indexes inside of a native `select` element. */"}, "entryType": "function", "description": "Selects the options at the specified indexes inside of a native `select` element.", "jsdocTags": [], "rawComment": "/** Selects the options at the specified indexes inside of a native `select` element. */", "memberType": "method", "memberTags": []}, {"name": "dispatchEvent", "signatures": [], "implementation": {"params": [{"name": "name", "description": "Name of the event to be dispatched.", "type": "string", "isOptional": false, "isRestParam": false}, {"name": "data", "description": "", "type": "Record<string, EventData> | undefined", "isOptional": true, "isRestParam": false}], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "dispatchEvent", "description": "Dispatches an event with a particular name.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "Name of the event to be dispatched."}], "rawComment": "/**\n   * Dispatches an event with a particular name.\n   * @param name Name of the event to be dispatched.\n   */"}, "entryType": "function", "description": "Dispatches an event with a particular name.", "jsdocTags": [{"name": "param", "comment": "Name of the event to be dispatched."}], "rawComment": "/**\n   * Dispatches an event with a particular name.\n   * @param name Name of the event to be dispatched.\n   */", "memberType": "method", "memberTags": []}], "generics": [], "description": "This acts as a common interface for DOM elements across both unit and e2e tests. It is the\ninterface through which the ComponentHarness interacts with the component's DOM.", "jsdocTags": [], "rawComment": "/**\n * This acts as a common interface for DOM elements across both unit and e2e tests. It is the\n * interface through which the ComponentHarness interacts with the component's DOM.\n */", "implements": [], "source": {"filePath": "/src/cdk/testing/test-element.ts", "startLine": 75, "endLine": 181}}, {"name": "stopHandlingAutoChangeDetectionStatus", "signatures": [{"name": "stopHandlingAutoChangeDetectionStatus", "entryType": "function", "description": "Allows a `HarnessEnvironment` to stop handling auto change detection status changes.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Allows a `HarnessEnvironment` to stop handling auto change detection status changes. */", "returnType": "void"}], "implementation": {"params": [], "isNewType": false, "returnType": "void", "generics": [], "name": "stopHandlingAutoChangeDetectionStatus", "description": "Allows a `HarnessEnvironment` to stop handling auto change detection status changes.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Allows a `HarnessEnvironment` to stop handling auto change detection status changes. */"}, "entryType": "function", "description": "Allows a `HarnessEnvironment` to stop handling auto change detection status changes.", "jsdocTags": [], "rawComment": "/** Allows a `HarnessEnvironment` to stop handling auto change detection status changes. */", "source": {"filePath": "src/cdk/testing/change-detection.ts", "startLine": 57, "endLine": 60}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isAbstract": false, "entryType": "interface", "members": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signatures": [], "implementation": {"params": [{"name": "selector", "description": "The selector for the root element of the new `HarnessLoader`", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader>", "generics": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Searches for an element with the given selector under the current instances's root element,\nand returns a `Ha<PERSON>ssLoader` rooted at the matching element. If multiple elements match the\nselector, the first is used. If no elements match, an error is thrown.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for the root element of the new `HarnessLoader`"}, {"name": "return", "comment": "A `HarnessLoader` rooted at the element matching the given selector."}, {"name": "throws", "comment": "If a matching element can't be found."}], "rawComment": "/**\n   * Searches for an element with the given selector under the current instances's root element,\n   * and returns a `<PERSON><PERSON>ssLoader` rooted at the matching element. If multiple elements match the\n   * selector, the first is used. If no elements match, an error is thrown.\n   * @param selector The selector for the root element of the new `<PERSON><PERSON><PERSON><PERSON>oader`\n   * @return A `HarnessLoader` rooted at the element matching the given selector.\n   * @throws If a matching element can't be found.\n   */"}, "entryType": "function", "description": "Searches for an element with the given selector under the current instances's root element,\nand returns a `Ha<PERSON>ssLoader` rooted at the matching element. If multiple elements match the\nselector, the first is used. If no elements match, an error is thrown.", "jsdocTags": [{"name": "param", "comment": "The selector for the root element of the new `HarnessLoader`"}, {"name": "return", "comment": "A `HarnessLoader` rooted at the element matching the given selector."}, {"name": "throws", "comment": "If a matching element can't be found."}], "rawComment": "/**\n   * Searches for an element with the given selector under the current instances's root element,\n   * and returns a `<PERSON><PERSON>ssLoader` rooted at the matching element. If multiple elements match the\n   * selector, the first is used. If no elements match, an error is thrown.\n   * @param selector The selector for the root element of the new `<PERSON><PERSON><PERSON><PERSON>oader`\n   * @return A `HarnessLoader` rooted at the element matching the given selector.\n   * @throws If a matching element can't be found.\n   */", "memberType": "method", "memberTags": []}, {"name": "getAllChildLoaders", "signatures": [], "implementation": {"params": [{"name": "selector", "description": "The selector for the root element of the new `HarnessLoader`", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader[]>", "generics": [], "name": "getAllChildLoaders", "description": "Searches for all elements with the given selector under the current instances's root element,\nand returns an array of `HarnessLoader`s, one for each matching element, rooted at that\nelement.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for the root element of the new `HarnessLoader`"}, {"name": "return", "comment": "A list of `HarnessLoader`s, one for each matching element, rooted at that element."}], "rawComment": "/**\n   * Searches for all elements with the given selector under the current instances's root element,\n   * and returns an array of `HarnessLoader`s, one for each matching element, rooted at that\n   * element.\n   * @param selector The selector for the root element of the new `<PERSON><PERSON><PERSON><PERSON>oader`\n   * @return A list of `HarnessLoader`s, one for each matching element, rooted at that element.\n   */"}, "entryType": "function", "description": "Searches for all elements with the given selector under the current instances's root element,\nand returns an array of `HarnessLoader`s, one for each matching element, rooted at that\nelement.", "jsdocTags": [{"name": "param", "comment": "The selector for the root element of the new `HarnessLoader`"}, {"name": "return", "comment": "A list of `HarnessLoader`s, one for each matching element, rooted at that element."}], "rawComment": "/**\n   * Searches for all elements with the given selector under the current instances's root element,\n   * and returns an array of `HarnessLoader`s, one for each matching element, rooted at that\n   * element.\n   * @param selector The selector for the root element of the new `<PERSON><PERSON><PERSON><PERSON>oader`\n   * @return A list of `HarnessLoader`s, one for each matching element, rooted at that element.\n   */", "memberType": "method", "memberTags": []}, {"name": "getHarness", "signatures": [], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "getHarness", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\nmatching components are found, a harness for the first one is returned. If no matching\ncomponent is found, an error is thrown.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An instance of the given harness type"}, {"name": "throws", "comment": "If a matching component instance can't be found."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `<PERSON><PERSON>ssLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\n   * matching components are found, a harness for the first one is returned. If no matching\n   * component is found, an error is thrown.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type\n   * @throws If a matching component instance can't be found.\n   */"}, "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\nmatching components are found, a harness for the first one is returned. If no matching\ncomponent is found, an error is thrown.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An instance of the given harness type"}, {"name": "throws", "comment": "If a matching component instance can't be found."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `<PERSON><PERSON>ssLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\n   * matching components are found, a harness for the first one is returned. If no matching\n   * component is found, an error is thrown.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type\n   * @throws If a matching component instance can't be found.\n   */", "memberType": "method", "memberTags": []}, {"name": "getHarnessOrNull", "signatures": [], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T | null>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "getHarnessOrNull", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\nmatching components are found, a harness for the first one is returned. If no matching\ncomponent is found, null is returned.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An instance of the given harness type (or null if not found)."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `<PERSON><PERSON>ssLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\n   * matching components are found, a harness for the first one is returned. If no matching\n   * component is found, null is returned.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type (or null if not found).\n   */"}, "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\nmatching components are found, a harness for the first one is returned. If no matching\ncomponent is found, null is returned.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An instance of the given harness type (or null if not found)."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `<PERSON><PERSON>ssLoader`'s root element, and returns a `ComponentHarness` for that instance. If multiple\n   * matching components are found, a harness for the first one is returned. If no matching\n   * component is found, null is returned.\n   * @param query A query for a harness to create\n   * @return An instance of the given harness type (or null if not found).\n   */", "memberType": "method", "memberTags": []}, {"name": "getHarnessAtIndex", "signatures": [], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}, {"name": "index", "description": "The zero-indexed offset of the matching component instance to return", "type": "number", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "getHarnessAtIndex", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns a `ComponentHarness` for the instance on the page\nat the given index. If no matching component exists at that index, an error is thrown.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "param", "comment": "The zero-indexed offset of the matching component instance to return"}, {"name": "return", "comment": "An instance of the given harness type."}, {"name": "throws", "comment": "If a matching component instance can't be found at the given index."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `<PERSON><PERSON>ssLoader`'s root element, and returns a `ComponentHarness` for the instance on the page\n   * at the given index. If no matching component exists at that index, an error is thrown.\n   * @param query A query for a harness to create\n   * @param index The zero-indexed offset of the matching component instance to return\n   * @return An instance of the given harness type.\n   * @throws If a matching component instance can't be found at the given index.\n   */"}, "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns a `ComponentHarness` for the instance on the page\nat the given index. If no matching component exists at that index, an error is thrown.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "param", "comment": "The zero-indexed offset of the matching component instance to return"}, {"name": "return", "comment": "An instance of the given harness type."}, {"name": "throws", "comment": "If a matching component instance can't be found at the given index."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `<PERSON><PERSON>ssLoader`'s root element, and returns a `ComponentHarness` for the instance on the page\n   * at the given index. If no matching component exists at that index, an error is thrown.\n   * @param query A query for a harness to create\n   * @param index The zero-indexed offset of the matching component instance to return\n   * @return An instance of the given harness type.\n   * @throws If a matching component instance can't be found at the given index.\n   */", "memberType": "method", "memberTags": []}, {"name": "get<PERSON>ll<PERSON><PERSON><PERSON><PERSON>", "signatures": [], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T[]>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "get<PERSON>ll<PERSON><PERSON><PERSON><PERSON>", "description": "Searches for all instances of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns a list `ComponentHarness` for each instance.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "A list instances of the given harness type."}], "rawComment": "/**\n   * Searches for all instances of the component corresponding to the given harness type under the\n   * `<PERSON>rnessLoader`'s root element, and returns a list `ComponentHarness` for each instance.\n   * @param query A query for a harness to create\n   * @return A list instances of the given harness type.\n   */"}, "entryType": "function", "description": "Searches for all instances of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns a list `ComponentHarness` for each instance.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "A list instances of the given harness type."}], "rawComment": "/**\n   * Searches for all instances of the component corresponding to the given harness type under the\n   * `<PERSON>rnessLoader`'s root element, and returns a list `ComponentHarness` for each instance.\n   * @param query A query for a harness to create\n   * @return A list instances of the given harness type.\n   */", "memberType": "method", "memberTags": []}, {"name": "countHarnesses", "signatures": [], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<number>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "countHarnesses", "description": "Searches for all instances of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns the total count of all matching components.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An integer indicating the number of instances that were found."}], "rawComment": "/**\n   * Searches for all instances of the component corresponding to the given harness type under the\n   * `<PERSON><PERSON>ssLoader`'s root element, and returns the total count of all matching components.\n   * @param query A query for a harness to create\n   * @return An integer indicating the number of instances that were found.\n   */"}, "entryType": "function", "description": "Searches for all instances of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns the total count of all matching components.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "An integer indicating the number of instances that were found."}], "rawComment": "/**\n   * Searches for all instances of the component corresponding to the given harness type under the\n   * `<PERSON><PERSON>ssLoader`'s root element, and returns the total count of all matching components.\n   * @param query A query for a harness to create\n   * @return An integer indicating the number of instances that were found.\n   */", "memberType": "method", "memberTags": []}, {"name": "hasH<PERSON>ness", "signatures": [], "implementation": {"params": [{"name": "query", "description": "A query for a harness to create", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<boolean>", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "hasH<PERSON>ness", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns a boolean indicating if any were found.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "A boolean indicating if an instance was found."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `<PERSON><PERSON>ssLoader`'s root element, and returns a boolean indicating if any were found.\n   * @param query A query for a harness to create\n   * @return A boolean indicating if an instance was found.\n   */"}, "entryType": "function", "description": "Searches for an instance of the component corresponding to the given harness type under the\n`HarnessLoader`'s root element, and returns a boolean indicating if any were found.", "jsdocTags": [{"name": "param", "comment": "A query for a harness to create"}, {"name": "return", "comment": "A boolean indicating if an instance was found."}], "rawComment": "/**\n   * Searches for an instance of the component corresponding to the given harness type under the\n   * `<PERSON><PERSON>ssLoader`'s root element, and returns a boolean indicating if any were found.\n   * @param query A query for a harness to create\n   * @return A boolean indicating if an instance was found.\n   */", "memberType": "method", "memberTags": []}], "generics": [], "description": "Interface used to load ComponentHarness objects. This interface is used by test authors to\ninstantiate `ComponentHarness`es.", "jsdocTags": [], "rawComment": "/**\n * Interface used to load ComponentHarness objects. This interface is used by test authors to\n * instantiate `ComponentHarness`es.\n */", "implements": [], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 81, "endLine": 156}}, {"name": "manualChangeDetection", "signatures": [{"name": "manualChangeDetection", "entryType": "function", "description": "Disables the harness system's auto change detection for the duration of the given function.", "generics": [{"name": "T"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The function to disable auto change detection for."}, {"name": "return", "comment": "The result of the given function."}], "params": [{"name": "fn", "description": "The function to disable auto change detection for.", "type": "() => Promise<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n * Disables the harness system's auto change detection for the duration of the given function.\n * @param fn The function to disable auto change detection for.\n * @return The result of the given function.\n */", "returnType": "Promise<T>"}], "implementation": {"params": [{"name": "fn", "description": "The function to disable auto change detection for.", "type": "() => Promise<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T>", "generics": [{"name": "T"}], "name": "manualChangeDetection", "description": "Disables the harness system's auto change detection for the duration of the given function.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The function to disable auto change detection for."}, {"name": "return", "comment": "The result of the given function."}], "rawComment": "/**\n * Disables the harness system's auto change detection for the duration of the given function.\n * @param fn The function to disable auto change detection for.\n * @return The result of the given function.\n */"}, "entryType": "function", "description": "Disables the harness system's auto change detection for the duration of the given function.", "jsdocTags": [{"name": "param", "comment": "The function to disable auto change detection for."}, {"name": "return", "comment": "The result of the given function."}], "rawComment": "/**\n * Disables the harness system's auto change detection for the duration of the given function.\n * @param fn The function to disable auto change detection for.\n * @return The result of the given function.\n */", "source": {"filePath": "src/cdk/testing/change-detection.ts", "startLine": 118, "endLine": 120}}, {"name": "parallel", "signatures": [{"name": "parallel", "entryType": "function", "description": "Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\ndetection over the entire operation such that change detection occurs exactly once before\nresolving the values and once after.", "generics": [{"name": "T1"}, {"name": "T2"}, {"name": "T3"}, {"name": "T4"}, {"name": "T5"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A getter for the async values to resolve in parallel with batched change detection."}, {"name": "return", "comment": "The resolved values."}], "params": [{"name": "values", "description": "A getter for the async values to resolve in parallel with batched change detection.", "type": "() => [T1 | PromiseLike<T1>, T2 | PromiseLike<T2>, T3 | PromiseLike<T3>, T4 | PromiseLike<T4>, T5 | PromiseLike<T5>]", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */", "returnType": "Promise<[T1, T2, T3, T4, T5]>"}, {"name": "parallel", "entryType": "function", "description": "Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\ndetection over the entire operation such that change detection occurs exactly once before\nresolving the values and once after.", "generics": [{"name": "T1"}, {"name": "T2"}, {"name": "T3"}, {"name": "T4"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A getter for the async values to resolve in parallel with batched change detection."}, {"name": "return", "comment": "The resolved values."}], "params": [{"name": "values", "description": "A getter for the async values to resolve in parallel with batched change detection.", "type": "() => [T1 | PromiseLike<T1>, T2 | PromiseLike<T2>, T3 | PromiseLike<T3>, T4 | PromiseLike<T4>]", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */", "returnType": "Promise<[T1, T2, T3, T4]>"}, {"name": "parallel", "entryType": "function", "description": "Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\ndetection over the entire operation such that change detection occurs exactly once before\nresolving the values and once after.", "generics": [{"name": "T1"}, {"name": "T2"}, {"name": "T3"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A getter for the async values to resolve in parallel with batched change detection."}, {"name": "return", "comment": "The resolved values."}], "params": [{"name": "values", "description": "A getter for the async values to resolve in parallel with batched change detection.", "type": "() => [T1 | PromiseLike<T1>, T2 | PromiseLike<T2>, T3 | PromiseLike<T3>]", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */", "returnType": "Promise<[T1, T2, T3]>"}, {"name": "parallel", "entryType": "function", "description": "Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\ndetection over the entire operation such that change detection occurs exactly once before\nresolving the values and once after.", "generics": [{"name": "T1"}, {"name": "T2"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A getter for the async values to resolve in parallel with batched change detection."}, {"name": "return", "comment": "The resolved values."}], "params": [{"name": "values", "description": "A getter for the async values to resolve in parallel with batched change detection.", "type": "() => [T1 | PromiseLike<T1>, T2 | PromiseLike<T2>]", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */", "returnType": "Promise<[T1, T2]>"}, {"name": "parallel", "entryType": "function", "description": "Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\ndetection over the entire operation such that change detection occurs exactly once before\nresolving the values and once after.", "generics": [{"name": "T"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A getter for the async values to resolve in parallel with batched change detection."}, {"name": "return", "comment": "The resolved values."}], "params": [{"name": "values", "description": "A getter for the async values to resolve in parallel with batched change detection.", "type": "() => (T | PromiseLike<T>)[]", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */", "returnType": "Promise<T[]>"}], "implementation": {"params": [{"name": "values", "description": "A getter for the async values to resolve in parallel with batched change detection.", "type": "() => Iterable<T | PromiseLike<T>>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<[T1, T2, T3, T4, T5]>", "generics": [{"name": "T"}], "name": "parallel", "description": "Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\ndetection over the entire operation such that change detection occurs exactly once before\nresolving the values and once after.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A getter for the async values to resolve in parallel with batched change detection."}, {"name": "return", "comment": "The resolved values."}], "rawComment": "/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */"}, "entryType": "function", "description": "Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\ndetection over the entire operation such that change detection occurs exactly once before\nresolving the values and once after.", "jsdocTags": [{"name": "param", "comment": "A getter for the async values to resolve in parallel with batched change detection."}, {"name": "return", "comment": "The resolved values."}], "rawComment": "/**\n * Resolves the given list of async values in parallel (i.e. via Promise.all) while batching change\n * detection over the entire operation such that change detection occurs exactly once before\n * resolving the values and once after.\n * @param values A getter for the async values to resolve in parallel with batched change detection.\n * @return The resolved values.\n */", "source": {"filePath": "src/cdk/testing/change-detection.ts", "startLine": 129, "endLine": 137}}, {"name": "TextOptions", "isAbstract": false, "entryType": "interface", "members": [{"name": "exclude", "type": "string | undefined", "memberType": "property", "memberTags": ["optional"], "description": "Optional selector for elements whose content should be excluded from the text string.", "jsdocTags": []}], "generics": [], "description": "Options that affect the text returned by `TestElement.text`.", "jsdocTags": [], "rawComment": "/**\n * Options that affect the text returned by `TestElement.text`.\n */", "implements": [], "source": {"filePath": "/src/cdk/testing/test-element.ts", "startLine": 186, "endLine": 189}}, {"name": "LocatorFactory", "isAbstract": false, "entryType": "interface", "members": [{"name": "documentRootLocatorFactory", "signatures": [], "implementation": {"params": [], "isNewType": false, "returnType": "LocatorFactory", "generics": [], "name": "documentRootLocatorFactory", "description": "Gets a locator factory rooted at the document root.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets a locator factory rooted at the document root. */"}, "entryType": "function", "description": "Gets a locator factory rooted at the document root.", "jsdocTags": [], "rawComment": "/** Gets a locator factory rooted at the document root. */", "memberType": "method", "memberTags": []}, {"name": "rootElement", "type": "TestElement", "memberType": "property", "memberTags": [], "description": "The root element of this `LocatorFactory` as a `TestElement`.", "jsdocTags": []}, {"name": "locatorFor", "signatures": [], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T>>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorFor", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the root element of this `LocatorFactory`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait lf.locatorFor('span')()            // Throws because the `Promise` rejects\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\neach query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `LocatorFactory`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the root element of this `LocatorFactory`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait lf.locatorFor('span')()            // Throws because the `Promise` rejects\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\neach query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `LocatorFactory`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */", "memberType": "method", "memberTags": []}, {"name": "locatorForOptional", "signatures": [], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T> | null>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorForOptional", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the root element of this `LocatorFactory`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait lf.locatorForOptional('span')()            // Gets `null`\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\nresult types for each query or null."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `LocatorFactory`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the root element of this `LocatorFactory`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait lf.locatorForOptional('span')()            // Gets `null`\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\nresult types for each query or null."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the root element of this `LocatorFactory`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await lf.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await lf.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await lf.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */", "memberType": "method", "memberTags": []}, {"name": "locatorForAll", "signatures": [], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T>[]>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorForAll", "description": "Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\nor elements under the root element of this `LocatorFactory`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n`IdIsD1Harness.hostSelector` is `'#d1'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\n// Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\nawait lf.locatorForAll(DivHarness, 'div')()\n// Gets [TestElement for #d1, TestElement for #d2]\nawait lf.locatorForAll('div', '#d1')()\n// Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\nawait lf.locatorForAll(DivHarness, IdIsD1Harness)()\n// Gets []\nawait lf.locatorForAll('span')()\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for all\nelements and harnesses matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If an element matches more than\none `ComponentHarness` class, the locator gets an instance of each for the same element. If\nan element matches multiple `string` selectors, only one `TestElement` instance is returned\nfor that element. The type that the `Promise` resolves to is an array where each element is\nthe union of all result types for each query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the root element of this `LocatorFactory`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await lf.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await lf.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await lf.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await lf.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\nor elements under the root element of this `LocatorFactory`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n`IdIsD1Harness.hostSelector` is `'#d1'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\n// Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\nawait lf.locatorForAll(DivHarness, 'div')()\n// Gets [TestElement for #d1, TestElement for #d2]\nawait lf.locatorForAll('div', '#d1')()\n// Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\nawait lf.locatorForAll(DivHarness, IdIsD1Harness)()\n// Gets []\nawait lf.locatorForAll('span')()\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for all\nelements and harnesses matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If an element matches more than\none `ComponentHarness` class, the locator gets an instance of each for the same element. If\nan element matches multiple `string` selectors, only one `TestElement` instance is returned\nfor that element. The type that the `Promise` resolves to is an array where each element is\nthe union of all result types for each query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the root element of this `LocatorFactory`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await lf.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await lf.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await lf.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await lf.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */", "memberType": "method", "memberTags": []}, {"name": "rootHarness<PERSON>oader", "signatures": [], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<HarnessLoader>", "generics": [], "name": "rootHarness<PERSON>oader", "description": "", "entryType": "function", "jsdocTags": [{"name": "return", "comment": "A `HarnessLoader` rooted at the root element of this `LocatorFactory`."}], "rawComment": "/** @return A `<PERSON><PERSON>ssLoader` rooted at the root element of this `LocatorFactory`. */"}, "entryType": "function", "description": "", "jsdocTags": [{"name": "return", "comment": "A `HarnessLoader` rooted at the root element of this `LocatorFactory`."}], "rawComment": "/** @return A `<PERSON><PERSON>ssLoader` rooted at the root element of this `LocatorFactory`. */", "memberType": "method", "memberTags": []}, {"name": "harnessLoaderFor", "signatures": [], "implementation": {"params": [{"name": "selector", "description": "The selector for the root element.", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader>", "generics": [], "name": "harnessLoaderFor", "description": "Gets a `<PERSON><PERSON><PERSON><PERSON>oader` instance for an element under the root of this `LocatorFactory`.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A `HarnessLoader` rooted at the first element matching the given selector."}, {"name": "throws", "comment": "If no matching element is found for the given selector."}], "rawComment": "/**\n   * Gets a `<PERSON><PERSON><PERSON><PERSON>oader` instance for an element under the root of this `LocatorFactory`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector.\n   * @throws If no matching element is found for the given selector.\n   */"}, "entryType": "function", "description": "Gets a `<PERSON><PERSON><PERSON><PERSON>oader` instance for an element under the root of this `LocatorFactory`.", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A `HarnessLoader` rooted at the first element matching the given selector."}, {"name": "throws", "comment": "If no matching element is found for the given selector."}], "rawComment": "/**\n   * Gets a `<PERSON><PERSON><PERSON><PERSON>oader` instance for an element under the root of this `LocatorFactory`.\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector.\n   * @throws If no matching element is found for the given selector.\n   */", "memberType": "method", "memberTags": []}, {"name": "harnessLoaderForOptional", "signatures": [], "implementation": {"params": [{"name": "selector", "description": "The selector for the root element.", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader | null>", "generics": [], "name": "harnessLoaderForOptional", "description": "Gets a `<PERSON><PERSON><PERSON><PERSON>oader` instance for an element under the root of this `LocatorFactory`", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A `HarnessLoader` rooted at the first element matching the given selector, or null if\nno matching element is found."}], "rawComment": "/**\n   * Gets a `<PERSON><PERSON><PERSON><PERSON>oader` instance for an element under the root of this `LocatorFactory`\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector, or null if\n   *     no matching element is found.\n   */"}, "entryType": "function", "description": "Gets a `<PERSON><PERSON><PERSON><PERSON>oader` instance for an element under the root of this `LocatorFactory`", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A `HarnessLoader` rooted at the first element matching the given selector, or null if\nno matching element is found."}], "rawComment": "/**\n   * Gets a `<PERSON><PERSON><PERSON><PERSON>oader` instance for an element under the root of this `LocatorFactory`\n   * @param selector The selector for the root element.\n   * @return A `HarnessLoader` rooted at the first element matching the given selector, or null if\n   *     no matching element is found.\n   */", "memberType": "method", "memberTags": []}, {"name": "harnessLoaderForAll", "signatures": [], "implementation": {"params": [{"name": "selector", "description": "The selector for the root element.", "type": "string", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader[]>", "generics": [], "name": "harnessLoaderForAll", "description": "Gets a list of `HarnessLoader` instances, one for each matching element.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A list of `HarnessLoader`, one rooted at each element matching the given selector."}], "rawComment": "/**\n   * Gets a list of `HarnessLoader` instances, one for each matching element.\n   * @param selector The selector for the root element.\n   * @return A list of `HarnessLoader`, one rooted at each element matching the given selector.\n   */"}, "entryType": "function", "description": "Gets a list of `HarnessLoader` instances, one for each matching element.", "jsdocTags": [{"name": "param", "comment": "The selector for the root element."}, {"name": "return", "comment": "A list of `HarnessLoader`, one rooted at each element matching the given selector."}], "rawComment": "/**\n   * Gets a list of `HarnessLoader` instances, one for each matching element.\n   * @param selector The selector for the root element.\n   * @return A list of `HarnessLoader`, one rooted at each element matching the given selector.\n   */", "memberType": "method", "memberTags": []}, {"name": "forceStabilize", "signatures": [], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "forceStabilize", "description": "Flushes change detection and async tasks captured in the Angular zone.\nIn most cases it should not be necessary to call this manually. However, there may be some edge\ncases where it is needed to fully flush animation events.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Flushes change detection and async tasks captured in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */"}, "entryType": "function", "description": "Flushes change detection and async tasks captured in the Angular zone.\nIn most cases it should not be necessary to call this manually. However, there may be some edge\ncases where it is needed to fully flush animation events.", "jsdocTags": [], "rawComment": "/**\n   * Flushes change detection and async tasks captured in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */", "memberType": "method", "memberTags": []}, {"name": "waitForTasksOutsideAngular", "signatures": [], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "waitForTasksOutsideAngular", "description": "Waits for all scheduled or running async tasks to complete. This allows harness\nauthors to wait for async tasks outside of the Angular zone.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */"}, "entryType": "function", "description": "Waits for all scheduled or running async tasks to complete. This allows harness\nauthors to wait for async tasks outside of the Angular zone.", "jsdocTags": [], "rawComment": "/**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */", "memberType": "method", "memberTags": []}], "generics": [], "description": "Interface used to create asynchronous locator functions used find elements and component\nharnesses. This interface is used by `ComponentHarness` authors to create locator functions for\ntheir `ComponentHarness` subclass.", "jsdocTags": [], "rawComment": "/**\n * Interface used to create asynchronous locator functions used find elements and component\n * harnesses. This interface is used by `ComponentHarness` authors to create locator functions for\n * their `ComponentHarness` subclass.\n */", "implements": [], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 163, "endLine": 318}}, {"name": "ComponentHarness", "isAbstract": true, "entryType": "undecorated_class", "members": [{"name": "constructor", "signatures": [], "implementation": {"params": [{"name": "locatorFactory", "description": "", "type": "LocatorFactory", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "ComponentHarness", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "host", "signatures": [{"name": "host", "entryType": "function", "description": "Gets a `Promise` for the `TestElement` representing the host element of the component.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Gets a `Promise` for the `TestElement` representing the host element of the component. */", "returnType": "Promise<TestElement>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<TestElement>", "generics": [], "name": "host", "description": "Gets a `Promise` for the `TestElement` representing the host element of the component.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets a `Promise` for the `TestElement` representing the host element of the component. */"}, "entryType": "function", "description": "Gets a `Promise` for the `TestElement` representing the host element of the component.", "jsdocTags": [], "rawComment": "/** Gets a `Promise` for the `TestElement` representing the host element of the component. */", "memberType": "method", "memberTags": []}, {"name": "documentRootLocatorFactory", "signatures": [{"name": "documentRootLocatorFactory", "entryType": "function", "description": "Gets a `LocatorFactory` for the document root element. This factory can be used to create\nlocators for elements that a component creates outside of its own root element. (e.g. by\nappending to document.body).", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Gets a `LocatorFactory` for the document root element. This factory can be used to create\n   * locators for elements that a component creates outside of its own root element. (e.g. by\n   * appending to document.body).\n   */", "returnType": "LocatorFactory"}], "implementation": {"params": [], "isNewType": false, "returnType": "LocatorFactory", "generics": [], "name": "documentRootLocatorFactory", "description": "Gets a `LocatorFactory` for the document root element. This factory can be used to create\nlocators for elements that a component creates outside of its own root element. (e.g. by\nappending to document.body).", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Gets a `LocatorFactory` for the document root element. This factory can be used to create\n   * locators for elements that a component creates outside of its own root element. (e.g. by\n   * appending to document.body).\n   */"}, "entryType": "function", "description": "Gets a `LocatorFactory` for the document root element. This factory can be used to create\nlocators for elements that a component creates outside of its own root element. (e.g. by\nappending to document.body).", "jsdocTags": [], "rawComment": "/**\n   * Gets a `LocatorFactory` for the document root element. This factory can be used to create\n   * locators for elements that a component creates outside of its own root element. (e.g. by\n   * appending to document.body).\n   */", "memberType": "method", "memberTags": ["protected"]}, {"name": "locatorFor", "signatures": [{"name": "locatorFor", "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorFor('span')()            // Throws because the `Promise` rejects\n```", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\neach query."}], "params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */", "returnType": "() => Promise<LocatorFnResult<T>>"}], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T>>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorFor", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorFor('span')()            // Throws because the `Promise` rejects\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\neach query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorFor('span')()            // Throws because the `Promise` rejects\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\neach query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */", "memberType": "method", "memberTags": ["protected"]}, {"name": "locatorForOptional", "signatures": [{"name": "locatorForOptional", "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorForOptional('span')()            // Gets `null`\n```", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\nresult types for each query or null."}], "params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */", "returnType": "() => Promise<LocatorFnResult<T> | null>"}], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T> | null>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorForOptional", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorForOptional('span')()            // Gets `null`\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\nresult types for each query or null."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorForOptional('span')()            // Gets `null`\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\nresult types for each query or null."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */", "memberType": "method", "memberTags": ["protected"]}, {"name": "locatorForAll", "signatures": [{"name": "locatorForAll", "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\nor elements under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n`IdIsD1Harness.hostSelector` is `'#d1'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\n// Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\nawait ch.locatorForAll(DivHarness, 'div')()\n// Gets [TestElement for #d1, TestElement for #d2]\nawait ch.locatorForAll('div', '#d1')()\n// Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\nawait ch.locatorForAll(DivHarness, IdIsD1Harness)()\n// Gets []\nawait ch.locatorForAll('span')()\n```", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for all\nelements and harnesses matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If an element matches more than\none `ComponentHarness` class, the locator gets an instance of each for the same element. If\nan element matches multiple `string` selectors, only one `TestElement` instance is returned\nfor that element. The type that the `Promise` resolves to is an array where each element is\nthe union of all result types for each query."}], "params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await ch.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await ch.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await ch.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await ch.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */", "returnType": "() => Promise<LocatorFnResult<T>[]>"}], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T>[]>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorForAll", "description": "Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\nor elements under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n`IdIsD1Harness.hostSelector` is `'#d1'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\n// Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\nawait ch.locatorForAll(DivHarness, 'div')()\n// Gets [TestElement for #d1, TestElement for #d2]\nawait ch.locatorForAll('div', '#d1')()\n// Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\nawait ch.locatorForAll(DivHarness, IdIsD1Harness)()\n// Gets []\nawait ch.locatorForAll('span')()\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for all\nelements and harnesses matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If an element matches more than\none `ComponentHarness` class, the locator gets an instance of each for the same element. If\nan element matches multiple `string` selectors, only one `TestElement` instance is returned\nfor that element. The type that the `Promise` resolves to is an array where each element is\nthe union of all result types for each query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await ch.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await ch.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await ch.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await ch.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\nor elements under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n`IdIsD1Harness.hostSelector` is `'#d1'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\n// Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\nawait ch.locatorForAll(DivHarness, 'div')()\n// Gets [TestElement for #d1, TestElement for #d2]\nawait ch.locatorForAll('div', '#d1')()\n// Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\nawait ch.locatorForAll(DivHarness, IdIsD1Harness)()\n// Gets []\nawait ch.locatorForAll('span')()\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for all\nelements and harnesses matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If an element matches more than\none `ComponentHarness` class, the locator gets an instance of each for the same element. If\nan element matches multiple `string` selectors, only one `TestElement` instance is returned\nfor that element. The type that the `Promise` resolves to is an array where each element is\nthe union of all result types for each query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await ch.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await ch.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await ch.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await ch.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */", "memberType": "method", "memberTags": ["protected"]}, {"name": "forceStabilize", "signatures": [{"name": "forceStabilize", "entryType": "function", "description": "Flushes change detection and async tasks in the Angular zone.\nIn most cases it should not be necessary to call this manually. However, there may be some edge\ncases where it is needed to fully flush animation events.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Flushes change detection and async tasks in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */", "returnType": "Promise<void>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "forceStabilize", "description": "Flushes change detection and async tasks in the Angular zone.\nIn most cases it should not be necessary to call this manually. However, there may be some edge\ncases where it is needed to fully flush animation events.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Flushes change detection and async tasks in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */"}, "entryType": "function", "description": "Flushes change detection and async tasks in the Angular zone.\nIn most cases it should not be necessary to call this manually. However, there may be some edge\ncases where it is needed to fully flush animation events.", "jsdocTags": [], "rawComment": "/**\n   * Flushes change detection and async tasks in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */", "memberType": "method", "memberTags": ["protected"]}, {"name": "waitForTasksOutsideAngular", "signatures": [{"name": "waitForTasksOutsideAngular", "entryType": "function", "description": "Waits for all scheduled or running async tasks to complete. This allows harness\nauthors to wait for async tasks outside of the Angular zone.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */", "returnType": "Promise<void>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "waitForTasksOutsideAngular", "description": "Waits for all scheduled or running async tasks to complete. This allows harness\nauthors to wait for async tasks outside of the Angular zone.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */"}, "entryType": "function", "description": "Waits for all scheduled or running async tasks to complete. This allows harness\nauthors to wait for async tasks outside of the Angular zone.", "jsdocTags": [], "rawComment": "/**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */", "memberType": "method", "memberTags": ["protected"]}], "generics": [], "description": "Base class for component test harnesses that all component harness authors should extend. This\nbase component harness provides the basic ability to locate element and sub-component harnesses.", "jsdocTags": [], "rawComment": "/**\n * Base class for component test harnesses that all component harness authors should extend. This\n * base component harness provides the basic ability to locate element and sub-component harnesses.\n */", "implements": [], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 324, "endLine": 473}}, {"name": "ContentContainerComponentHarness", "isAbstract": true, "entryType": "undecorated_class", "members": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "signatures": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "entryType": "function", "description": "Gets a `HarnessLoader` that searches for harnesses under the first element matching the given\nselector within the current harness's content.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The selector for an element in the component's content."}, {"name": "returns", "comment": "A `HarnessLoader` that searches for harnesses under the given selector."}], "params": [{"name": "selector", "description": "The selector for an element in the component's content.", "type": "S", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Gets a `HarnessLoader` that searches for harnesses under the first element matching the given\n   * selector within the current harness's content.\n   * @param selector The selector for an element in the component's content.\n   * @returns A `HarnessLoader` that searches for harnesses under the given selector.\n   */", "returnType": "Promise<HarnessLoader>"}], "implementation": {"params": [{"name": "selector", "description": "The selector for an element in the component's content.", "type": "S", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader>", "returnDescription": "A `HarnessLoader` that searches for harnesses under the given selector.", "generics": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Gets a `HarnessLoader` that searches for harnesses under the first element matching the given\nselector within the current harness's content.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for an element in the component's content."}, {"name": "returns", "comment": "A `HarnessLoader` that searches for harnesses under the given selector."}], "rawComment": "/**\n   * Gets a `HarnessLoader` that searches for harnesses under the first element matching the given\n   * selector within the current harness's content.\n   * @param selector The selector for an element in the component's content.\n   * @returns A `HarnessLoader` that searches for harnesses under the given selector.\n   */"}, "entryType": "function", "description": "Gets a `HarnessLoader` that searches for harnesses under the first element matching the given\nselector within the current harness's content.", "jsdocTags": [{"name": "param", "comment": "The selector for an element in the component's content."}, {"name": "returns", "comment": "A `HarnessLoader` that searches for harnesses under the given selector."}], "rawComment": "/**\n   * Gets a `HarnessLoader` that searches for harnesses under the first element matching the given\n   * selector within the current harness's content.\n   * @param selector The selector for an element in the component's content.\n   * @returns A `HarnessLoader` that searches for harnesses under the given selector.\n   */", "memberType": "method", "memberTags": []}, {"name": "getAllChildLoaders", "signatures": [{"name": "getAllChildLoaders", "entryType": "function", "description": "Gets a list of `HarnessLoader` for each element matching the given selector under the current\nharness's cotnent that searches for harnesses under that element.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The selector for elements in the component's content."}, {"name": "returns", "comment": "A list of `HarnessLoader` for each element matching the given selector."}], "params": [{"name": "selector", "description": "The selector for elements in the component's content.", "type": "S", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Gets a list of `HarnessLoader` for each element matching the given selector under the current\n   * harness's cotnent that searches for harnesses under that element.\n   * @param selector The selector for elements in the component's content.\n   * @returns A list of `HarnessLoader` for each element matching the given selector.\n   */", "returnType": "Promise<HarnessLoader[]>"}], "implementation": {"params": [{"name": "selector", "description": "The selector for elements in the component's content.", "type": "S", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<HarnessLoader[]>", "returnDescription": "A list of `HarnessLoader` for each element matching the given selector.", "generics": [], "name": "getAllChildLoaders", "description": "Gets a list of `HarnessLoader` for each element matching the given selector under the current\nharness's cotnent that searches for harnesses under that element.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The selector for elements in the component's content."}, {"name": "returns", "comment": "A list of `HarnessLoader` for each element matching the given selector."}], "rawComment": "/**\n   * Gets a list of `HarnessLoader` for each element matching the given selector under the current\n   * harness's cotnent that searches for harnesses under that element.\n   * @param selector The selector for elements in the component's content.\n   * @returns A list of `HarnessLoader` for each element matching the given selector.\n   */"}, "entryType": "function", "description": "Gets a list of `HarnessLoader` for each element matching the given selector under the current\nharness's cotnent that searches for harnesses under that element.", "jsdocTags": [{"name": "param", "comment": "The selector for elements in the component's content."}, {"name": "returns", "comment": "A list of `HarnessLoader` for each element matching the given selector."}], "rawComment": "/**\n   * Gets a list of `HarnessLoader` for each element matching the given selector under the current\n   * harness's cotnent that searches for harnesses under that element.\n   * @param selector The selector for elements in the component's content.\n   * @returns A list of `HarnessLoader` for each element matching the given selector.\n   */", "memberType": "method", "memberTags": []}, {"name": "getHarness", "signatures": [{"name": "getHarness", "entryType": "function", "description": "Gets the first matching harness for the given query within the current harness's content.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The first harness matching the given query."}, {"name": "throws", "comment": "If no matching harness is found."}], "params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Gets the first matching harness for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The first harness matching the given query.\n   * @throws If no matching harness is found.\n   */", "returnType": "Promise<T>"}], "implementation": {"params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T>", "returnDescription": "The first harness matching the given query.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "getHarness", "description": "Gets the first matching harness for the given query within the current harness's content.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The first harness matching the given query."}, {"name": "throws", "comment": "If no matching harness is found."}], "rawComment": "/**\n   * Gets the first matching harness for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The first harness matching the given query.\n   * @throws If no matching harness is found.\n   */"}, "entryType": "function", "description": "Gets the first matching harness for the given query within the current harness's content.", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The first harness matching the given query."}, {"name": "throws", "comment": "If no matching harness is found."}], "rawComment": "/**\n   * Gets the first matching harness for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The first harness matching the given query.\n   * @throws If no matching harness is found.\n   */", "memberType": "method", "memberTags": []}, {"name": "getHarnessOrNull", "signatures": [{"name": "getHarnessOrNull", "entryType": "function", "description": "Gets the first matching harness for the given query within the current harness's content.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The first harness matching the given query, or null if none is found."}], "params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Gets the first matching harness for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The first harness matching the given query, or null if none is found.\n   */", "returnType": "Promise<T | null>"}], "implementation": {"params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T | null>", "returnDescription": "The first harness matching the given query, or null if none is found.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "getHarnessOrNull", "description": "Gets the first matching harness for the given query within the current harness's content.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The first harness matching the given query, or null if none is found."}], "rawComment": "/**\n   * Gets the first matching harness for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The first harness matching the given query, or null if none is found.\n   */"}, "entryType": "function", "description": "Gets the first matching harness for the given query within the current harness's content.", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The first harness matching the given query, or null if none is found."}], "rawComment": "/**\n   * Gets the first matching harness for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The first harness matching the given query, or null if none is found.\n   */", "memberType": "method", "memberTags": []}, {"name": "getHarnessAtIndex", "signatures": [{"name": "getHarnessAtIndex", "entryType": "function", "description": "Gets a matching harness for the given query and index within the current harness's content.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "param", "comment": "The zero-indexed offset of the component to find."}, {"name": "returns", "comment": "The first harness matching the given query."}, {"name": "throws", "comment": "If no matching harness is found."}], "params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}, {"name": "index", "description": "The zero-indexed offset of the component to find.", "type": "number", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Gets a matching harness for the given query and index within the current harness's content.\n   * @param query The harness query to search for.\n   * @param index The zero-indexed offset of the component to find.\n   * @returns The first harness matching the given query.\n   * @throws If no matching harness is found.\n   */", "returnType": "Promise<T>"}], "implementation": {"params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}, {"name": "index", "description": "The zero-indexed offset of the component to find.", "type": "number", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T>", "returnDescription": "The first harness matching the given query.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "getHarnessAtIndex", "description": "Gets a matching harness for the given query and index within the current harness's content.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "param", "comment": "The zero-indexed offset of the component to find."}, {"name": "returns", "comment": "The first harness matching the given query."}, {"name": "throws", "comment": "If no matching harness is found."}], "rawComment": "/**\n   * Gets a matching harness for the given query and index within the current harness's content.\n   * @param query The harness query to search for.\n   * @param index The zero-indexed offset of the component to find.\n   * @returns The first harness matching the given query.\n   * @throws If no matching harness is found.\n   */"}, "entryType": "function", "description": "Gets a matching harness for the given query and index within the current harness's content.", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "param", "comment": "The zero-indexed offset of the component to find."}, {"name": "returns", "comment": "The first harness matching the given query."}, {"name": "throws", "comment": "If no matching harness is found."}], "rawComment": "/**\n   * Gets a matching harness for the given query and index within the current harness's content.\n   * @param query The harness query to search for.\n   * @param index The zero-indexed offset of the component to find.\n   * @returns The first harness matching the given query.\n   * @throws If no matching harness is found.\n   */", "memberType": "method", "memberTags": []}, {"name": "get<PERSON>ll<PERSON><PERSON><PERSON><PERSON>", "signatures": [{"name": "get<PERSON>ll<PERSON><PERSON><PERSON><PERSON>", "entryType": "function", "description": "Gets all matching harnesses for the given query within the current harness's content.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The list of harness matching the given query."}], "params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Gets all matching harnesses for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The list of harness matching the given query.\n   */", "returnType": "Promise<T[]>"}], "implementation": {"params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T[]>", "returnDescription": "The list of harness matching the given query.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "get<PERSON>ll<PERSON><PERSON><PERSON><PERSON>", "description": "Gets all matching harnesses for the given query within the current harness's content.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The list of harness matching the given query."}], "rawComment": "/**\n   * Gets all matching harnesses for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The list of harness matching the given query.\n   */"}, "entryType": "function", "description": "Gets all matching harnesses for the given query within the current harness's content.", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The list of harness matching the given query."}], "rawComment": "/**\n   * Gets all matching harnesses for the given query within the current harness's content.\n   * @param query The harness query to search for.\n   * @returns The list of harness matching the given query.\n   */", "memberType": "method", "memberTags": []}, {"name": "countHarnesses", "signatures": [{"name": "countHarnesses", "entryType": "function", "description": "Returns the number of matching harnesses for the given query within the current harness's\ncontent.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The number of matching harnesses for the given query."}], "params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Returns the number of matching harnesses for the given query within the current harness's\n   * content.\n   *\n   * @param query The harness query to search for.\n   * @returns The number of matching harnesses for the given query.\n   */", "returnType": "Promise<number>"}], "implementation": {"params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<number>", "returnDescription": "The number of matching harnesses for the given query.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "countHarnesses", "description": "Returns the number of matching harnesses for the given query within the current harness's\ncontent.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The number of matching harnesses for the given query."}], "rawComment": "/**\n   * Returns the number of matching harnesses for the given query within the current harness's\n   * content.\n   *\n   * @param query The harness query to search for.\n   * @returns The number of matching harnesses for the given query.\n   */"}, "entryType": "function", "description": "Returns the number of matching harnesses for the given query within the current harness's\ncontent.", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "The number of matching harnesses for the given query."}], "rawComment": "/**\n   * Returns the number of matching harnesses for the given query within the current harness's\n   * content.\n   *\n   * @param query The harness query to search for.\n   * @returns The number of matching harnesses for the given query.\n   */", "memberType": "method", "memberTags": []}, {"name": "hasH<PERSON>ness", "signatures": [{"name": "hasH<PERSON>ness", "entryType": "function", "description": "Checks whether there is a matching harnesses for the given query within the current harness's\ncontent.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "Whether there is matching harnesses for the given query."}], "params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Checks whether there is a matching harnesses for the given query within the current harness's\n   * content.\n   *\n   * @param query The harness query to search for.\n   * @returns Whether there is matching harnesses for the given query.\n   */", "returnType": "Promise<boolean>"}], "implementation": {"params": [{"name": "query", "description": "The harness query to search for.", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON>y<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<boolean>", "returnDescription": "Whether there is matching harnesses for the given query.", "generics": [{"name": "T", "constraint": "ComponentHarness"}], "name": "hasH<PERSON>ness", "description": "Checks whether there is a matching harnesses for the given query within the current harness's\ncontent.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "Whether there is matching harnesses for the given query."}], "rawComment": "/**\n   * Checks whether there is a matching harnesses for the given query within the current harness's\n   * content.\n   *\n   * @param query The harness query to search for.\n   * @returns Whether there is matching harnesses for the given query.\n   */"}, "entryType": "function", "description": "Checks whether there is a matching harnesses for the given query within the current harness's\ncontent.", "jsdocTags": [{"name": "param", "comment": "The harness query to search for."}, {"name": "returns", "comment": "Whether there is matching harnesses for the given query."}], "rawComment": "/**\n   * Checks whether there is a matching harnesses for the given query within the current harness's\n   * content.\n   *\n   * @param query The harness query to search for.\n   * @returns Whether there is matching harnesses for the given query.\n   */", "memberType": "method", "memberTags": []}, {"name": "getRootHarnessLoader", "signatures": [{"name": "getRootHarnessLoader", "entryType": "function", "description": "Gets the root harness loader from which to start\nsearching for content contained by this harness.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Gets the root harness loader from which to start\n   * searching for content contained by this harness.\n   */", "returnType": "Promise<HarnessLoader>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<HarnessLoader>", "generics": [], "name": "getRootHarnessLoader", "description": "Gets the root harness loader from which to start\nsearching for content contained by this harness.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Gets the root harness loader from which to start\n   * searching for content contained by this harness.\n   */"}, "entryType": "function", "description": "Gets the root harness loader from which to start\nsearching for content contained by this harness.", "jsdocTags": [], "rawComment": "/**\n   * Gets the root harness loader from which to start\n   * searching for content contained by this harness.\n   */", "memberType": "method", "memberTags": ["protected"]}, {"name": "host", "signatures": [{"name": "host", "entryType": "function", "description": "Gets a `Promise` for the `TestElement` representing the host element of the component.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Gets a `Promise` for the `TestElement` representing the host element of the component. */", "returnType": "Promise<TestElement>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<TestElement>", "generics": [], "name": "host", "description": "Gets a `Promise` for the `TestElement` representing the host element of the component.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets a `Promise` for the `TestElement` representing the host element of the component. */"}, "entryType": "function", "description": "Gets a `Promise` for the `TestElement` representing the host element of the component.", "jsdocTags": [], "rawComment": "/** Gets a `Promise` for the `TestElement` representing the host element of the component. */", "memberType": "method", "memberTags": ["override"]}, {"name": "documentRootLocatorFactory", "signatures": [{"name": "documentRootLocatorFactory", "entryType": "function", "description": "Gets a `LocatorFactory` for the document root element. This factory can be used to create\nlocators for elements that a component creates outside of its own root element. (e.g. by\nappending to document.body).", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Gets a `LocatorFactory` for the document root element. This factory can be used to create\n   * locators for elements that a component creates outside of its own root element. (e.g. by\n   * appending to document.body).\n   */", "returnType": "LocatorFactory"}], "implementation": {"params": [], "isNewType": false, "returnType": "LocatorFactory", "generics": [], "name": "documentRootLocatorFactory", "description": "Gets a `LocatorFactory` for the document root element. This factory can be used to create\nlocators for elements that a component creates outside of its own root element. (e.g. by\nappending to document.body).", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Gets a `LocatorFactory` for the document root element. This factory can be used to create\n   * locators for elements that a component creates outside of its own root element. (e.g. by\n   * appending to document.body).\n   */"}, "entryType": "function", "description": "Gets a `LocatorFactory` for the document root element. This factory can be used to create\nlocators for elements that a component creates outside of its own root element. (e.g. by\nappending to document.body).", "jsdocTags": [], "rawComment": "/**\n   * Gets a `LocatorFactory` for the document root element. This factory can be used to create\n   * locators for elements that a component creates outside of its own root element. (e.g. by\n   * appending to document.body).\n   */", "memberType": "method", "memberTags": ["protected", "override"]}, {"name": "locatorFor", "signatures": [{"name": "locatorFor", "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorFor('span')()            // Throws because the `Promise` rejects\n```", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\neach query."}], "params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */", "returnType": "() => Promise<LocatorFnResult<T>>"}], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T>>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorFor", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorFor('span')()            // Throws because the `Promise` rejects\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\neach query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorFor('span')()            // Throws because the `Promise` rejects\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\neach query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorFor(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorFor('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorFor('span')()            // Throws because the `Promise` rejects\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` rejects. The type that the `Promise` resolves to is a union of all result types for\n   *   each query.\n   */", "memberType": "method", "memberTags": ["protected", "override"]}, {"name": "locatorForOptional", "signatures": [{"name": "locatorForOptional", "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorForOptional('span')()            // Gets `null`\n```", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\nresult types for each query or null."}], "params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */", "returnType": "() => Promise<LocatorFnResult<T> | null>"}], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T> | null>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorForOptional", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorForOptional('span')()            // Gets `null`\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\nresult types for each query or null."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\nor element under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\nawait ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\nawait ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\nawait ch.locatorForOptional('span')()            // Gets `null`\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for the\nfirst element or harness matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If no matches are found, the\n`Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\nresult types for each query or null."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find a `ComponentHarness` instance\n   * or element under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * await ch.locatorForOptional(DivHarness, 'div')() // Gets a `DivHarness` instance for #d1\n   * await ch.locatorForOptional('div', DivHarness)() // Gets a `TestElement` instance for #d1\n   * await ch.locatorForOptional('span')()            // Gets `null`\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for the\n   *   first element or harness matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If no matches are found, the\n   *   `Promise` is resolved with `null`. The type that the `Promise` resolves to is a union of all\n   *   result types for each query or null.\n   */", "memberType": "method", "memberTags": ["protected", "override"]}, {"name": "locatorForAll", "signatures": [{"name": "locatorForAll", "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\nor elements under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n`IdIsD1Harness.hostSelector` is `'#d1'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\n// Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\nawait ch.locatorForAll(DivHarness, 'div')()\n// Gets [TestElement for #d1, TestElement for #d2]\nawait ch.locatorForAll('div', '#d1')()\n// Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\nawait ch.locatorForAll(DivHarness, IdIsD1Harness)()\n// Gets []\nawait ch.locatorForAll('span')()\n```", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for all\nelements and harnesses matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If an element matches more than\none `ComponentHarness` class, the locator gets an instance of each for the same element. If\nan element matches multiple `string` selectors, only one `TestElement` instance is returned\nfor that element. The type that the `Promise` resolves to is an array where each element is\nthe union of all result types for each query."}], "params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await ch.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await ch.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await ch.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await ch.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */", "returnType": "() => Promise<LocatorFnResult<T>[]>"}], "implementation": {"params": [{"name": "queries", "description": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate.", "type": "T", "isOptional": false, "isRestParam": true}], "isNewType": false, "returnType": "() => Promise<LocatorFnResult<T>[]>", "generics": [{"name": "T", "constraint": "(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><any> | string)[]"}], "name": "locatorForAll", "description": "Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\nor elements under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n`IdIsD1Harness.hostSelector` is `'#d1'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\n// Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\nawait ch.locatorForAll(DivHarness, 'div')()\n// Gets [TestElement for #d1, TestElement for #d2]\nawait ch.locatorForAll('div', '#d1')()\n// Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\nawait ch.locatorForAll(DivHarness, IdIsD1Harness)()\n// Gets []\nawait ch.locatorForAll('span')()\n```", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for all\nelements and harnesses matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If an element matches more than\none `ComponentHarness` class, the locator gets an instance of each for the same element. If\nan element matches multiple `string` selectors, only one `TestElement` instance is returned\nfor that element. The type that the `Promise` resolves to is an array where each element is\nthe union of all result types for each query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await ch.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await ch.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await ch.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await ch.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */"}, "entryType": "function", "description": "Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\nor elements under the host element of this `ComponentHarness`.\n\nFor example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n`IdIsD1Harness.hostSelector` is `'#d1'`\n\n```html\n<div id=\"d1\"></div><div id=\"d2\"></div>\n```\n\nthen we expect:\n\n```ts\n// Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\nawait ch.locatorForAll(DivHarness, 'div')()\n// Gets [TestElement for #d1, TestElement for #d2]\nawait ch.locatorForAll('div', '#d1')()\n// Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\nawait ch.locatorForAll(DivHarness, IdIsD1Harness)()\n// Gets []\nawait ch.locatorForAll('span')()\n```", "jsdocTags": [{"name": "param", "comment": "A list of queries specifying which harnesses and elements to search for:\n- A `string` searches for elements matching the CSS selector specified by the string.\n- A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\ngiven class.\n- A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\npredicate."}, {"name": "return", "comment": "An asynchronous locator function that searches for and returns a `Promise` for all\nelements and harnesses matching the given search criteria. Matches are ordered first by\norder in the DOM, and second by order in the queries list. If an element matches more than\none `ComponentHarness` class, the locator gets an instance of each for the same element. If\nan element matches multiple `string` selectors, only one `TestElement` instance is returned\nfor that element. The type that the `Promise` resolves to is an array where each element is\nthe union of all result types for each query."}], "rawComment": "/**\n   * Creates an asynchronous locator function that can be used to find `ComponentHarness` instances\n   * or elements under the host element of this `ComponentHarness`.\n   *\n   * For example, given the following DOM and assuming `DivHarness.hostSelector` is `'div'` and\n   * `IdIsD1Harness.hostSelector` is `'#d1'`\n   *\n   * ```html\n   * <div id=\"d1\"></div><div id=\"d2\"></div>\n   * ```\n   *\n   * then we expect:\n   *\n   * ```ts\n   * // Gets [DivHarness for #d1, TestElement for #d1, DivHarness for #d2, TestElement for #d2]\n   * await ch.locatorForAll(DivHarness, 'div')()\n   * // Gets [TestElement for #d1, TestElement for #d2]\n   * await ch.locatorForAll('div', '#d1')()\n   * // Gets [DivHarness for #d1, IdIsD1Harness for #d1, DivHarness for #d2]\n   * await ch.locatorForAll(DivHarness, IdIsD1Harness)()\n   * // Gets []\n   * await ch.locatorForAll('span')()\n   * ```\n   *\n   * @param queries A list of queries specifying which harnesses and elements to search for:\n   *   - A `string` searches for elements matching the CSS selector specified by the string.\n   *   - A `ComponentHarness` constructor searches for `ComponentHarness` instances matching the\n   *     given class.\n   *   - A `HarnessPredicate` searches for `ComponentHarness` instances matching the given\n   *     predicate.\n   * @return An asynchronous locator function that searches for and returns a `Promise` for all\n   *   elements and harnesses matching the given search criteria. Matches are ordered first by\n   *   order in the DOM, and second by order in the queries list. If an element matches more than\n   *   one `ComponentHarness` class, the locator gets an instance of each for the same element. If\n   *   an element matches multiple `string` selectors, only one `TestElement` instance is returned\n   *   for that element. The type that the `Promise` resolves to is an array where each element is\n   *   the union of all result types for each query.\n   */", "memberType": "method", "memberTags": ["protected", "override"]}, {"name": "forceStabilize", "signatures": [{"name": "forceStabilize", "entryType": "function", "description": "Flushes change detection and async tasks in the Angular zone.\nIn most cases it should not be necessary to call this manually. However, there may be some edge\ncases where it is needed to fully flush animation events.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Flushes change detection and async tasks in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */", "returnType": "Promise<void>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "forceStabilize", "description": "Flushes change detection and async tasks in the Angular zone.\nIn most cases it should not be necessary to call this manually. However, there may be some edge\ncases where it is needed to fully flush animation events.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Flushes change detection and async tasks in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */"}, "entryType": "function", "description": "Flushes change detection and async tasks in the Angular zone.\nIn most cases it should not be necessary to call this manually. However, there may be some edge\ncases where it is needed to fully flush animation events.", "jsdocTags": [], "rawComment": "/**\n   * Flushes change detection and async tasks in the Angular zone.\n   * In most cases it should not be necessary to call this manually. However, there may be some edge\n   * cases where it is needed to fully flush animation events.\n   */", "memberType": "method", "memberTags": ["protected", "override"]}, {"name": "waitForTasksOutsideAngular", "signatures": [{"name": "waitForTasksOutsideAngular", "entryType": "function", "description": "Waits for all scheduled or running async tasks to complete. This allows harness\nauthors to wait for async tasks outside of the Angular zone.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */", "returnType": "Promise<void>"}], "implementation": {"params": [], "isNewType": false, "returnType": "Promise<void>", "generics": [], "name": "waitForTasksOutsideAngular", "description": "Waits for all scheduled or running async tasks to complete. This allows harness\nauthors to wait for async tasks outside of the Angular zone.", "entryType": "function", "jsdocTags": [], "rawComment": "/**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */"}, "entryType": "function", "description": "Waits for all scheduled or running async tasks to complete. This allows harness\nauthors to wait for async tasks outside of the Angular zone.", "jsdocTags": [], "rawComment": "/**\n   * Waits for all scheduled or running async tasks to complete. This allows harness\n   * authors to wait for async tasks outside of the Angular zone.\n   */", "memberType": "method", "memberTags": ["protected", "override"]}], "generics": [{"name": "S", "constraint": "string", "default": "string"}], "description": "Base class for component harnesses that authors should extend if they anticipate that consumers\nof the harness may want to access other harnesses within the `<ng-content>` of the component.", "jsdocTags": [], "rawComment": "/**\n * Base class for component harnesses that authors should extend if they anticipate that consumers\n * of the harness may want to access other harnesses within the `<ng-content>` of the component.\n */", "extends": "ComponentHarness", "implements": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 479, "endLine": 574}}, {"name": "ComponentHarnessConstructor", "isAbstract": false, "entryType": "interface", "members": [{"name": "hostSelector", "type": "string", "memberType": "property", "memberTags": [], "description": "`ComponentHarness` subclasses must specify a static `hostSelector` property that is used to\nfind the host element for the corresponding component. This property should match the selector\nfor the Angular component.", "jsdocTags": []}], "generics": [{"name": "T", "constraint": "ComponentHarness"}], "description": "Constructor for a ComponentHarness subclass. To be a valid ComponentHarnessConstructor, the\nclass must also have a static `hostSelector` property.", "jsdocTags": [], "rawComment": "/**\n * Constructor for a ComponentHarness subclass. To be a valid ComponentHarnessConstructor, the\n * class must also have a static `hostSelector` property.\n */", "implements": [], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 580, "endLine": 589}}, {"name": "BaseHarnessFilters", "isAbstract": false, "entryType": "interface", "members": [{"name": "selector", "type": "string | undefined", "memberType": "property", "memberTags": ["optional"], "description": "Only find instances whose host element matches the given selector.", "jsdocTags": []}, {"name": "ancestor", "type": "string | undefined", "memberType": "property", "memberTags": ["optional"], "description": "Only find instances that are nested under an element with the given selector.", "jsdocTags": []}], "generics": [], "description": "A set of criteria that can be used to filter a list of `ComponentHarness` instances.", "jsdocTags": [], "rawComment": "/** A set of criteria that can be used to filter a list of `ComponentHarness` instances. */", "implements": [], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 592, "endLine": 597}}, {"name": "HarnessPredicate", "isAbstract": false, "entryType": "undecorated_class", "members": [{"name": "constructor", "signatures": [], "implementation": {"params": [{"name": "harnessType", "description": "", "type": "ComponentHarnessConstructor<T>", "isOptional": false, "isRestParam": false}, {"name": "options", "description": "", "type": "BaseHarnessFilters", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "HarnessPredicate<T>", "generics": [], "name": "constructor", "description": "", "entryType": "function", "jsdocTags": [], "rawComment": ""}, "entryType": "function", "description": "", "jsdocTags": [], "rawComment": "", "memberType": "method", "memberTags": []}, {"name": "harnessType", "type": "ComponentHarnessConstructor<T>", "memberType": "property", "memberTags": ["override"], "description": "", "jsdocTags": []}, {"name": "add", "signatures": [{"name": "add", "entryType": "function", "description": "Adds a predicate function to be run against candidate harnesses.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "A description of this predicate that may be used in error messages."}, {"name": "param", "comment": "An async predicate function."}, {"name": "return", "comment": "this (for method chaining)."}], "params": [{"name": "description", "description": "A description of this predicate that may be used in error messages.", "type": "string", "isOptional": false, "isRestParam": false}, {"name": "predicate", "description": "An async predicate function.", "type": "AsyncPredicate<T>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Adds a predicate function to be run against candidate harnesses.\n   * @param description A description of this predicate that may be used in error messages.\n   * @param predicate An async predicate function.\n   * @return this (for method chaining).\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "description", "description": "A description of this predicate that may be used in error messages.", "type": "string", "isOptional": false, "isRestParam": false}, {"name": "predicate", "description": "An async predicate function.", "type": "AsyncPredicate<T>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [], "name": "add", "description": "Adds a predicate function to be run against candidate harnesses.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "A description of this predicate that may be used in error messages."}, {"name": "param", "comment": "An async predicate function."}, {"name": "return", "comment": "this (for method chaining)."}], "rawComment": "/**\n   * Adds a predicate function to be run against candidate harnesses.\n   * @param description A description of this predicate that may be used in error messages.\n   * @param predicate An async predicate function.\n   * @return this (for method chaining).\n   */"}, "entryType": "function", "description": "Adds a predicate function to be run against candidate harnesses.", "jsdocTags": [{"name": "param", "comment": "A description of this predicate that may be used in error messages."}, {"name": "param", "comment": "An async predicate function."}, {"name": "return", "comment": "this (for method chaining)."}], "rawComment": "/**\n   * Adds a predicate function to be run against candidate harnesses.\n   * @param description A description of this predicate that may be used in error messages.\n   * @param predicate An async predicate function.\n   * @return this (for method chaining).\n   */", "memberType": "method", "memberTags": []}, {"name": "addOption", "signatures": [{"name": "addOption", "entryType": "function", "description": "Adds a predicate function that depends on an option value to be run against candidate\nharnesses. If the option value is undefined, the predicate will be ignored.", "generics": [{"name": "O"}], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The name of the option (may be used in error messages)."}, {"name": "param", "comment": "The option value."}, {"name": "param", "comment": "The predicate function to run if the option value is not undefined."}, {"name": "return", "comment": "this (for method chaining)."}], "params": [{"name": "name", "description": "The name of the option (may be used in error messages).", "type": "string", "isOptional": false, "isRestParam": false}, {"name": "option", "description": "The option value.", "type": "O | undefined", "isOptional": false, "isRestParam": false}, {"name": "predicate", "description": "The predicate function to run if the option value is not undefined.", "type": "AsyncOptionPredicate<T, O>", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Adds a predicate function that depends on an option value to be run against candidate\n   * harnesses. If the option value is undefined, the predicate will be ignored.\n   * @param name The name of the option (may be used in error messages).\n   * @param option The option value.\n   * @param predicate The predicate function to run if the option value is not undefined.\n   * @return this (for method chaining).\n   */", "returnType": "this"}], "implementation": {"params": [{"name": "name", "description": "The name of the option (may be used in error messages).", "type": "string", "isOptional": false, "isRestParam": false}, {"name": "option", "description": "The option value.", "type": "O | undefined", "isOptional": false, "isRestParam": false}, {"name": "predicate", "description": "The predicate function to run if the option value is not undefined.", "type": "AsyncOptionPredicate<T, O>", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "this", "generics": [{"name": "O"}], "name": "addOption", "description": "Adds a predicate function that depends on an option value to be run against candidate\nharnesses. If the option value is undefined, the predicate will be ignored.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The name of the option (may be used in error messages)."}, {"name": "param", "comment": "The option value."}, {"name": "param", "comment": "The predicate function to run if the option value is not undefined."}, {"name": "return", "comment": "this (for method chaining)."}], "rawComment": "/**\n   * Adds a predicate function that depends on an option value to be run against candidate\n   * harnesses. If the option value is undefined, the predicate will be ignored.\n   * @param name The name of the option (may be used in error messages).\n   * @param option The option value.\n   * @param predicate The predicate function to run if the option value is not undefined.\n   * @return this (for method chaining).\n   */"}, "entryType": "function", "description": "Adds a predicate function that depends on an option value to be run against candidate\nharnesses. If the option value is undefined, the predicate will be ignored.", "jsdocTags": [{"name": "param", "comment": "The name of the option (may be used in error messages)."}, {"name": "param", "comment": "The option value."}, {"name": "param", "comment": "The predicate function to run if the option value is not undefined."}, {"name": "return", "comment": "this (for method chaining)."}], "rawComment": "/**\n   * Adds a predicate function that depends on an option value to be run against candidate\n   * harnesses. If the option value is undefined, the predicate will be ignored.\n   * @param name The name of the option (may be used in error messages).\n   * @param option The option value.\n   * @param predicate The predicate function to run if the option value is not undefined.\n   * @return this (for method chaining).\n   */", "memberType": "method", "memberTags": []}, {"name": "filter", "signatures": [{"name": "filter", "entryType": "function", "description": "Filters a list of harnesses on this predicate.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The list of harnesses to filter."}, {"name": "return", "comment": "A list of harnesses that satisfy this predicate."}], "params": [{"name": "harnesses", "description": "The list of harnesses to filter.", "type": "T[]", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Filters a list of harnesses on this predicate.\n   * @param harnesses The list of harnesses to filter.\n   * @return A list of harnesses that satisfy this predicate.\n   */", "returnType": "Promise<T[]>"}], "implementation": {"params": [{"name": "harnesses", "description": "The list of harnesses to filter.", "type": "T[]", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<T[]>", "generics": [], "name": "filter", "description": "Filters a list of harnesses on this predicate.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The list of harnesses to filter."}, {"name": "return", "comment": "A list of harnesses that satisfy this predicate."}], "rawComment": "/**\n   * Filters a list of harnesses on this predicate.\n   * @param harnesses The list of harnesses to filter.\n   * @return A list of harnesses that satisfy this predicate.\n   */"}, "entryType": "function", "description": "Filters a list of harnesses on this predicate.", "jsdocTags": [{"name": "param", "comment": "The list of harnesses to filter."}, {"name": "return", "comment": "A list of harnesses that satisfy this predicate."}], "rawComment": "/**\n   * Filters a list of harnesses on this predicate.\n   * @param harnesses The list of harnesses to filter.\n   * @return A list of harnesses that satisfy this predicate.\n   */", "memberType": "method", "memberTags": []}, {"name": "evaluate", "signatures": [{"name": "evaluate", "entryType": "function", "description": "Evaluates whether the given harness satisfies this predicate.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The harness to check"}, {"name": "return", "comment": "A promise that resolves to true if the harness satisfies this predicate,\nand resolves to false otherwise."}], "params": [{"name": "harness", "description": "The harness to check", "type": "T", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Evaluates whether the given harness satisfies this predicate.\n   * @param harness The harness to check\n   * @return A promise that resolves to true if the harness satisfies this predicate,\n   *   and resolves to false otherwise.\n   */", "returnType": "Promise<boolean>"}], "implementation": {"params": [{"name": "harness", "description": "The harness to check", "type": "T", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<boolean>", "generics": [], "name": "evaluate", "description": "Evaluates whether the given harness satisfies this predicate.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The harness to check"}, {"name": "return", "comment": "A promise that resolves to true if the harness satisfies this predicate,\nand resolves to false otherwise."}], "rawComment": "/**\n   * Evaluates whether the given harness satisfies this predicate.\n   * @param harness The harness to check\n   * @return A promise that resolves to true if the harness satisfies this predicate,\n   *   and resolves to false otherwise.\n   */"}, "entryType": "function", "description": "Evaluates whether the given harness satisfies this predicate.", "jsdocTags": [{"name": "param", "comment": "The harness to check"}, {"name": "return", "comment": "A promise that resolves to true if the harness satisfies this predicate,\nand resolves to false otherwise."}], "rawComment": "/**\n   * Evaluates whether the given harness satisfies this predicate.\n   * @param harness The harness to check\n   * @return A promise that resolves to true if the harness satisfies this predicate,\n   *   and resolves to false otherwise.\n   */", "memberType": "method", "memberTags": []}, {"name": "getDescription", "signatures": [{"name": "getDescription", "entryType": "function", "description": "Gets a description of this predicate for use in error messages.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Gets a description of this predicate for use in error messages. */", "returnType": "string"}], "implementation": {"params": [], "isNewType": false, "returnType": "string", "generics": [], "name": "getDescription", "description": "Gets a description of this predicate for use in error messages.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets a description of this predicate for use in error messages. */"}, "entryType": "function", "description": "Gets a description of this predicate for use in error messages.", "jsdocTags": [], "rawComment": "/** Gets a description of this predicate for use in error messages. */", "memberType": "method", "memberTags": []}, {"name": "getSelector", "signatures": [{"name": "getSelector", "entryType": "function", "description": "Gets the selector used to find candidate elements.", "generics": [], "isNewType": false, "jsdocTags": [], "params": [], "rawComment": "/** Gets the selector used to find candidate elements. */", "returnType": "string"}], "implementation": {"params": [], "isNewType": false, "returnType": "string", "generics": [], "name": "getSelector", "description": "Gets the selector used to find candidate elements.", "entryType": "function", "jsdocTags": [], "rawComment": "/** Gets the selector used to find candidate elements. */"}, "entryType": "function", "description": "Gets the selector used to find candidate elements.", "jsdocTags": [], "rawComment": "/** Gets the selector used to find candidate elements. */", "memberType": "method", "memberTags": []}, {"name": "stringMatches", "signatures": [{"name": "stringMatches", "entryType": "function", "description": "Checks if the specified nullable string value matches the given pattern.", "generics": [], "isNewType": false, "jsdocTags": [{"name": "param", "comment": "The nullable string value to check, or a Promise resolving to the\nnullable string value."}, {"name": "param", "comment": "The pattern the value is expected to match. If `pattern` is a string,\n`value` is expected to match exactly. If `pattern` is a regex, a partial match is\nallowed. If `pattern` is `null`, the value is expected to be `null`."}, {"name": "return", "comment": "Whether the value matches the pattern."}], "params": [{"name": "value", "description": "The nullable string value to check, or a Promise resolving to the\nnullable string value.", "type": "string | Promise<string | null> | null", "isOptional": false, "isRestParam": false}, {"name": "pattern", "description": "The pattern the value is expected to match. If `pattern` is a string,\n`value` is expected to match exactly. If `pattern` is a regex, a partial match is\nallowed. If `pattern` is `null`, the value is expected to be `null`.", "type": "string | RegExp | null", "isOptional": false, "isRestParam": false}], "rawComment": "/**\n   * Checks if the specified nullable string value matches the given pattern.\n   * @param value The nullable string value to check, or a Promise resolving to the\n   *   nullable string value.\n   * @param pattern The pattern the value is expected to match. If `pattern` is a string,\n   *   `value` is expected to match exactly. If `pattern` is a regex, a partial match is\n   *   allowed. If `pattern` is `null`, the value is expected to be `null`.\n   * @return Whether the value matches the pattern.\n   */", "returnType": "Promise<boolean>"}], "implementation": {"params": [{"name": "value", "description": "The nullable string value to check, or a Promise resolving to the\nnullable string value.", "type": "string | Promise<string | null> | null", "isOptional": false, "isRestParam": false}, {"name": "pattern", "description": "The pattern the value is expected to match. If `pattern` is a string,\n`value` is expected to match exactly. If `pattern` is a regex, a partial match is\nallowed. If `pattern` is `null`, the value is expected to be `null`.", "type": "string | RegExp | null", "isOptional": false, "isRestParam": false}], "isNewType": false, "returnType": "Promise<boolean>", "generics": [], "name": "stringMatches", "description": "Checks if the specified nullable string value matches the given pattern.", "entryType": "function", "jsdocTags": [{"name": "param", "comment": "The nullable string value to check, or a Promise resolving to the\nnullable string value."}, {"name": "param", "comment": "The pattern the value is expected to match. If `pattern` is a string,\n`value` is expected to match exactly. If `pattern` is a regex, a partial match is\nallowed. If `pattern` is `null`, the value is expected to be `null`."}, {"name": "return", "comment": "Whether the value matches the pattern."}], "rawComment": "/**\n   * Checks if the specified nullable string value matches the given pattern.\n   * @param value The nullable string value to check, or a Promise resolving to the\n   *   nullable string value.\n   * @param pattern The pattern the value is expected to match. If `pattern` is a string,\n   *   `value` is expected to match exactly. If `pattern` is a regex, a partial match is\n   *   allowed. If `pattern` is `null`, the value is expected to be `null`.\n   * @return Whether the value matches the pattern.\n   */"}, "entryType": "function", "description": "Checks if the specified nullable string value matches the given pattern.", "jsdocTags": [{"name": "param", "comment": "The nullable string value to check, or a Promise resolving to the\nnullable string value."}, {"name": "param", "comment": "The pattern the value is expected to match. If `pattern` is a string,\n`value` is expected to match exactly. If `pattern` is a regex, a partial match is\nallowed. If `pattern` is `null`, the value is expected to be `null`."}, {"name": "return", "comment": "Whether the value matches the pattern."}], "rawComment": "/**\n   * Checks if the specified nullable string value matches the given pattern.\n   * @param value The nullable string value to check, or a Promise resolving to the\n   *   nullable string value.\n   * @param pattern The pattern the value is expected to match. If `pattern` is a string,\n   *   `value` is expected to match exactly. If `pattern` is a regex, a partial match is\n   *   allowed. If `pattern` is `null`, the value is expected to be `null`.\n   * @return Whether the value matches the pattern.\n   */", "memberType": "method", "memberTags": ["static"]}], "generics": [{"name": "T", "constraint": "ComponentHarness"}], "description": "A class used to associate a ComponentHarness class with predicate functions that can be used to\nfilter instances of the class to be matched.", "jsdocTags": [], "rawComment": "/**\n * A class used to associate a ComponentHarness class with predicate functions that can be used to\n * filter instances of the class to be matched.\n */", "implements": [], "source": {"filePath": "src/cdk/testing/component-harness.ts", "startLine": 603, "endLine": 731}}], "symbols": [["getNoKeysSpecifiedError", "@angular/cdk/testing"], ["ElementDimensions", "@angular/cdk/testing"], ["AutoChangeDetectionStatus", "@angular/cdk/testing"], ["Modifier<PERSON>eys", "@angular/cdk/testing"], ["AsyncFactoryFn", "@angular/cdk/testing"], ["EventData", "@angular/cdk/testing"], ["AsyncPredicate", "@angular/cdk/testing"], ["<PERSON><PERSON><PERSON>", "@angular/cdk/testing"], ["AsyncOptionPredicate", "@angular/cdk/testing"], ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@angular/cdk/testing"], ["LocatorFnResult", "@angular/cdk/testing"], ["HarnessEnvironment", "@angular/cdk/testing"], ["handleAutoChangeDetectionStatus", "@angular/cdk/testing"], ["TestElement", "@angular/cdk/testing"], ["stopHandlingAutoChangeDetectionStatus", "@angular/cdk/testing"], ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@angular/cdk/testing"], ["manualChangeDetection", "@angular/cdk/testing"], ["parallel", "@angular/cdk/testing"], ["TextOptions", "@angular/cdk/testing"], ["LocatorFactory", "@angular/cdk/testing"], ["ComponentHarness", "@angular/cdk/testing"], ["ContentContainerComponentHarness", "@angular/cdk/testing"], ["ComponentHarnessConstructor", "@angular/cdk/testing"], ["BaseHarnessFilters", "@angular/cdk/testing"], ["HarnessPredicate", "@angular/cdk/testing"], ["getNoKeysSpecifiedError", "@angular/cdk/testing"], ["ElementDimensions", "@angular/cdk/testing"], ["ElementDimensions.top", "@angular/cdk/testing"], ["ElementDimensions.left", "@angular/cdk/testing"], ["ElementDimensions.width", "@angular/cdk/testing"], ["ElementDimensions.height", "@angular/cdk/testing"], ["AutoChangeDetectionStatus", "@angular/cdk/testing"], ["AutoChangeDetectionStatus.isDisabled", "@angular/cdk/testing"], ["AutoChangeDetectionStatus.onDetectChangesNow", "@angular/cdk/testing"], ["Modifier<PERSON>eys", "@angular/cdk/testing"], ["ModifierKeys.control", "@angular/cdk/testing"], ["ModifierKeys.alt", "@angular/cdk/testing"], ["ModifierKeys.shift", "@angular/cdk/testing"], ["ModifierKeys.meta", "@angular/cdk/testing"], ["AsyncFactoryFn", "@angular/cdk/testing"], ["EventData", "@angular/cdk/testing"], ["AsyncPredicate", "@angular/cdk/testing"], ["<PERSON><PERSON><PERSON>", "@angular/cdk/testing"], ["TestKey.BACKSPACE", "@angular/cdk/testing"], ["TestKey.TAB", "@angular/cdk/testing"], ["TestKey.ENTER", "@angular/cdk/testing"], ["TestKey.SHIFT", "@angular/cdk/testing"], ["TestKey.CONTROL", "@angular/cdk/testing"], ["TestKey.ALT", "@angular/cdk/testing"], ["TestKey.ESCAPE", "@angular/cdk/testing"], ["TestKey.PAGE_UP", "@angular/cdk/testing"], ["TestKey.PAGE_DOWN", "@angular/cdk/testing"], ["TestKey.END", "@angular/cdk/testing"], ["TestKey.HOME", "@angular/cdk/testing"], ["TestKey.LEFT_ARROW", "@angular/cdk/testing"], ["TestKey.UP_ARROW", "@angular/cdk/testing"], ["TestKey.RIGHT_ARROW", "@angular/cdk/testing"], ["TestKey.DOWN_ARROW", "@angular/cdk/testing"], ["TestKey.INSERT", "@angular/cdk/testing"], ["TestKey.DELETE", "@angular/cdk/testing"], ["TestKey.F1", "@angular/cdk/testing"], ["TestKey.F2", "@angular/cdk/testing"], ["TestKey.F3", "@angular/cdk/testing"], ["TestKey.F4", "@angular/cdk/testing"], ["TestKey.F5", "@angular/cdk/testing"], ["TestKey.F6", "@angular/cdk/testing"], ["TestKey.F7", "@angular/cdk/testing"], ["TestKey.F8", "@angular/cdk/testing"], ["TestKey.F9", "@angular/cdk/testing"], ["TestKey.F10", "@angular/cdk/testing"], ["TestKey.F11", "@angular/cdk/testing"], ["TestKey.F12", "@angular/cdk/testing"], ["TestKey.META", "@angular/cdk/testing"], ["TestKey.COMMA", "@angular/cdk/testing"], ["AsyncOptionPredicate", "@angular/cdk/testing"], ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@angular/cdk/testing"], ["LocatorFnResult", "@angular/cdk/testing"], ["HarnessEnvironment", "@angular/cdk/testing"], ["HarnessEnvironment.constructor", "@angular/cdk/testing"], ["HarnessEnvironment.rootElement", "@angular/cdk/testing"], ["HarnessEnvironment.rootElement", "@angular/cdk/testing"], ["HarnessEnvironment.documentRootLocatorFactory", "@angular/cdk/testing"], ["HarnessEnvironment.locatorFor", "@angular/cdk/testing"], ["HarnessEnvironment.locatorForOptional", "@angular/cdk/testing"], ["HarnessEnvironment.locatorForAll", "@angular/cdk/testing"], ["HarnessEnvironment.rootHarnessLoader", "@angular/cdk/testing"], ["HarnessEnvironment.harnessLoaderFor", "@angular/cdk/testing"], ["HarnessEnvironment.harnessLoaderForOptional", "@angular/cdk/testing"], ["HarnessEnvironment.harnessLoaderForAll", "@angular/cdk/testing"], ["HarnessEnvironment.getHarness", "@angular/cdk/testing"], ["HarnessEnvironment.getHarnessOrNull", "@angular/cdk/testing"], ["HarnessEnvironment.getHarnessAtIndex", "@angular/cdk/testing"], ["HarnessEnvironment.getAllHarnesses", "@angular/cdk/testing"], ["HarnessEnvironment.countHarnesses", "@angular/cdk/testing"], ["HarnessEnvironment.hasHarness", "@angular/cdk/testing"], ["HarnessEnvironment.getChildLoader", "@angular/cdk/testing"], ["HarnessEnvironment.getAllChildLoaders", "@angular/cdk/testing"], ["HarnessEnvironment.createComponentHarness", "@angular/cdk/testing"], ["HarnessEnvironment.forceStabilize", "@angular/cdk/testing"], ["HarnessEnvironment.waitForTasksOutsideAngular", "@angular/cdk/testing"], ["HarnessEnvironment.getDocumentRoot", "@angular/cdk/testing"], ["HarnessEnvironment.createTestElement", "@angular/cdk/testing"], ["HarnessEnvironment.createEnvironment", "@angular/cdk/testing"], ["HarnessEnvironment.getAllRawElements", "@angular/cdk/testing"], ["handleAutoChangeDetectionStatus", "@angular/cdk/testing"], ["TestElement", "@angular/cdk/testing"], ["TestElement.blur", "@angular/cdk/testing"], ["TestElement.clear", "@angular/cdk/testing"], ["TestElement.click", "@angular/cdk/testing"], ["TestElement.click", "@angular/cdk/testing"], ["TestElement.click", "@angular/cdk/testing"], ["TestElement.rightClick", "@angular/cdk/testing"], ["TestElement.focus", "@angular/cdk/testing"], ["TestElement.getCssValue", "@angular/cdk/testing"], ["TestElement.hover", "@angular/cdk/testing"], ["TestElement.mouseAway", "@angular/cdk/testing"], ["TestElement.send<PERSON>eys", "@angular/cdk/testing"], ["TestElement.send<PERSON>eys", "@angular/cdk/testing"], ["TestElement.text", "@angular/cdk/testing"], ["TestElement.setContenteditableValue", "@angular/cdk/testing"], ["TestElement.getAttribute", "@angular/cdk/testing"], ["TestElement.hasClass", "@angular/cdk/testing"], ["TestElement.getDimensions", "@angular/cdk/testing"], ["TestElement.getProperty", "@angular/cdk/testing"], ["TestElement.matchesSelector", "@angular/cdk/testing"], ["TestElement.isFocused", "@angular/cdk/testing"], ["TestElement.setInputValue", "@angular/cdk/testing"], ["TestElement.selectOptions", "@angular/cdk/testing"], ["TestElement.dispatchEvent", "@angular/cdk/testing"], ["stopHandlingAutoChangeDetectionStatus", "@angular/cdk/testing"], ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "@angular/cdk/testing"], ["HarnessLoader.getChildLoader", "@angular/cdk/testing"], ["HarnessLoader.getAllChildLoaders", "@angular/cdk/testing"], ["HarnessLoader.getHarness", "@angular/cdk/testing"], ["HarnessLoader.getHarnessOrNull", "@angular/cdk/testing"], ["HarnessLoader.getHarnessAtIndex", "@angular/cdk/testing"], ["HarnessLoader.getAllHarnesses", "@angular/cdk/testing"], ["HarnessLoader.countHarnesses", "@angular/cdk/testing"], ["HarnessLoader.hasHarness", "@angular/cdk/testing"], ["manualChangeDetection", "@angular/cdk/testing"], ["parallel", "@angular/cdk/testing"], ["TextOptions", "@angular/cdk/testing"], ["TextOptions.exclude", "@angular/cdk/testing"], ["LocatorFactory", "@angular/cdk/testing"], ["LocatorFactory.documentRootLocatorFactory", "@angular/cdk/testing"], ["LocatorFactory.rootElement", "@angular/cdk/testing"], ["LocatorFactory.locatorFor", "@angular/cdk/testing"], ["LocatorFactory.locatorForOptional", "@angular/cdk/testing"], ["LocatorFactory.locatorForAll", "@angular/cdk/testing"], ["LocatorFactory.rootHarnessLoader", "@angular/cdk/testing"], ["LocatorFactory.harnessLoaderFor", "@angular/cdk/testing"], ["LocatorFactory.harnessLoaderForOptional", "@angular/cdk/testing"], ["LocatorFactory.harnessLoaderForAll", "@angular/cdk/testing"], ["LocatorFactory.forceStabilize", "@angular/cdk/testing"], ["LocatorFactory.waitForTasksOutsideAngular", "@angular/cdk/testing"], ["ComponentHarness", "@angular/cdk/testing"], ["ComponentHarness.constructor", "@angular/cdk/testing"], ["ComponentHarness.host", "@angular/cdk/testing"], ["ComponentHarness.documentRootLocatorFactory", "@angular/cdk/testing"], ["ComponentHarness.locatorFor", "@angular/cdk/testing"], ["ComponentHarness.locatorForOptional", "@angular/cdk/testing"], ["ComponentHarness.locatorForAll", "@angular/cdk/testing"], ["ComponentHarness.forceStabilize", "@angular/cdk/testing"], ["ComponentHarness.waitForTasksOutsideAngular", "@angular/cdk/testing"], ["ContentContainerComponentHarness", "@angular/cdk/testing"], ["ContentContainerComponentHarness.getChildLoader", "@angular/cdk/testing"], ["ContentContainerComponentHarness.getAllChildLoaders", "@angular/cdk/testing"], ["ContentContainerComponentHarness.getHarness", "@angular/cdk/testing"], ["ContentContainerComponentHarness.getHarnessOrNull", "@angular/cdk/testing"], ["ContentContainerComponentHarness.getHarnessAtIndex", "@angular/cdk/testing"], ["ContentContainerComponentHarness.getAllHarnesses", "@angular/cdk/testing"], ["ContentContainerComponentHarness.countHarnesses", "@angular/cdk/testing"], ["ContentContainerComponentHarness.hasHarness", "@angular/cdk/testing"], ["ContentContainerComponentHarness.getRootHarnessLoader", "@angular/cdk/testing"], ["ContentContainerComponentHarness.host", "@angular/cdk/testing"], ["ContentContainerComponentHarness.documentRootLocatorFactory", "@angular/cdk/testing"], ["ContentContainerComponentHarness.locatorFor", "@angular/cdk/testing"], ["ContentContainerComponentHarness.locatorForOptional", "@angular/cdk/testing"], ["ContentContainerComponentHarness.locatorForAll", "@angular/cdk/testing"], ["ContentContainerComponentHarness.forceStabilize", "@angular/cdk/testing"], ["ContentContainerComponentHarness.waitForTasksOutsideAngular", "@angular/cdk/testing"], ["ComponentHarnessConstructor", "@angular/cdk/testing"], ["ComponentHarnessConstructor.hostSelector", "@angular/cdk/testing"], ["BaseHarnessFilters", "@angular/cdk/testing"], ["BaseHarnessFilters.selector", "@angular/cdk/testing"], ["BaseHarnessFilters.ancestor", "@angular/cdk/testing"], ["HarnessPredicate", "@angular/cdk/testing"], ["HarnessPredicate.constructor", "@angular/cdk/testing"], ["HarnessPredicate.harnessType", "@angular/cdk/testing"], ["HarnessPredicate.add", "@angular/cdk/testing"], ["HarnessPredicate.addOption", "@angular/cdk/testing"], ["HarnessPredicate.filter", "@angular/cdk/testing"], ["HarnessPredicate.evaluate", "@angular/cdk/testing"], ["HarnessPredicate.getDescription", "@angular/cdk/testing"], ["HarnessPredicate.getSelector", "@angular/cdk/testing"], ["HarnessPredicate.stringMatches", "@angular/cdk/testing"]]}