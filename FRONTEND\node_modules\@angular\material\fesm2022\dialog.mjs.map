{"version": 3, "file": "dialog.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/dialog/dialog-animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * Default parameters for the animation for backwards compatibility.\n * @docs-private\n * @deprecated Will stop being exported.\n * @breaking-change 21.0.0\n */\nexport const _defaultParams = {\n  params: {enterAnimationDuration: '150ms', exitAnimationDuration: '75ms'},\n};\n\n/**\n * Animations used by MatDialog.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const matDialogAnimations: {\n  readonly dialogContainer: any;\n} = {\n  // Represents:\n  // trigger('dialogContainer', [\n  //   // Note: The `enter` animation transitions to `transform: none`, because for some reason\n  //   // specifying the transform explicitly, causes IE both to blur the dialog content and\n  //   // decimate the animation performance. Leaving it as `none` solves both issues.\n  //   state('void, exit', style({opacity: 0, transform: 'scale(0.7)'})),\n  //   state('enter', style({transform: 'none'})),\n  //   transition(\n  //     '* => enter',\n  //     group([\n  //       animate(\n  //         '{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)',\n  //         style({transform: 'none', opacity: 1}),\n  //       ),\n  //       query('@*', animateChild(), {optional: true}),\n  //     ]),\n  //     _defaultParams,\n  //   ),\n  //   transition(\n  //     '* => void, * => exit',\n  //     group([\n  //       animate('{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)', style({opacity: 0})),\n  //       query('@*', animateChild(), {optional: true}),\n  //     ]),\n  //     _defaultParams,\n  //   ),\n  // ])\n\n  /** Animation that is applied on the dialog container by default. */\n  dialogContainer: {\n    type: 7,\n    name: 'dialogContainer',\n    definitions: [\n      {\n        type: 0,\n        name: 'void, exit',\n        styles: {type: 6, styles: {opacity: 0, transform: 'scale(0.7)'}, offset: null},\n      },\n      {\n        type: 0,\n        name: 'enter',\n        styles: {type: 6, styles: {transform: 'none'}, offset: null},\n      },\n      {\n        type: 1,\n        expr: '* => enter',\n        animation: {\n          type: 3,\n          steps: [\n            {\n              type: 4,\n              styles: {type: 6, styles: {transform: 'none', opacity: 1}, offset: null},\n              timings: '{{enterAnimationDuration}} cubic-bezier(0, 0, 0.2, 1)',\n            },\n            {\n              type: 11,\n              selector: '@*',\n              animation: {type: 9, options: null},\n              options: {optional: true},\n            },\n          ],\n          options: null,\n        },\n        options: {params: {enterAnimationDuration: '150ms', exitAnimationDuration: '75ms'}},\n      },\n      {\n        type: 1,\n        expr: '* => void, * => exit',\n        animation: {\n          type: 3,\n          steps: [\n            {\n              type: 4,\n              styles: {type: 6, styles: {opacity: 0}, offset: null},\n              timings: '{{exitAnimationDuration}} cubic-bezier(0.4, 0.0, 0.2, 1)',\n            },\n            {\n              type: 11,\n              selector: '@*',\n              animation: {type: 9, options: null},\n              options: {optional: true},\n            },\n          ],\n          options: null,\n        },\n        options: {params: {enterAnimationDuration: '150ms', exitAnimationDuration: '75ms'}},\n      },\n    ],\n    options: {},\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAQA;;;;;AAKG;AACU,MAAA,cAAc,GAAG;IAC5B,MAAM,EAAE,EAAC,sBAAsB,EAAE,OAAO,EAAE,qBAAqB,EAAE,MAAM,EAAC;;AAG1E;;;;;AAKG;AACU,MAAA,mBAAmB,GAE5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BF,IAAA,eAAe,EAAE;AACf,QAAA,IAAI,EAAE,CAAC;AACP,QAAA,IAAI,EAAE,iBAAiB;AACvB,QAAA,WAAW,EAAE;AACX,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,YAAY;gBAClB,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAE,SAAS,EAAE,YAAY,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC/E,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,OAAO;AACb,gBAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,MAAM,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AAC7D,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,YAAY;AAClB,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,KAAK,EAAE;AACL,wBAAA;AACE,4BAAA,IAAI,EAAE,CAAC;4BACP,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACxE,4BAAA,OAAO,EAAE,uDAAuD;AACjE,yBAAA;AACD,wBAAA;AACE,4BAAA,IAAI,EAAE,EAAE;AACR,4BAAA,QAAQ,EAAE,IAAI;4BACd,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAC;AACnC,4BAAA,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC;AAC1B,yBAAA;AACF,qBAAA;AACD,oBAAA,OAAO,EAAE,IAAI;AACd,iBAAA;AACD,gBAAA,OAAO,EAAE,EAAC,MAAM,EAAE,EAAC,sBAAsB,EAAE,OAAO,EAAE,qBAAqB,EAAE,MAAM,EAAC,EAAC;AACpF,aAAA;AACD,YAAA;AACE,gBAAA,IAAI,EAAE,CAAC;AACP,gBAAA,IAAI,EAAE,sBAAsB;AAC5B,gBAAA,SAAS,EAAE;AACT,oBAAA,IAAI,EAAE,CAAC;AACP,oBAAA,KAAK,EAAE;AACL,wBAAA;AACE,4BAAA,IAAI,EAAE,CAAC;AACP,4BAAA,MAAM,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAE,CAAC,EAAC,EAAE,MAAM,EAAE,IAAI,EAAC;AACrD,4BAAA,OAAO,EAAE,0DAA0D;AACpE,yBAAA;AACD,wBAAA;AACE,4BAAA,IAAI,EAAE,EAAE;AACR,4BAAA,QAAQ,EAAE,IAAI;4BACd,SAAS,EAAE,EAAC,IAAI,EAAE,CAAC,EAAE,OAAO,EAAE,IAAI,EAAC;AACnC,4BAAA,OAAO,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC;AAC1B,yBAAA;AACF,qBAAA;AACD,oBAAA,OAAO,EAAE,IAAI;AACd,iBAAA;AACD,gBAAA,OAAO,EAAE,EAAC,MAAM,EAAE,EAAC,sBAAsB,EAAE,OAAO,EAAE,qBAAqB,EAAE,MAAM,EAAC,EAAC;AACpF,aAAA;AACF,SAAA;AACD,QAAA,OAAO,EAAE,EAAE;AACZ,KAAA;;;;;"}